# Wen - 富文本编辑协作平台

一个基于 Vue 3 和 Spring Boot 的现代化富文本编辑和内容管理平台，提供实时协作功能和全面的无障碍访问支持。

## 项目结构

```
├── web/                    # Vue 3 前端应用
├── Wen/                    # Java Spring Boot 后端服务
├── interactive-feedback-mcp/  # MCP 交互反馈服务器
├── .kiro/                  # Kiro AI 助手配置
└── *.bat                   # Windows 服务启动脚本
```

## 核心功能

### 🎯 富文本编辑
- **TipTap 编辑器**: 基于 ProseMirror 的强大富文本编辑器
- **拖拽重排**: 直观的段落拖拽重新排序功能
- **上下文菜单**: 智能的内容转换和操作菜单
- **实时协作**: WebSocket 支持的多人协作编辑

### ♿ 无障碍访问
- **键盘导航**: 完整的键盘快捷键支持
- **屏幕阅读器**: 全面的 ARIA 标签和语音提示
- **焦点管理**: 智能的焦点处理和导航
- **用户偏好**: 支持减少动画和高对比度模式

### 🔧 技术特性
- **现代架构**: Vue 3 + Composition API + TypeScript
- **微服务后端**: Spring Boot 模块化架构
- **实时通信**: STOMP.js WebSocket 连接
- **状态管理**: Pinia 全局状态管理
- **UI 组件**: Naive UI 组件库

## 快速开始

### 前端开发

```bash
cd web
npm install
npm run dev
```

### 后端服务

```bash
# 启动核心服务
start-wen-core.bat

# 启动网关服务
start-wen-gateway.bat

# 启动认证服务
start-wen-authentication.bat
```

## 键盘快捷键

### 拖拽操作
- **Alt + ↑/↓**: 移动内容上下
- **Alt + Shift + ↑/↓**: 移动到顶部/底部
- **Alt + 1-6**: 转换为对应级别标题
- **Alt + h/p/l/c/q**: 转换内容类型
- **Alt + d**: 复制内容
- **Alt + Delete**: 删除内容
- **Ctrl + Space**: 打开上下文菜单

### 菜单导航
- **↑/↓**: 导航菜单项
- **Enter/Space**: 执行选中项
- **Escape**: 关闭菜单
- **1-6**: 快速转换为标题
- **p/c/q/l**: 快速内容转换

## 技术栈

### 前端 (web/)
- **框架**: Vue 3 + Composition API
- **构建工具**: Vite 6.0+ + TypeScript
- **UI 库**: Naive UI
- **富文本编辑器**: TipTap + 自定义扩展
- **状态管理**: Pinia
- **路由**: Vue Router 4
- **样式**: SCSS + CSS 模块
- **实时通信**: STOMP.js
- **测试**: Vitest + Playwright

### 后端 (Wen/)
- **框架**: Spring Boot
- **架构**: 微服务模块化
- **服务发现**: Nacos
- **文件存储**: MinIO
- **数据库**: MySQL

## 开发工具

- **代码检查**: ESLint + TypeScript + Vue 插件
- **代码格式化**: Prettier
- **样式检查**: Stylelint
- **Git 钩子**: Husky + lint-staged
- **提交规范**: Conventional commits + commitlint

## 文档

- [前端开发指南](web/README.md)
- [无障碍访问指南](web/docs/accessibility.md)
- [TipTap 扩展文档](web/docs/tiptap-extensions.md)
- [代码规范](web/docs/code-standards.md)

## 开发命令

### 前端开发
```bash
npm run dev          # 开发服务器
npm run build        # 生产构建
npm run type-check   # 类型检查
npm run code-check   # 代码质量检查
npm run test:unit    # 单元测试
npm run lint         # 代码检查
npm run format       # 代码格式化
```

### 测试
```bash
npm run test:unit              # 单元测试
npm run test:e2e              # 端到端测试
npm run test:accessibility    # 无障碍测试
npm run test:keyboard         # 键盘导航测试
```

## 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'feat: add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 支持

如果您遇到问题或有建议，请：

1. 查看[文档](web/docs/)
2. 搜索[已知问题](../../issues)
3. 创建新的 [Issue](../../issues/new)

## 更新日志

### v1.0.0 (最新)
- ✨ 完整的拖拽重排功能
- ✨ 智能上下文菜单系统
- ✨ 全面的键盘无障碍支持
- ✨ 屏幕阅读器完整支持
- ✨ 增强的键盘快捷键系统
- 🐛 修复拖拽操作的边界情况
- 🎨 改进视觉反馈和动画效果
- 📚 完善文档和使用指南