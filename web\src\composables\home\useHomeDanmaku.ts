import { ref } from 'vue'

import type { CommentDanmakuRef } from '@/types/component-refs.types'

export function useHomeDanmaku() {
  // 弹幕控制状态
  const danmakuLoop = ref(false)
  const danmakuPause = ref(false)

  // 处理弹幕暂停状态变化
  const handleDanmakuPauseChange = (
    newVal: boolean,
    commentDanmakuRef: { value: CommentDanmakuRef },
  ) => {
    if (commentDanmakuRef.value) {
      if (newVal) {
        commentDanmakuRef.value.pause()
      } else {
        commentDanmakuRef.value.play()
      }
    }
  }

  // 重置弹幕状态
  const resetDanmakuState = () => {
    danmakuLoop.value = false
    danmakuPause.value = false
  }

  // 处理弹幕视图的订阅和取消订阅
  const handleDanmakuSubscription = (
    commentDanmakuRef: { value: CommentDanmakuRef },
    subscribe: boolean,
  ) => {
    if (commentDanmakuRef.value) {
      if (subscribe) {
        commentDanmakuRef.value.subscribeComment()
      } else {
        commentDanmakuRef.value.unsubscribeComment()
        commentDanmakuRef.value.clearDanmaku()
      }
    }
  }

  // 处理窗口大小调整时的弹幕重置
  const handleDanmakuResize = (
    commentDanmakuRef: { value: CommentDanmakuRef },
    isCardVisible: boolean,
  ) => {
    if (!isCardVisible && commentDanmakuRef.value) {
      commentDanmakuRef.value.resize()
    }
  }

  return {
    // 状态
    danmakuLoop,
    danmakuPause,

    // 方法
    handleDanmakuPauseChange,
    resetDanmakuState,
    handleDanmakuSubscription,
    handleDanmakuResize,
  }
}
