package com.shenmo.wen.common.messagesynchronizer;

import com.shenmo.wen.common.messagesynchronizer.redis.AbstractRedisMessageListener;
import com.shenmo.wen.common.messagesynchronizer.redis.AppRedisMessageListener;
import com.shenmo.wen.common.util.ThreadLocalUtils;
import com.shenmo.wen.common.util.ThrowUtils;
import com.shenmo.wen.common.util.spring.SpringIocUtils;
import org.springframework.core.annotation.AnnotationUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.lang.NonNull;

import java.lang.reflect.Method;
import java.util.Objects;
import java.util.Optional;

/**
 * 消息同步监听器
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class MessageSynchronizerListener extends AbstractRedisMessageListener<MessageData<MessageSynchronizerMethod>> {

    /**
     * 消息同步器方法tl
     */
    private static final ThreadLocal<MessageSynchronizerMethod> MESSAGE_SYNCHRONIZER_DESCRIPTION_TL = ThreadLocalUtils.empty();


    /**
     * 构造方法
     *
     * @param redisTemplate redis操作模板
     * <AUTHOR>
     */
    public MessageSynchronizerListener(@NonNull RedisTemplate<String, MessageData<MessageSynchronizerMethod>> redisTemplate) {
        super(redisTemplate, AppRedisMessageListener.processChannel("public_advice"));
    }

    /**
     * 获取消息同步器方法
     *
     * @return 消息同步器方法
     * <AUTHOR>
     */
    public static MessageSynchronizerMethod getMessagePublisherInfo() {
        return MESSAGE_SYNCHRONIZER_DESCRIPTION_TL.get();
    }

    @Override
    protected void onMessage(@NonNull MessageData<MessageSynchronizerMethod> messageData) {
        final MessageSynchronizerMethod messageSynchronizerMethod = messageData.getType();
        final Object[] data = (Object[]) messageData.getData();
        final Class<?> cls = messageSynchronizerMethod.getSource();
        final String methodName = messageSynchronizerMethod.getMethodName();
        final Class<?>[] parameterTypes = messageSynchronizerMethod.getParameterTypes();
        final Optional<?> beanOp = SpringIocUtils.getBeanOp(cls);
        if (beanOp.isPresent()) {
            final Object bean = beanOp.get();
            try {
                MESSAGE_SYNCHRONIZER_DESCRIPTION_TL.set(messageSynchronizerMethod);
                final Class<?> beanClass = bean.getClass();
                final Method declaredMethod = beanClass.getDeclaredMethod(methodName, parameterTypes);
                declaredMethod.setAccessible(true);
                if (Objects.nonNull(AnnotationUtils.findAnnotation(declaredMethod, MessageSynchronizer.class))) {
                    declaredMethod.invoke(bean, data);
                }
            } catch (Exception e) {
                throw ThrowUtils.getThrow().internalServerError(String.format("Message listener invoke fail, %s", messageSynchronizerMethod), e);
            } finally {
                MESSAGE_SYNCHRONIZER_DESCRIPTION_TL.remove();
            }
        }
    }
}
