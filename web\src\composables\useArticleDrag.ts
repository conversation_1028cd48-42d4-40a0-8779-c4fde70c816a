import { ref, onUnmounted, computed } from 'vue'

import type { Article } from '@/types/article.types'
import { createDragClone, cleanupDragElement } from '@/utils/drag-clone'
import { setupDragEvents } from '@/utils/drag-events'

interface DragOptions {
  onDragStart?: (article: Article) => void
  onDragEnd?: () => void
  onDelete?: (article: Article) => void
  onReorder?: (draggedId: string, targetId: string, position: 'before' | 'after') => void
}

export function useArticleDrag(options: DragOptions = {}) {
  // 拖拽状态
  const isDragging = ref(false)
  const draggedArticle = ref<Article | null>(null)
  const showTrashBin = ref(false)
  const isOverTrashBin = ref(false)
  const dragOverCardId = ref<string | null>(null)
  const dragOverPosition = ref<'before' | 'after' | null>(null)
  const isSingleCardRow = ref(false)

  // 拖拽元素的位置
  const dragPosition = ref({ x: 0, y: 0 })

  // 克隆的拖拽元素
  let clonedElement: HTMLElement | null = null

  // 长按定时器
  let longPressTimer: ReturnType<typeof setTimeout> | null = null
  const longPressDelay = 500

  // 触摸移动阈值（像素）
  const moveThreshold = 10
  let startPosition = { x: 0, y: 0 }
  let isLongPressActive = false

  // 事件处理器引用
  let eventHandlers: (() => void) | null = null

  // 防抖标志
  let isEnding = false

  // 清理函数
  const cleanup = () => {
    if (longPressTimer) {
      clearTimeout(longPressTimer)
      longPressTimer = null
    }
    isLongPressActive = false
  }

  // 开始长按
  const startLongPress = (
    event: MouseEvent | TouchEvent,
    article: Article,
    avatarElement: HTMLElement,
  ) => {
    console.log('开始长按检测:', article.title)
    event.preventDefault()
    event.stopPropagation()

    cleanup()

    // 记录初始位置
    const { clientX, clientY } = getEventCoordinates(event)
    startPosition = { x: clientX, y: clientY }
    isLongPressActive = true

    // 添加临时的移动监听器来检测是否超过阈值
    const tempMoveHandler = (moveEvent: MouseEvent | TouchEvent) => {
      if (!isLongPressActive) return

      const { clientX: currentX, clientY: currentY } = getEventCoordinates(moveEvent)
      const distance = Math.sqrt(
        Math.pow(currentX - startPosition.x, 2) + Math.pow(currentY - startPosition.y, 2),
      )

      // 如果移动距离超过阈值，取消长按
      if (distance > moveThreshold) {
        console.log('移动距离超过阈值，取消长按')
        cleanup()
        removeTempListeners()
      }
    }

    const tempEndHandler = () => {
      cleanup()
      removeTempListeners()
    }

    const removeTempListeners = () => {
      isLongPressActive = false
      document.removeEventListener('mousemove', tempMoveHandler)
      document.removeEventListener('touchmove', tempMoveHandler)
      document.removeEventListener('mouseup', tempEndHandler)
      document.removeEventListener('touchend', tempEndHandler)
      document.removeEventListener('touchcancel', tempEndHandler)
    }

    // 添加临时监听器
    document.addEventListener('mousemove', tempMoveHandler, { passive: false })
    document.addEventListener('touchmove', tempMoveHandler, { passive: false })
    document.addEventListener('mouseup', tempEndHandler)
    document.addEventListener('touchend', tempEndHandler)
    document.addEventListener('touchcancel', tempEndHandler)

    longPressTimer = setTimeout(() => {
      if (isLongPressActive) {
        console.log('长按触发，开始拖拽:', article.title)
        removeTempListeners()
        startDragging(event, article, avatarElement)
      }
    }, longPressDelay)
  }

  // 开始拖拽
  const startDragging = (
    event: MouseEvent | TouchEvent,
    article: Article,
    avatarElement: HTMLElement,
  ) => {
    // 清理长按状态，进入拖拽状态
    isLongPressActive = false
    isDragging.value = true
    draggedArticle.value = article

    // 只有文章拥有者才显示垃圾篓
    if (article.isOwner) {
      showTrashBin.value = true
      console.log('显示垃圾篓 - 文章拥有者')
    }

    // 获取初始位置
    const { clientX, clientY } = getEventCoordinates(event)
    dragPosition.value = { x: clientX, y: clientY }

    // 创建克隆元素
    const cardElement = avatarElement.closest('.card-item') as HTMLElement
    if (cardElement) {
      const rect = cardElement.getBoundingClientRect()
      clonedElement = createDragClone(cardElement, rect, dragPosition.value)
    }

    // 设置事件监听器
    eventHandlers = setupDragEvents({
      onMove: handleDragMove,
      onEnd: handleDragEnd,
    })

    options.onDragStart?.(article)
  }

  // 获取事件坐标
  const getEventCoordinates = (event: MouseEvent | TouchEvent) => {
    if (event instanceof MouseEvent) {
      return { clientX: event.clientX, clientY: event.clientY }
    } else if (event instanceof TouchEvent) {
      // 优先使用 touches，如果为空则使用 changedTouches（适用于 touchend 事件）
      const touch = event.touches.length > 0 ? event.touches[0] : event.changedTouches[0]
      if (touch) {
        return { clientX: touch.clientX, clientY: touch.clientY }
      }
    }
    return { clientX: 0, clientY: 0 }
  }

  // 处理拖拽移动
  const handleDragMove = (event: MouseEvent | TouchEvent) => {
    if (!isDragging.value) return

    event.preventDefault()
    const { clientX, clientY } = getEventCoordinates(event)
    dragPosition.value = { x: clientX, y: clientY }

    // 更新克隆元素位置
    if (clonedElement) {
      clonedElement.style.left = `${clientX}px`
      clonedElement.style.top = `${clientY}px`
    }

    // 节流检查
    throttledCheck()
  }

  // 节流检查函数
  let lastCheckTime = 0
  const throttledCheck = () => {
    const now = Date.now()
    if (now - lastCheckTime > 50) {
      lastCheckTime = now

      // 检查垃圾篓和卡片悬停
      if (draggedArticle.value?.isOwner) {
        checkOverTrashBin()
      }
      checkOverCard()
    }
  }

  // 检查是否在垃圾篓上方
  const checkOverTrashBin = () => {
    const trashBin = document.querySelector('.trash-bin')
    if (!trashBin) return

    const rect = trashBin.getBoundingClientRect()
    const { x, y } = dragPosition.value

    isOverTrashBin.value = x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom
  }

  // 检查是否在其他卡片上方
  const checkOverCard = () => {
    const cards = document.querySelectorAll('.card-item:not(.dragging)')
    dragOverCardId.value = null
    dragOverPosition.value = null
    isSingleCardRow.value = false

    cards.forEach((card) => {
      const rect = card.getBoundingClientRect()
      const { x, y } = dragPosition.value

      if (x >= rect.left && x <= rect.right && y >= rect.top && y <= rect.bottom) {
        const cardElement = card as HTMLElement
        const articleId = cardElement.dataset.articleId

        if (articleId && articleId !== draggedArticle.value?.id) {
          dragOverCardId.value = articleId

          // 检查是否为单卡片行
          const isOnlyCard = checkIfOnlyCardInRow(card as HTMLElement)
          isSingleCardRow.value = isOnlyCard

          console.log('拖拽悬停检测:', {
            articleId,
            isOnlyCard,
            containerWidth: document.querySelector('.article-container')?.getBoundingClientRect()
              .width,
          })

          // 确定插入位置
          if (isOnlyCard) {
            dragOverPosition.value = y < rect.top + rect.height / 2 ? 'before' : 'after'
            console.log('垂直插入位置:', dragOverPosition.value)
          } else {
            dragOverPosition.value = x < rect.left + rect.width / 2 ? 'before' : 'after'
            console.log('水平插入位置:', dragOverPosition.value)
          }
        }
      }
    })
  }

  // 检查是否为行中唯一卡片
  const checkIfOnlyCardInRow = (targetCard: HTMLElement): boolean => {
    const allCards = document.querySelectorAll('.card-item:not(.dragging)')
    const targetRect = targetCard.getBoundingClientRect()

    // 检查容器宽度，如果容器宽度较小，很可能是单卡片行
    const container = document.querySelector('.article-container')
    const containerWidth = container ? container.getBoundingClientRect().width : window.innerWidth

    // 如果容器宽度小于等于768px，认为是窄屏，应该使用垂直指示器
    if (containerWidth <= 768) {
      return true
    }

    // 否则检查同一行中的卡片数量
    let cardsInSameRow = 0
    allCards.forEach((card) => {
      const cardRect = card.getBoundingClientRect()
      // 检查垂直重叠（同一行）
      const verticalOverlap = !(
        cardRect.bottom <= targetRect.top || cardRect.top >= targetRect.bottom
      )

      if (verticalOverlap) {
        cardsInSameRow++
      }
    })

    return cardsInSameRow === 1
  }

  // 处理拖拽结束
  const handleDragEnd = () => {
    if (!isDragging.value || isEnding) return

    isEnding = true
    console.log('拖拽结束')

    // 清理事件监听器
    if (eventHandlers) {
      eventHandlers()
      eventHandlers = null
    }

    // 处理拖拽结果
    if (isOverTrashBin.value && draggedArticle.value?.isOwner) {
      console.log('触发删除操作')
      options.onDelete?.(draggedArticle.value)
    } else if (dragOverCardId.value && dragOverPosition.value && draggedArticle.value) {
      console.log('触发重新排序操作')
      options.onReorder?.(draggedArticle.value.id, dragOverCardId.value, dragOverPosition.value)
    }

    // 清理克隆元素
    if (clonedElement) {
      cleanupDragElement(clonedElement)
      clonedElement = null
    }

    // 重置状态
    isDragging.value = false
    draggedArticle.value = null
    showTrashBin.value = false
    isOverTrashBin.value = false
    dragOverCardId.value = null
    dragOverPosition.value = null

    options.onDragEnd?.()

    // 延迟重置防抖标志
    setTimeout(() => {
      isEnding = false
    }, 100)
  }

  // 取消长按
  const cancelLongPress = () => {
    console.log('取消长按')

    // 只在长按检测阶段取消，拖拽状态由全局事件处理
    if (isLongPressActive && !isDragging.value) {
      console.log('取消长按检测')
      cleanup()
    }
  }

  // 强制重置所有状态
  const forceReset = () => {
    console.log('强制重置拖拽状态')

    // 清理定时器
    cleanup()

    // 清理事件监听器
    if (eventHandlers) {
      eventHandlers()
      eventHandlers = null
    }

    // 清理克隆元素
    if (clonedElement) {
      cleanupDragElement(clonedElement)
      clonedElement = null
    }

    // 重置所有状态
    isDragging.value = false
    draggedArticle.value = null
    showTrashBin.value = false
    isOverTrashBin.value = false
    dragOverCardId.value = null
    dragOverPosition.value = null

    // 重置防抖标志
    isEnding = false
  }

  // 计算拖拽时的样式
  const dragStyle = computed(() => {
    return {}
  })

  onUnmounted(() => {
    cleanup()
    if (clonedElement) {
      cleanupDragElement(clonedElement)
    }
    if (eventHandlers) {
      eventHandlers()
    }
  })

  return {
    isDragging,
    draggedArticle,
    showTrashBin,
    isOverTrashBin,
    dragPosition,
    dragStyle,
    dragOverCardId,
    dragOverPosition,
    isSingleCardRow,
    isLongPressActive: computed(() => isLongPressActive),
    startLongPress,
    cancelLongPress,
    forceReset,
  }
}
