package com.shenmo.wen.app.core.interaction.controller;

import com.shenmo.wen.app.core.interaction.pojo.entity.WenInteraction;
import com.shenmo.wen.app.core.interaction.pojo.param.WenInteractionParam;
import com.shenmo.wen.app.core.interaction.pojo.vo.WenInteractionCount;
import com.shenmo.wen.app.core.interaction.service.WenInteractionService;
import com.shenmo.wen.common.pojo.response.ResponseData;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("interaction")
@RequiredArgsConstructor
public class WenInteractionController {
    private final WenInteractionService service;

    @GetMapping
    public ResponseData<WenInteraction> load() {
        return ResponseData.success();
    }

    @PostMapping
    public ResponseData<WenInteractionCount> save(@RequestBody WenInteractionParam param) {
        return ResponseData.success(service.save(param));
    }
}
