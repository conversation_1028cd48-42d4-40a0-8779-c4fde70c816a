package com.shenmo.wen.common.util.mybatis;

import com.baomidou.mybatisplus.core.toolkit.support.LambdaMeta;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.google.common.cache.Cache;
import com.google.common.cache.CacheBuilder;
import org.apache.ibatis.reflection.property.PropertyNamer;

import java.util.concurrent.ExecutionException;

/**
 * lambda工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public abstract class LambdaUtils {
    static Cache<SFunction<?, ?>, String> CACHE = CacheBuilder.newBuilder()
            .recordStats()
            .maximumWeight(5000)
            .weigher((k, v) -> String.valueOf(v).length())
            .build();

    public static <T> String getColumnCache(SFunction<T, ?> column) {
        try {
            return CACHE.get(column, () -> {
                final LambdaMeta meta = com.baomidou.mybatisplus.core.toolkit.LambdaUtils.extract(column);
                final Class<?> instantiatedClass = meta.getInstantiatedClass();
                final String fieldName = PropertyNamer.methodToProperty(meta.getImplMethodName());
                return com.baomidou.mybatisplus.core.toolkit.LambdaUtils.getColumnMap(instantiatedClass).get(com.baomidou.mybatisplus.core.toolkit.LambdaUtils.formatKey(fieldName)).getColumn();
            });
        } catch (ExecutionException e) {
            throw new RuntimeException(e);
        }
    }
}
