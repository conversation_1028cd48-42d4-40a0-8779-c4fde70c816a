package com.shenmo.wen.app.core.file.exception;

import com.shenmo.wen.common.exception.enumeration.ExceptionEnum;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnumOption;
import com.shenmo.wen.common.exception.enumeration.ExceptionType;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@ExceptionType(type = FileException.class, module = FileExceptionEnum.MODULE)
public enum FileExceptionEnum implements ExceptionEnum {


    BUCKET_NOT_ALLOW_ACCESS(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "别搞，这个资源你访问不了！")),
    UPLOAD_FAIL(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "文件上传出了点问题...")),
    ;

    public static final String MODULE = "004";

    /**
     * 异常枚举选项
     */
    private final ExceptionEnumOption exceptionEnumOption;

    FileExceptionEnum(ExceptionEnumOption exceptionEnumOption) {

        this.exceptionEnumOption = exceptionEnumOption;
    }

    @Override
    public ExceptionEnumOption getOption() {

        return exceptionEnumOption;
    }
}
