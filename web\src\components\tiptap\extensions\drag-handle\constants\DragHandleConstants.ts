/**
 * Constants for drag handle functionality
 */
export const DRAG_HANDLE_CONSTANTS = {
  SELECTORS: {
    DRAG_HANDLE: '.drag-handle',
    DROP_ZONE_INDICATOR: '.drop-zone-indicator',
    DRAGGING_CLASS: 'dragging',
    KEYBOARD_FOCUSED_CLASS: 'keyboard-focused'
  },
  
  POSITIONING: {
    DEFAULT_LEFT_OFFSET: -20,
    LIST_ITEM_LEFT_OFFSET: -24,
    BLOCKQUOTE_LEFT_OFFSET: -24,
    DEFAULT_TOP_OFFSET: 2,
    HEADING_TOP_OFFSET: 0,
    CODE_BLOCK_TOP_OFFSET: 4,
    Z_INDEX: 10,
    INDICATOR_Z_INDEX: 100
  },
  
  STYLING: {
    GHOST_IMAGE_OPACITY: 0.5,
    GHOST_IMAGE_ROTATION: '5deg',
    GHOST_IMAGE_MAX_WIDTH: '300px',
    GHOST_IMAGE_SHADOW: '0 8px 32px rgba(0, 0, 0, 0.2)',
    GHOST_IMAGE_BORDER_RADIUS: '8px',
    INDICATOR_BORDER_RADIUS: '2px',
    TRANSITION_DURATION: '0.2s'
  },
  
  COLORS: {
    PRIMARY: 'var(--color-primary, #3b82f6)',
    ERROR: 'var(--color-error, #ef4444)',
    SUCCESS: 'var(--color-success, #10b981)'
  },
  
  INDICATOR_HEIGHTS: {
    paragraph: 4,
    heading: 6,
    codeBlock: 4,
    blockquote: 4,
    bulletList: 3,
    orderedList: 3,
    listItem: 4
  },
  
  PERFORMANCE: {
    THROTTLE_DELAY: 16, // 60fps
    DEBOUNCE_DELAY: 300,
    CLEANUP_INTERVAL: 30000, // 30 seconds
    VIRTUAL_SCROLL_THRESHOLD: 100,
    MAX_CACHED_ITEMS: 1000
  },
  
  ACCESSIBILITY: {
    ARIA_LABELS: {
      DRAG_HANDLE: '拖拽手柄',
      DROP_ZONE: '放置区域',
      DRAGGING: '正在拖拽',
      DROP_SUCCESS: '放置成功',
      DROP_FAILED: '放置失败'
    },
    
    KEYBOARD_SHORTCUTS: {
      'Alt-ArrowUp': 'moveParagraphUp',
      'Alt-ArrowDown': 'moveParagraphDown',
      'Alt-1': 'convertToHeading1',
      'Alt-2': 'convertToHeading2',
      'Alt-3': 'convertToHeading3',
      'Alt-4': 'convertToHeading4',
      'Alt-5': 'convertToHeading5',
      'Alt-6': 'convertToHeading6',
      'Alt-d': 'duplicateNode',
      'Alt-Delete': 'deleteNode',
      'Alt-m': 'showClickMenu',
      'Escape': 'cancelDrag',
      'Enter': 'activateDragHandle',
      ' ': 'activateDragHandle',
      'F1': 'showKeyboardHelp',
      '?': 'showKeyboardHelp'
    }
  }
} as const

export const NODE_TYPE_CONFIGS = {
  paragraph: {
    selector: 'p',
    canDragInto: ['paragraph', 'heading', 'blockquote'],
    canReceiveFrom: ['paragraph', 'heading'],
    preserveAttributes: ['class', 'id'],
    nestingRules: {
      maxDepth: 5,
      allowedParents: ['doc', 'blockquote', 'listItem'],
      allowedChildren: []
    }
  },
  
  heading: {
    selector: 'h1, h2, h3, h4, h5, h6',
    canDragInto: ['paragraph', 'heading', 'blockquote'],
    canReceiveFrom: ['paragraph', 'heading'],
    preserveAttributes: ['class', 'id', 'level'],
    nestingRules: {
      maxDepth: 3,
      allowedParents: ['doc', 'blockquote'],
      allowedChildren: []
    }
  },
  
  listItem: {
    selector: 'li',
    canDragInto: ['listItem', 'bulletList', 'orderedList'],
    canReceiveFrom: ['paragraph', 'heading', 'listItem'],
    preserveAttributes: ['class', 'id'],
    nestingRules: {
      maxDepth: 10,
      allowedParents: ['bulletList', 'orderedList'],
      allowedChildren: ['paragraph', 'bulletList', 'orderedList']
    }
  },
  
  codeBlock: {
    selector: '.code-block, pre',
    canDragInto: ['paragraph', 'codeBlock'],
    canReceiveFrom: ['codeBlock'],
    preserveAttributes: ['class', 'id', 'language'],
    nestingRules: {
      maxDepth: 2,
      allowedParents: ['doc', 'blockquote'],
      allowedChildren: []
    }
  },
  
  blockquote: {
    selector: 'blockquote',
    canDragInto: ['paragraph', 'blockquote'],
    canReceiveFrom: ['paragraph', 'heading', 'blockquote'],
    preserveAttributes: ['class', 'id'],
    nestingRules: {
      maxDepth: 3,
      allowedParents: ['doc', 'blockquote'],
      allowedChildren: ['paragraph', 'heading', 'bulletList', 'orderedList']
    }
  }
} as const