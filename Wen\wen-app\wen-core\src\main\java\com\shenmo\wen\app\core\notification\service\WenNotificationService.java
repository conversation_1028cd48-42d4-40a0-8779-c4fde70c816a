package com.shenmo.wen.app.core.notification.service;

import com.shenmo.wen.app.core.notification.pojo.param.WenNotificationLoadParam;
import com.shenmo.wen.app.core.notification.pojo.vo.WenNotificationVo;
import com.shenmo.wen.common.pojo.response.PageResult;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface WenNotificationService {
    PageResult<WenNotificationVo> load(WenNotificationLoadParam param);

    void read(Long id);

    void unread(Long id);

    /**
     * 标记所有通知为已读
     * 
     * @param userId 用户ID
     */
    void readAll(Long userId);

    Long totalUnread();
}
