<template>
  <span
    class="mention"
    :data-type="'mention'"
    :data-id="node.attrs.id"
    :data-label="node.attrs.label"
    :data-avatar="node.attrs.avatar || ''"
    contenteditable="false"
  >
    <span class="mention-name">@{{ node.attrs.label }}</span>
    <img
      v-if="node.attrs.avatar"
      :src="avatarUrl"
      :alt="node.attrs.label"
      class="mention-avatar"
      loading="lazy"
    />
  </span>
</template>

<script setup lang="ts">
import { computed } from 'vue'

import fileApi from '@/api/file'

import type { NodeViewProps } from '@tiptap/core'
import type { Node } from '@tiptap/pm/model'

interface Props extends Omit<NodeViewProps, 'node'> {
  node: Node & {
    attrs: {
      id: string
      label: string
      avatar?: string
    }
  }
}

const props = defineProps<Props>()

/**
 * 计算头像 URL
 */
const avatarUrl = computed(() => {
  if (!props.node.attrs.avatar) return ''
  return fileApi.getResourceURL(props.node.attrs.avatar)
})

// 实现必要的 NodeViewProps 方法
const updateAttributes = (attrs: Record<string, any>) => {
  props.updateAttributes(attrs)
}

const deleteNode = () => {
  props.deleteNode()
}
</script>

<style scoped>
.mention {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  padding: 2px 6px;
  background-color: var(--color-primary-light);
  color: var(--color-primary);
  border-radius: 12px;
  font-size: 14px;
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.mention:hover {
  background-color: var(--color-primary-hover);
}

.mention-name {
  font-weight: 500;
}

.mention-avatar {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  object-fit: cover;
  flex-shrink: 0;
}
</style>
