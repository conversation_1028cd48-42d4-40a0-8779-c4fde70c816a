<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>点击菜单和拖拽测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e1e5e9;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
            color: #2c3e50;
        }
        
        .test-description {
            margin-bottom: 15px;
            color: #666;
        }
        
        .status {
            padding: 8px 12px;
            border-radius: 4px;
            font-weight: 500;
            margin: 10px 0;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .test-steps {
            background: white;
            padding: 15px;
            border-radius: 4px;
            margin: 10px 0;
        }
        
        .test-steps ol {
            margin: 0;
            padding-left: 20px;
        }
        
        .test-steps li {
            margin: 8px 0;
        }
        
        .editor-container {
            border: 1px solid #ddd;
            border-radius: 4px;
            min-height: 200px;
            padding: 15px;
            background: white;
            margin: 15px 0;
        }
        
        .click-menu {
            background: white;
            border: 1px solid #ddd;
            border-radius: 6px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 4px;
            display: flex;
            flex-direction: column;
            gap: 2px;
        }
        
        .click-menu__button {
            width: 24px;
            height: 24px;
            border: none;
            background: none;
            cursor: pointer;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #666;
            transition: background-color 0.2s ease;
        }
        
        .click-menu__button:hover {
            background: #f0f0f0;
            color: #333;
        }
        
        .click-menu__context {
            min-width: 150px;
        }
        
        .click-menu__context button {
            font-size: 13px;
            white-space: nowrap;
            display: block;
            width: 100%;
            padding: 4px 8px;
            border: none;
            background: none;
            cursor: pointer;
            text-align: left;
        }
        
        .click-menu__context button:hover {
            background: #f0f0f0;
        }
    </style>
</head>
<body>
    <h1>点击菜单和拖拽功能测试</h1>
    
    <div class="test-section">
        <div class="test-title">🎯 测试目标</div>
        <div class="test-description">
            验证TipTap编辑器中的点击菜单和段落拖拽功能是否正常工作。
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">📋 测试步骤</div>
        <div class="test-steps">
            <h4>点击菜单测试：</h4>
            <ol>
                <li>在编辑器中输入一些文本段落</li>
                <li>将鼠标悬停在段落左侧</li>
                <li>应该看到一个包含"+"和拖拽图标的菜单</li>
                <li>点击"+"按钮应该在当前段落后插入新段落</li>
                <li>点击拖拽按钮应该显示上下文菜单</li>
            </ol>
            
            <h4>拖拽功能测试：</h4>
            <ol>
                <li>创建多个段落</li>
                <li>使用拖拽按钮或直接拖拽段落</li>
                <li>段落应该能够重新排序</li>
                <li>拖拽过程中应该有视觉反馈</li>
            </ol>
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">🔧 当前状态</div>
        <div id="status-container">
            <div class="status info">
                <strong>状态：</strong> 正在检查扩展加载情况...
            </div>
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">📝 编辑器测试区域</div>
        <div class="test-description">
            在下面的编辑器中测试点击菜单和拖拽功能：
        </div>
        <div class="editor-container" id="editor">
            <p>这是第一个段落。将鼠标悬停在左侧查看点击菜单。</p>
            <p>这是第二个段落。尝试拖拽重新排序。</p>
            <p>这是第三个段落。测试各种功能。</p>
        </div>
    </div>
    
    <div class="test-section">
        <div class="test-title">🐛 故障排除</div>
        <div class="test-description">
            如果功能不工作，请检查：
        </div>
        <div class="test-steps">
            <ul>
                <li>浏览器控制台是否有错误信息</li>
                <li>开发服务器是否正常运行</li>
                <li>扩展是否正确加载</li>
                <li>CSS样式是否正确应用</li>
            </ul>
        </div>
    </div>
    
    <script>
        // 检查开发服务器状态
        function checkServerStatus() {
            const statusContainer = document.getElementById('status-container');
            
            fetch('http://localhost:5174/')
                .then(response => {
                    if (response.ok) {
                        statusContainer.innerHTML = `
                            <div class="status success">
                                <strong>✅ 开发服务器：</strong> 正常运行 (端口 5174)
                            </div>
                            <div class="status info">
                                <strong>📍 访问地址：</strong> <a href="http://localhost:5174" target="_blank">http://localhost:5174</a>
                            </div>
                        `;
                    } else {
                        throw new Error('Server responded with error');
                    }
                })
                .catch(error => {
                    statusContainer.innerHTML = `
                        <div class="status error">
                            <strong>❌ 开发服务器：</strong> 无法连接 (${error.message})
                        </div>
                        <div class="status info">
                            <strong>💡 解决方案：</strong> 请确保在 web 目录下运行 <code>npm run dev</code>
                        </div>
                    `;
                });
        }
        
        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', checkServerStatus);
        
        // 每30秒检查一次服务器状态
        setInterval(checkServerStatus, 30000);
    </script>
</body>
</html>
