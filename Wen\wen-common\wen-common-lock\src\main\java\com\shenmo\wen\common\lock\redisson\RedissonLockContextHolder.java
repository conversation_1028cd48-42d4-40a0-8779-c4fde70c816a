package com.shenmo.wen.common.lock.redisson;

import com.shenmo.wen.common.util.ThreadLocalUtils;
import org.redisson.api.RLock;

/**
 * redisson锁的线程上下文处理
 *
 * <AUTHOR>
 * @version 1.0.0
 * @see ThreadLocal
 */
public class RedissonLockContextHolder {

    /**
     * {@link ThreadLocal}
     */
    private static final ThreadLocal<RLock> LOCK_TL = ThreadLocalUtils.empty();

    /**
     * {@link ThreadLocal#set(Object)}
     *
     * @param lock {@link RLock}
     * <AUTHOR>
     */
    public static void set(RLock lock) {

        LOCK_TL.set(lock);
    }

    /**
     * {@link ThreadLocal#get()}
     *
     * @return {@link RLock}
     * <AUTHOR>
     */
    public static RLock get() {

        return LOCK_TL.get();
    }

    /**
     * {@link ThreadLocal#remove()}
     *
     * <AUTHOR>
     */
    public static void release() {

        ThreadLocalUtils.release(LOCK_TL);
    }
}
