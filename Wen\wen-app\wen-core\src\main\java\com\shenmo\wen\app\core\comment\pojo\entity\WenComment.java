package com.shenmo.wen.app.core.comment.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shenmo.wen.common.mybatis.handler.TimestampToLongTypeHandler;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@TableName(value = "wen_comment", autoResultMap = true)
public class WenComment {
    /**
     * 评论ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 评论内容，以文本形式存储，不能为空。
     */
    private String content;

    /**
     * 目标文章ID，用于关联对应的文章，表明该评论所属的文章，可为空（比如在一些特殊情况下可能暂未关联文章）。
     */
    private Long articleId;

    /**
     * 父评论ID，如果该评论是回复其他评论的，则记录被回复评论的ID，可为空，表示是直接对文章的评论，而非回复其他评论。
     */
    private Long parentCommentId;

    /**
     * 评论发布人ID
     */
    private Long userId;

    /**
     * 评论发布时的IP地址
     */
    private String ip;

    /**
     * 评论发布时的IP归属地
     */
    private String ipLocation;

    /**
     * 提及人ID
     */
    private String mentionUserId;

    /**
     * 提及人用户名
     */
    private String mentionUsername;

    /**
     * 点赞数，记录该评论获得的点赞数量，默认值为0
     */
    private Integer likeCount;

    /**
     * 点踩数，记录该评论获得的点踩数量，默认值为0
     */
    private Integer dislikeCount;

    /**
     * 收藏数，记录该评论的收藏数量，默认值为0
     */
    private Integer favoriteCount;

    /**
     * 回复数，记录该评论的回复数量，默认值为0
     */
    private Integer replyCount;

    /**
     * 评论时间，记录评论发布的时间戳，默认取当前时间，在插入评论时自动赋值。
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long publishedAt;

    /**
     * 创建时间，记录评论首次创建的时间戳，默认取当前时间，在插入评论时自动赋值。
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long ctTm;

    /**
     * 修改时间，记录评论最后一次被修改的时间戳，每次评论更新时会自动更新为当前时间。
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long mdTm;
}
