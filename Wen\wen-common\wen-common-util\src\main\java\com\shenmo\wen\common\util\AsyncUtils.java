package com.shenmo.wen.common.util;

import com.shenmo.wen.common.exception.BaseException;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnumOption;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.http.HttpStatus;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.Executor;
import java.util.function.BiConsumer;
import java.util.function.Consumer;
import java.util.function.Supplier;

/**
 * 异步工具
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public abstract class AsyncUtils {

    /**
     * 默认线程池
     */
    public static final Executor EXECUTOR_SERVICE = ExecutorServiceUtils.createThreadPool(AsyncUtils.class);

    /**
     * 异步执行
     *
     * @param asyncLogic 异步逻辑
     * <AUTHOR>
     */
    public static void asyncExecutor(Runnable asyncLogic) {

        asyncExecutor(asyncLogic, EXECUTOR_SERVICE);
    }

    /**
     * 异步执行
     *
     * @param asyncLogic             异步逻辑
     * @param executor               执行器
     * @param asyncConsumerCallbacks 异步消费回调
     * @param <C>                    异步消费回调泛型
     * <AUTHOR>
     */
    @SafeVarargs
    public static <C> void asyncExecutor(Runnable asyncLogic, Executor executor, AsyncConsumerCallback<C>... asyncConsumerCallbacks) {

        // 确定所有的异步消费回调
        final List<AsyncConsumerCallback<C>> determineAsyncConsumerCallbackList = determineAsyncConsumerCallback(asyncConsumerCallbacks);
        // 执行并整理出所有的异步供给
        final List<C> provideList = provideList(determineAsyncConsumerCallbackList);
        executor.execute(() -> {
            try {
                // 执行所有消费 before
                executeConsumerBefore(determineAsyncConsumerCallbackList, provideList);
                asyncLogic.run();
            } finally {
                // 执行所有消费 after
                executeConsumerAfter(determineAsyncConsumerCallbackList, provideList);
            }
        });
    }

    /**
     * 异步执行
     *
     * @param asyncCount 异步次数
     * @param asyncLogic 异步逻辑
     * <AUTHOR>
     */
    public static void asyncExecutor(int asyncCount, Runnable asyncLogic) {

        asyncExecutor(asyncCount, asyncLogic, EXECUTOR_SERVICE);
    }

    /**
     * 异步执行
     *
     * @param asyncCount 异步次数
     * @param asyncLogic 异步逻辑
     * @param executor   执行器
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    public static void asyncExecutor(int asyncCount, Runnable asyncLogic, Executor executor) {

        asyncExecutor(asyncCount, asyncLogic, executor, new AsyncConsumerCallback[0]);
    }


    /**
     * 异步执行
     *
     * @param asyncCount             异步次数
     * @param asyncLogic             异步逻辑
     * @param executor               执行器
     * @param asyncConsumerCallbacks 异步消费回调
     * @param <C>                    异步消费回调泛型
     * <AUTHOR>
     */
    @SafeVarargs
    public static <C> void asyncExecutor(int asyncCount, Runnable asyncLogic, Executor executor, AsyncConsumerCallback<C>... asyncConsumerCallbacks) {

        final CountDownLatch countDownLatch = new CountDownLatch(asyncCount);
        // 确定所有的异步消费回调
        final List<AsyncConsumerCallback<C>> determineAsyncConsumerCallbackList = determineAsyncConsumerCallback(asyncConsumerCallbacks);
        // 移除不支持countDownLatch的异步消费回调
        determineAsyncConsumerCallbackList.removeIf(AsyncConsumerCallback::excludeCountDownLatch);
        // 执行并整理出所有的异步供给
        final List<C> provideList = provideList(determineAsyncConsumerCallbackList);
        for (int i = 0; i < asyncCount; i++) {
            // 异步执行
            executor.execute(() -> {

                try {
                    // 执行所有消费 before
                    executeConsumerBefore(determineAsyncConsumerCallbackList, provideList);
                    asyncLogic.run();
                } finally {

                    countDownLatch.countDown();
                    // 执行所有消费 after
                    executeConsumerAfter(determineAsyncConsumerCallbackList, provideList);
                }
            });
        }
        try {
            // 等待
            countDownLatch.await();
        } catch (InterruptedException e) {
            throw new BaseException(ExceptionEnumOption.of(HttpStatus.INTERNAL_SERVER_ERROR, "Async executor interrupted"));
        }
    }

    /**
     * 异步执行
     *
     * @param asyncCount 异步次数
     * @param asyncLogic 异步逻辑
     * <AUTHOR>
     */
    public static void asyncExecutor(int asyncCount, Consumer<Integer> asyncLogic) {

        asyncExecutor(asyncCount, asyncLogic, EXECUTOR_SERVICE);
    }

    /**
     * 异步执行
     *
     * @param asyncCount 异步次数
     * @param asyncLogic 异步逻辑
     * @param executor   执行器
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    public static void asyncExecutor(int asyncCount, Consumer<Integer> asyncLogic, Executor executor) {

        asyncExecutor(asyncCount, asyncLogic, executor, new AsyncConsumerCallback[0]);
    }

    /**
     * 异步执行
     *
     * @param asyncCount             异步次数
     * @param asyncLogic             异步逻辑
     * @param executor               执行器
     * @param asyncConsumerCallbacks 异步消费回调
     * @param <C>                    异步消费回调泛型
     * <AUTHOR>
     */
    @SafeVarargs
    public static <C> void asyncExecutor(int asyncCount, Consumer<Integer> asyncLogic, Executor executor, AsyncConsumerCallback<C>... asyncConsumerCallbacks) {

        final CountDownLatch countDownLatch = new CountDownLatch(asyncCount);
        // 确定所有的异步消费回调
        final List<AsyncConsumerCallback<C>> determineAsyncConsumerCallbackList = determineAsyncConsumerCallback(asyncConsumerCallbacks);
        // 移除不支持countDownLatch的异步消费回调
        determineAsyncConsumerCallbackList.removeIf(AsyncConsumerCallback::excludeCountDownLatch);
        // 执行并整理出所有的异步供给
        final List<C> provideList = provideList(determineAsyncConsumerCallbackList);
        for (int i = 0; i < asyncCount; i++) {
            // 异步执行
            int finalI = i;
            executor.execute(() -> {

                try {
                    // 执行所有消费 before
                    executeConsumerBefore(determineAsyncConsumerCallbackList, provideList);
                    asyncLogic.accept(finalI);
                } finally {

                    countDownLatch.countDown();
                    // 执行所有消费 after
                    executeConsumerAfter(determineAsyncConsumerCallbackList, provideList);
                }
            });
        }

        try {
            // 等待
            countDownLatch.await();
        } catch (InterruptedException e) {
            throw new BaseException(ExceptionEnumOption.of(HttpStatus.INTERNAL_SERVER_ERROR, "Async executor interrupted"));
        }
    }

    /**
     * 异步执行
     *
     * @param dataList   数据集
     * @param asyncLogic 异步逻辑
     * @param <P>        逻辑函数泛型
     * <AUTHOR>
     */
    public static <P> void asyncExecutor(List<P> dataList, Consumer<P> asyncLogic) {

        asyncExecutor(dataList, asyncLogic, EXECUTOR_SERVICE);
    }

    /**
     * 异步执行
     *
     * @param dataList   数据集
     * @param asyncLogic 异步逻辑
     * @param executor   执行器
     * @param <P>        逻辑函数泛型
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    public static <P> void asyncExecutor(List<P> dataList, Consumer<P> asyncLogic, Executor executor) {

        asyncExecutor(dataList, asyncLogic, executor, new AsyncConsumerCallback[0]);
    }

    /**
     * 异步执行
     *
     * @param dataList               数据集
     * @param asyncLogic             异步逻辑
     * @param executor               执行器
     * @param asyncConsumerCallbacks 异步消费回调
     * @param <P>                    逻辑函数泛型
     * @param <C>                    异步消费回调泛型
     * <AUTHOR>
     */
    @SafeVarargs
    public static <P, C> void asyncExecutor(List<P> dataList, Consumer<P> asyncLogic, Executor executor, AsyncConsumerCallback<C>... asyncConsumerCallbacks) {

        final CountDownLatch countDownLatch = new CountDownLatch(dataList.size());
        // 确定所有的异步消费回调
        final List<AsyncConsumerCallback<C>> determineAsyncConsumerCallbackList = determineAsyncConsumerCallback(asyncConsumerCallbacks);
        // 移除不支持countDownLatch的异步消费回调
        determineAsyncConsumerCallbackList.removeIf(AsyncConsumerCallback::excludeCountDownLatch);
        // 执行并整理出所有的异步供给
        final List<C> provideList = provideList(determineAsyncConsumerCallbackList);
        for (int i = 0; i < dataList.size(); i++) {
            // 异步执行
            int finalI = i;
            executor.execute(() -> {

                try {
                    // 执行所有消费 before
                    executeConsumerBefore(determineAsyncConsumerCallbackList, provideList);
                    asyncLogic.accept(dataList.get(finalI));
                } finally {

                    countDownLatch.countDown();
                    // 执行所有消费 after
                    executeConsumerAfter(determineAsyncConsumerCallbackList, provideList);
                }
            });
        }
        try {
            // 等待
            countDownLatch.await();
        } catch (InterruptedException e) {
            throw new BaseException(ExceptionEnumOption.of(HttpStatus.INTERNAL_SERVER_ERROR, "Async executor interrupted"));
        }
    }

    /**
     * 异步执行
     *
     * @param dataList   数据集
     * @param asyncLogic 异步逻辑
     * @param <P>        逻辑函数泛型
     * <AUTHOR>
     */
    public static <P> void asyncExecutor(List<P> dataList, BiConsumer<P, Integer> asyncLogic) {

        asyncExecutor(dataList, asyncLogic, EXECUTOR_SERVICE);
    }

    /**
     * 异步执行
     *
     * @param dataList   数据集
     * @param asyncLogic 异步逻辑
     * @param executor   执行器
     * @param <P>        逻辑函数泛型
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    public static <P> void asyncExecutor(List<P> dataList, BiConsumer<P, Integer> asyncLogic, Executor executor) {

        asyncExecutor(dataList, asyncLogic, executor, new AsyncConsumerCallback[0]);
    }

    /**
     * 异步执行
     *
     * @param dataList               数据集
     * @param asyncLogic             异步逻辑
     * @param executor               执行器
     * @param asyncConsumerCallbacks 异步消费回调
     * @param <P>                    逻辑函数泛型
     * @param <C>                    异步消费回调泛型
     * <AUTHOR>
     */
    @SafeVarargs
    public static <P, C> void asyncExecutor(List<P> dataList, BiConsumer<P, Integer> asyncLogic, Executor executor, AsyncConsumerCallback<C>... asyncConsumerCallbacks) {

        final CountDownLatch countDownLatch = new CountDownLatch(dataList.size());
        // 确定所有的异步消费回调
        final List<AsyncConsumerCallback<C>> determineAsyncConsumerCallbackList = determineAsyncConsumerCallback(asyncConsumerCallbacks);
        // 移除不支持countDownLatch的异步消费回调
        determineAsyncConsumerCallbackList.removeIf(AsyncConsumerCallback::excludeCountDownLatch);
        // 执行并整理出所有的异步供给
        final List<C> provideList = provideList(determineAsyncConsumerCallbackList);
        for (int i = 0; i < dataList.size(); i++) {
            // 异步执行
            int finalI = i;
            executor.execute(() -> {
                try {
                    // 执行所有消费 before
                    executeConsumerBefore(determineAsyncConsumerCallbackList, provideList);
                    asyncLogic.accept(dataList.get(finalI), finalI);
                } finally {

                    countDownLatch.countDown();
                    // 执行所有消费 after
                    executeConsumerAfter(determineAsyncConsumerCallbackList, provideList);
                }
            });
        }
        try {
            // 等待
            countDownLatch.await();
        } catch (InterruptedException e) {
            throw new BaseException(ExceptionEnumOption.of(HttpStatus.INTERNAL_SERVER_ERROR, "Async executor interrupted"));
        }
    }

    /**
     * 异步执行
     *
     * @param asyncLogic 异步逻辑
     * @param <R>        逻辑函数泛型
     * <AUTHOR>
     */
    public static <R> CompletableFuture<R> asyncExecutor(Supplier<R> asyncLogic) {

        return asyncExecutor(asyncLogic, EXECUTOR_SERVICE, AsyncConsumerCallback.DEFAULT_EMPTY);
    }

    /**
     * 异步执行
     *
     * @param asyncLogic             异步逻辑
     * @param asyncConsumerCallbacks 异步消费回调
     * @param <R>                    逻辑函数泛型
     * @param <C>                    异步消费回调泛型
     * <AUTHOR>
     */
    @SafeVarargs
    public static <R, C> CompletableFuture<R> asyncExecutor(Supplier<R> asyncLogic, AsyncConsumerCallback<C>... asyncConsumerCallbacks) {

        return asyncExecutor(asyncLogic, EXECUTOR_SERVICE, asyncConsumerCallbacks);
    }

    /**
     * 异步执行
     *
     * @param asyncLogic             异步逻辑
     * @param asyncConsumerCallbacks 异步消费回调
     * @param executor               并发执行器
     * @param <R>                    逻辑函数泛型
     * @param <C>                    异步消费回调泛型
     * <AUTHOR>
     */
    @SafeVarargs
    public static <R, C> CompletableFuture<R> asyncExecutor(Supplier<R> asyncLogic, Executor executor, AsyncConsumerCallback<C>... asyncConsumerCallbacks) {
        // 确定所有的异步消费回调
        final List<AsyncConsumerCallback<C>> determineAsyncConsumerCallbackList = determineAsyncConsumerCallback(asyncConsumerCallbacks);
        // 执行并整理出所有的异步供给
        final List<C> provideList = provideList(determineAsyncConsumerCallbackList);
        return CompletableFuture.supplyAsync(() -> {
            R logic;
            try {
                // 执行所有消费 before
                executeConsumerBefore(determineAsyncConsumerCallbackList, provideList);
                logic = asyncLogic.get();
            } finally {
                // 执行所有消费 after
                executeConsumerAfter(determineAsyncConsumerCallbackList, provideList);
            }
            return logic;
        }, executor);
    }

    /**
     * 执行所有消费 before
     *
     * @param asyncConsumerCallbackList 异步消费回调列表
     * @param provideList               异步供给列表
     * @param <C>                       异步消费回调泛型
     * <AUTHOR>
     */
    public static <C> void executeConsumerBefore(List<AsyncConsumerCallback<C>> asyncConsumerCallbackList, List<C> provideList) {
        for (int i = 0; i < asyncConsumerCallbackList.size(); i++) {

            final AsyncConsumerCallback<C> consumerCallback = asyncConsumerCallbackList.get(i);
            consumerCallback.subConsumerBefore(provideList.get(i));
        }
    }

    /**
     * 执行所有消费 after
     *
     * @param asyncConsumerCallbackList 异步消费回调列表
     * @param provideList               异步供给列表
     * @param <C>                       异步消费回调泛型
     * <AUTHOR>
     */
    public static <C> void executeConsumerAfter(List<AsyncConsumerCallback<C>> asyncConsumerCallbackList, List<C> provideList) {
        for (int i = 0; i < asyncConsumerCallbackList.size(); i++) {

            final AsyncConsumerCallback<C> consumerCallback = asyncConsumerCallbackList.get(i);
            consumerCallback.subConsumerAfter(provideList.get(i));
        }
    }

    /**
     * 执行并整理出所有的异步供给
     *
     * @param asyncConsumerCallbackList 异步消费回调列表
     * @param <C>                       异步消费回调泛型
     * @return 异步供给列表
     * <AUTHOR>
     */
    public static <C> List<C> provideList(List<AsyncConsumerCallback<C>> asyncConsumerCallbackList) {

        final List<C> provideList = new CopyOnWriteArrayList<>();
        for (AsyncConsumerCallback<C> asyncConsumerCallback : asyncConsumerCallbackList) {
            try {
                C mainProvide = asyncConsumerCallback.mainProvide();
                provideList.add(mainProvide);
            } catch (Throwable e) {
                asyncConsumerCallbackList.remove(asyncConsumerCallback);
                log.warn("{} main provide is fail,msg: {}", asyncConsumerCallback.getClass().getSimpleName(), e.getMessage());
            }
        }
        return provideList;
    }

    /**
     * 确定所有的异步消费回调
     *
     * @param asyncConsumerCallbacks 异步消费回调
     * @param <C>                    异步消费回调泛型
     * @return 异步消费回调列表
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    public static <C> List<AsyncConsumerCallback<C>> determineAsyncConsumerCallback(AsyncConsumerCallback<C>... asyncConsumerCallbacks) {

        List<AsyncConsumerCallback<C>> determineAsyncConsumerCallbackList = new CopyOnWriteArrayList<>();
        if (ArrayUtils.isNotEmpty(asyncConsumerCallbacks)) {
            determineAsyncConsumerCallbackList.addAll(Arrays.stream(asyncConsumerCallbacks).map(AsyncUtils::determineAsyncConsumerCallback).toList());
        }
        return determineAsyncConsumerCallbackList;
    }

    /**
     * 确定异步消费回调
     *
     * @param asyncConsumerCallback 异步消费回调
     * @param <C>                   异步消费回调泛型
     * @return 异步消费回调
     * <AUTHOR>
     */
    @SuppressWarnings("unchecked")
    public static <C> AsyncConsumerCallback<C> determineAsyncConsumerCallback(AsyncConsumerCallback<C> asyncConsumerCallback) {
        if (Objects.isNull(asyncConsumerCallback)) {

            asyncConsumerCallback = (AsyncConsumerCallback<C>) AsyncConsumerCallback.DEFAULT_EMPTY;
        }
        return asyncConsumerCallback;
    }


    /**
     * 异步消费回调
     *
     * <AUTHOR>
     * @version 1.0.0
     */
    public interface AsyncConsumerCallback<T> {

        /**
         * 默认空异步消费回调
         */
        DefaultEmptyAsyncConsumerCallback DEFAULT_EMPTY = new DefaultEmptyAsyncConsumerCallback();

        /**
         * 主线程供给
         *
         * @return 供给
         * <AUTHOR>
         */
        default T mainProvide() {

            return null;
        }

        /**
         * 子线程消费前
         *
         * @param main 供给
         * <AUTHOR>
         */
        default void subConsumerBefore(T main) {

        }

        /**
         * 子线程消费后
         *
         * @param main 供给
         * <AUTHOR>
         */
        default void subConsumerAfter(T main) {

        }

        /**
         * 获取是否排除掉{@link java.util.concurrent.CountDownLatch}的应用
         *
         * @return 是否排除掉 {@link java.util.concurrent.CountDownLatch} 的应用
         * <AUTHOR>
         */
        default boolean excludeCountDownLatch() {

            return false;
        }
    }

    /**
     * 默认空异步消费回调
     *
     * <AUTHOR>
     * @version 1.0.0
     */
    static final class DefaultEmptyAsyncConsumerCallback implements AsyncConsumerCallback<Object> {

        private DefaultEmptyAsyncConsumerCallback() {
        }
    }
}
