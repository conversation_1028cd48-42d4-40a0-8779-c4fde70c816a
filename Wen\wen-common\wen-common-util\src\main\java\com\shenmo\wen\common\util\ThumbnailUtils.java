package com.shenmo.wen.common.util;

import com.shenmo.wen.common.constant.BucketConstant;
import net.coobird.thumbnailator.Thumbnails;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public class ThumbnailUtils {

    /**
     * 生成缩略图并返回字节数组
     * @param inputPath 输入图像文件路径
     * @param width 缩略图宽度
     * @param height 缩略图高度
     * @return 缩略图的字节数组
     * @throws IOException 如果文件读取或生成缩略图过程中发生错误
     */
    public static byte[] generateThumbnailAsBytes(String inputPath, int width, int height) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Thumbnails.of(new File(inputPath))
                .size(width, height)
                .toOutputStream(baos);
        return baos.toByteArray();
    }


    /**
     * 从输入流生成缩略图并返回字节数组（适用于上传文件等情况）
     * @param inputStream 输入流
     * @param width 缩略图宽度
     * @param height 缩略图高度
     * @return 缩略图字节数组
     * @throws IOException 如果生成缩略图过程中发生错误
     */
    public static byte[] generateThumbnailFromInputStreamAsBytes(InputStream inputStream, int width, int height) throws IOException {
        ByteArrayOutputStream baos = new ByteArrayOutputStream();
        Thumbnails.of(inputStream)
                .size(width, height)
                .keepAspectRatio(true)
                .toOutputStream(baos);
        return baos.toByteArray();
    }

    public static int generateThumbnailSize(String bucket) {
        if (BucketConstant.ARTICLE.equals(bucket)) {
            return 300;
        }
        return 100;
    }
}
