package com.shenmo.wen.common.util;


import com.shenmo.wen.common.pojo.response.ResponseData;
import org.springframework.core.io.buffer.DataBuffer;
import org.springframework.http.MediaType;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.web.reactive.function.BodyInserters;
import org.springframework.web.reactive.function.server.ServerResponse;
import reactor.core.publisher.Mono;

import java.nio.charset.StandardCharsets;


/**
 * 响应式http响应工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class ServerResponseUtils {

    /**
     * 写入响应
     *
     * @param response     响应式http响应
     * @param responseData 响应数据
     * @return 响应式流
     * <AUTHOR>
     */
    public static Mono<Void> writeResponse(ServerHttpResponse response, ResponseData<?> responseData) {

        response.getHeaders().setContentType(MediaType.APPLICATION_JSON);
        DataBuffer buffer = response.bufferFactory()
                .wrap(JacksonUtils.toJson(responseData).getBytes(StandardCharsets.UTF_8));
        return response.writeWith(Mono.just(buffer));
    }

    /**
     * 成功响应
     *
     * @param responseData 响应数据
     * @return 响应式流
     * <AUTHOR>
     */
    public static <T> Mono<ServerResponse> ok(ResponseData<T> responseData) {

        return ServerResponse.ok().body(BodyInserters.fromValue(responseData));
    }
}
