import { Extension } from '@tiptap/core'
import { Plugin, Plugin<PERSON><PERSON> } from '@tiptap/pm/state'
import { DragHandleView } from './DragHandleView'

export interface DragHandleOptions {
  enabled?: boolean
}

export const DragHandleExtension = Extension.create<DragHandleOptions>({
  name: 'dragHandle',

  addOptions() {
    return {
      enabled: true,
    }
  },

  addProseMirrorPlugins() {
    if (!this.options.enabled) return []

    const view = new DragHandleView({
      editor: this.editor,
    })

    return [
      new Plugin({
        key: new PluginKey(`${this.name}-drag-handle`),
        view: () => ({
          destroy: () => view.destroy()
        }),
        props: {
          handleDOMEvents: view.events()
        },
      }),
    ]
  },
})