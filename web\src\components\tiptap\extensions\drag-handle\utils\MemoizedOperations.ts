/**
 * Memoized operations for performance optimization
 */
export class MemoizedOperations {
  private static selectorCache = new Map<string, string>()
  private static nodeTypeCache = new WeakMap<HTMLElement, string>()
  private static positionCache = new WeakMap<HTMLElement, number>()

  static getNodeSelector(nodeType: string, nodeTypeConfigs: Record<string, any>): string {
    const cacheKey = `${nodeType}-selector`
    
    if (this.selectorCache.has(cacheKey)) {
      return this.selectorCache.get(cacheKey)!
    }

    const config = nodeTypeConfigs[nodeType]
    const selector = config?.selector || this.getDefaultSelector(nodeType)
    
    this.selectorCache.set(cacheKey, selector)
    return selector
  }

  static getNodeTypeFromElement(
    element: HTMLElement, 
    nodeTypeConfigs: Record<string, any>
  ): string | null {
    if (this.nodeTypeCache.has(element)) {
      return this.nodeTypeCache.get(element)!
    }

    for (const [nodeType, config] of Object.entries(nodeTypeConfigs)) {
      const selectors = config.selector.split(',').map((s: string) => s.trim())
      if (selectors.some((selector: string) => element.matches(selector))) {
        this.nodeTypeCache.set(element, nodeType)
        return nodeType
      }
    }

    this.nodeTypeCache.set(element, '')
    return null
  }

  static getElementPosition(element: HTMLElement, view: any): number | null {
    if (this.positionCache.has(element)) {
      return this.positionCache.get(element)!
    }

    const pos = view.posAtDOM(element, 0)
    if (pos !== null && pos !== undefined) {
      this.positionCache.set(element, pos)
    }
    
    return pos
  }

  private static getDefaultSelector(nodeType: string): string {
    const selectorMap: Record<string, string> = {
      paragraph: 'p',
      heading: 'h1, h2, h3, h4, h5, h6',
      listItem: 'li',
      codeBlock: '.code-block, pre',
      blockquote: 'blockquote',
      bulletList: 'ul',
      orderedList: 'ol'
    }
    
    return selectorMap[nodeType] || `.${nodeType}`
  }

  static clearCache(): void {
    this.selectorCache.clear()
    this.nodeTypeCache = new WeakMap()
    this.positionCache = new WeakMap()
  }

  static clearElementCache(element: HTMLElement): void {
    this.nodeTypeCache.delete(element)
    this.positionCache.delete(element)
  }
}