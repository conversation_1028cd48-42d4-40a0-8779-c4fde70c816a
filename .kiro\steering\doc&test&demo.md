---
inclusion: always
---

# Documentation, Testing, and Demo Organization

## File Organization Rules

### Centralized Structure
All documentation, tests, and demo files for the web application should be organized in dedicated top-level directories:

- `web/docs/` - Component and feature documentation
- `web/tests/` - Unit and integration tests
- `web/demos/` - Interactive demos and examples

### Directory Structure
```
web/
├── docs/
│   ├── components/          # Component-specific documentation
│   ├── features/           # Feature documentation
│   └── api/               # API documentation
├── tests/
│   ├── components/         # Component tests
│   ├── features/          # Feature tests
│   └── utils/             # Utility tests
└── demos/
    ├── components/         # Component demos
    └── features/          # Feature demos
```

### Naming Conventions
- **Documentation**: Use `.md` extension with descriptive names (e.g., `TipTapEditor.md`)
- **Tests**: Use `.test.ts` or `.spec.ts` suffix matching the component/feature name
- **Demos**: Use `Demo.vue` suffix for Vue components (e.g., `TipTapEditorDemo.vue`)

### Co-location Principle
When creating documentation, tests, or demos:
1. Mirror the source structure in the respective directory
2. Use the same naming pattern as the source file
3. Group related files by business domain or component family

### Examples
```
# Source component
web/src/components/tiptap/extensions/ClickMenu.vue

# Corresponding files
web/docs/components/tiptap/extensions/ClickMenu.md
web/tests/components/tiptap/extensions/ClickMenu.test.ts
web/demos/components/tiptap/extensions/ClickMenuDemo.vue
```

## Implementation Guidelines

- **Avoid scattering** documentation, tests, or demos within `web/src/`
- **Maintain consistency** in directory structure across docs, tests, and demos
- **Use descriptive paths** that clearly indicate the purpose and scope of each file
- **Group by business logic** rather than technical implementation details