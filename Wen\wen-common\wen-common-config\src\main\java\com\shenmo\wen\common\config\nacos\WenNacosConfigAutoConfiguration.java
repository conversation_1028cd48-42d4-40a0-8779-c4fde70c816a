
package com.shenmo.wen.common.config.nacos;

import com.alibaba.cloud.nacos.NacosConfigEnabledCondition;
import com.alibaba.cloud.nacos.NacosConfigManager;
import com.alibaba.cloud.nacos.NacosConfigProperties;
import com.alibaba.cloud.nacos.annotation.NacosAnnotationProcessor;
import com.alibaba.cloud.nacos.refresh.NacosContextRefresher;
import com.alibaba.cloud.nacos.refresh.NacosRefreshHistory;
import org.springframework.beans.factory.BeanFactoryUtils;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.SearchStrategy;
import org.springframework.context.ApplicationContext;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Conditional;
import org.springframework.context.annotation.Configuration;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@AutoConfiguration
@Conditional(NacosConfigEnabledCondition.class)
public class WenNacosConfigAutoConfiguration {


	@Bean
	public NacosConfigProperties nacosConfigProperties(ApplicationContext context) {
		if (context.getParent() != null && BeanFactoryUtils.beanNamesForTypeIncludingAncestors(context.getParent(),
				NacosConfigProperties.class).length > 0) {
			return BeanFactoryUtils.beanOfTypeIncludingAncestors(context.getParent(), NacosConfigProperties.class);
		}
		if (NacosConfigManager.getInstance() == null) {
			return new NacosConfigProperties();
		}
		else {
			return NacosConfigManager.getInstance().getNacosConfigProperties();
		}
	}

	@Bean
	public static NacosAnnotationProcessor nacosAnnotationProcessor() {
		return new NacosAnnotationProcessor();
	}
}
