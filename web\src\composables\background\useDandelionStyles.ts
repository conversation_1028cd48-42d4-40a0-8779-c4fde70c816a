import { ref } from 'vue'

/**
 * 蒲公英样式生成逻辑
 * 抽取自BackgroundAnimation组件
 */
export function useDandelionStyles() {
  // 蒲公英种子样式生成
  const getDandelionSeedStyle = (index: number) => {
    // 随机核心尺寸，这是蒲公英种子的中心部分
    const coreSize = Math.floor(Math.random() * 3) + 2 // 2-5px的核心大小

    // 从屏幕底部或者下方区域开始漂浮
    const left = Math.floor(Math.random() * 100)
    const startBottom = Math.floor(Math.random() * 10) - 5 // 从底部或稍微底部以下开始

    // 飘向更高的位置，覆盖整个屏幕
    const floatHeight = 80 + Math.floor(Math.random() * 40) // 漂浮高度80-120%屏幕高度

    // 使水平漂移更自然，添加曲线运动感
    const floatSideWave = Math.floor(Math.random() * 10) + 5 // 波浪幅度
    const floatSide = Math.floor(Math.random() * 40) - 20 // 总体水平偏移 -20% 到 +20%

    // 更长的动画时间，给人缓慢飘向远处的感觉，增加曲线感
    const duration = Math.floor(Math.random() * 20) + 20 // 20-40秒
    const delay = Math.floor(Math.random() * 15) // 0-15秒延迟，创造自然的错落感

    // 轻微的模糊效果，远处的种子更模糊
    const blur = Math.random() * 1.5 + 0.5

    // 每个蒲公英种子有细微的旋转效果，但不要太夸张
    const rotation = Math.floor(Math.random() * 360) - 180 // -180度到+180度的旋转

    // 蒲公英种子的不透明度，随距离变化
    const maxOpacity = Math.random() * 0.2 + 0.7 // 最大不透明度0.7-0.9

    // 随机确定蒲公英种子的类型
    const seedType = index % 3 // 0,1,2三种不同外观的种子类型

    return {
      width: `${coreSize}px`,
      height: `${coreSize}px`,
      left: `${left}%`,
      bottom: `${startBottom}%`,
      animationDelay: `${delay}s`,
      filter: `blur(${blur}px)`,
      '--seed-type': seedType,
      '--animation-duration': `${duration}s`,
      '--float-height': `${floatHeight}vh`,
      '--float-side': `${floatSide}vw`,
      '--float-side-wave': `${floatSideWave}vw`,
      '--rotation': `${rotation}deg`,
      '--max-opacity': maxOpacity,
      '--fluff-count': '1',
      '--core-size': `${coreSize}px`,
    }
  }

  return {
    getDandelionSeedStyle,
  }
}
