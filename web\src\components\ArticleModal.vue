<template>
  <NModal
    v-model:show="modalState.isArticleDialogVisible.value"
    preset="dialog"
    negative-text="算了"
    positive-text="确认"
    @negative-click="() => modalState.handleClose(articleForm.resetArticleForm)"
    @positive-click="handleSubmit"
    :showIcon="false"
    @close="() => modalState.handleClose(articleForm.resetArticleForm)"
    @mask-click="() => modalState.handleClose(articleForm.resetArticleForm)"
    :mask-closable="false"
    :auto-focus="false"
    :close-on-esc="false"
    class="article-modal"
    :positive-button-props="{ loading: modalState.submitLoading.value }"
  >
    <template #header>
      <NGradientText type="primary" :size="20">
        {{ modalState.isEditingArticle.value ? '是得再改改' : '想点什么呢' }}
      </NGradientText>
      <FileUpload :size="24" class="cursor-pointer" @click="articleFile.handleArticleFileClick" />
      <input
        type="file"
        ref="articleFile.articleFileInputRef"
        accept=".md"
        @change="
          (e) =>
            articleFile.handleArticleFileChange(
              e,
              articleForm.articleForm,
              articleForm.articleTiptapEditorRef,
            )
        "
        class="display-none"
      />
    </template>
    <NForm :model="articleForm.articleForm.value" ref="articleFormRef" label-placement="left">
      <!-- 标题 -->
      <NFormItem label="标题" path="title" style="width: min(30rem, 100%)">
        <NInput v-model:value="articleForm.articleForm.value.title" placeholder="请输入文章标题" />
      </NFormItem>
      <!-- 标签 -->
      <NFormItem label="标签" path="tag" style="width: min(30rem, 100%)">
        <NDynamicTags
          v-model:value="articleForm.articleForm.value.tags"
          :input-props="{ maxlength: 20 }"
          :max="3"
          type="primary"
          placeholder="请输入标签"
        />
      </NFormItem>
      <div class="flex-between-center" style="width: min(16rem, 100%)">
        <!-- 等级 -->
        <NFormItem label="等级 | 范围" path="allowCommentLevel" style="width: 6rem">
          <NPopselect
            v-model:value="articleForm.articleForm.value.operationLevel"
            :options="articleForm.generateCommentLevel.value"
            size="small"
            trigger="click"
          >
            <NButton size="small">
              Lv{{ articleForm.articleForm.value.operationLevel || '0' }}
            </NButton>
          </NPopselect>
        </NFormItem>
        <!-- 范围 -->
        <NFormItem path="scope">
          <NRadioGroup
            v-model:value="articleForm.articleForm.value.publishedScope"
            size="small"
            :default-value="ArticlePublishedScope.PERSONAL"
          >
            <NRadioButton
              class="flex-between-center"
              v-for="scope in [
                {
                  value: ArticlePublishedScope.PUBLIC,
                  label: ARTICLE_PUBLISHED_SCOPE_LABEL[ArticlePublishedScope.PUBLIC],
                },
                {
                  value: ArticlePublishedScope.PERSONAL,
                  label: ARTICLE_PUBLISHED_SCOPE_LABEL[ArticlePublishedScope.PERSONAL],
                },
              ]"
              :key="scope.value"
              :value="scope.value"
              :label="scope.label"
            />
          </NRadioGroup>
        </NFormItem>
      </div>
      <!-- 分享用户，仅个人范围时显示 -->
      <NFormItem
        v-if="articleForm.articleForm.value.publishedScope === ArticlePublishedScope.PERSONAL"
        label="分享给"
        path="shareUsers"
        style="width: min(30rem, 100%)"
      >
        <SearchUserSelect
          v-model="articleForm.articleForm.value.shareUsers"
          placeholder="请搜索并选择用户"
          ref="shareUserSelectRef"
        />
      </NFormItem>
      <!-- 内容 -->
      <NFormItem path="content">
        <NScrollbar class="article-modal-content">
          <TiptapEditor
            ref="articleTiptapEditorRef"
            v-model="articleForm.articleForm.value.contentObj"
            :editor-props="{
              attributes: {
                class: 'ProseMirrorNoneOutline',
              },
            }"
            :bubble-menu="true"
            :floating-menu="true"
            :file-bucket="ARTICLE"
            :all-extensions="true"
            :toolbar="true"
            :toolbar-class="['editor-toolbar', 'editor-toolbar-bgc']"
            placeholder="尽情发挥！"
            :show-character-count="true"
            :character-limit="ARTICLE_CHARACTER_LIMIT"
            :save-loading="modalState.quickSaveLoading.value"
            @save="handleQuickSave"
          />
        </NScrollbar>
      </NFormItem>
    </NForm>
  </NModal>
</template>

<script setup lang="ts">
import { FileUpload } from '@/icons'
import {
  NButton,
  NModal,
  NForm,
  NFormItem,
  NPopselect,
  NInput,
  NDynamicTags,
  NGradientText,
  NScrollbar,
  NRadioGroup,
  NRadioButton,
} from 'naive-ui'

// 导入组件
import SearchUserSelect from '@/components/SearchUserSelect.vue'
import TiptapEditor from '@/components/tiptap/core/TipTapEditor.vue'
// 导入类型
import type { Article } from '@/types/article.types'
// 导入 composables
import { useArticleFile } from '@/composables/article/useArticleFile'
import { useArticleForm } from '@/composables/article/useArticleForm'
import { useArticleModalState } from '@/composables/article/useArticleModalState'
// 导入常量
import {
  ArticlePublishedScope,
  ARTICLE_PUBLISHED_SCOPE_LABEL,
} from '@/constants/article_published_scope.constants'
import { ARTICLE } from '@/constants/bucket.constants'
import { ARTICLE_CHARACTER_LIMIT } from '@/constants/tiptap.constants'

// 定义 emits
const emit = defineEmits<{
  (event: 'success'): void
}>()

// 使用 composables
const modalState = useArticleModalState()
const articleForm = useArticleForm()
const articleFile = useArticleFile()

// 解构ref，确保ref绑定正确
const { articleFormRef, articleTiptapEditorRef, shareUserSelectRef } = articleForm

// 设置键盘监听
modalState.setupKeyboardListener(() => handleQuickSave())

// 处理提交
const handleSubmit = () => {
  articleForm.submitArticleForm(
    modalState.isEditingArticle,
    modalState.submitLoading,
    modalState.isArticleDialogVisible,
    emit,
  )
}

// 处理快速保存
const handleQuickSave = () => {
  articleForm.quickSaveArticleForm(
    modalState.isEditingArticle,
    modalState.quickSaveLoading,
    modalState.isArticleDialogVisible,
    emit,
  )
}

// 暴露方法给父组件
defineExpose({
  openCreateArticleDialog: () => modalState.openCreateArticleDialog(articleForm.resetArticleForm),
  openEditArticleDialog: (articleData: Article) =>
    modalState.openEditArticleDialog(articleData, articleForm.setFormData),
})
</script>

<style lang="scss">
.article-modal {
  &.n-modal {
    width: 100vw;
    max-width: 100vw;
  }

  &.n-dialog {
    margin: 0;
  }

  &-content {
    max-width: 100%;
    padding: 0 1.25rem;
    border: var(--border-1);
    border-radius: 0.25rem;
    min-height: 66.5vh;
    min-height: 66.5dvh;
    max-height: 66.5vh;
    max-height: 66.5dvh;
    overflow: hidden;
    box-sizing: border-box;

    .dark-theme & {
      background-color: var(--white-2);
    }

    /* 确保编辑器不超出容器 */
    .tiptap-editor-wrapper {
      max-width: 100%;
      height: 100%;
      box-sizing: border-box;
      display: flex;
      flex-direction: column;

      .editor-toolbar {
        position: sticky;
        top: 0;
        z-index: 10;
      }

      .editor-content {
        flex: 1;
        overflow-y: auto;
        max-width: 100%;
        box-sizing: border-box;
      }
    }
  }
}
</style>
