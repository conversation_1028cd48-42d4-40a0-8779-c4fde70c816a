<template>
  <div class="background-animation" :class="{ 'dark-theme': isDarkTheme }" :style="backgroundStyle">
    <!-- 静态元素容器 -->
    <div class="static-elements">
      <!-- 浅色主题云朵 -->
      <div v-if="!isDarkTheme && enableClouds" class="clouds-container">
        <div
          v-for="i in Math.ceil(cloudCount)"
          :key="`cloud-${i}`"
          class="cloud"
          :style="getCloudStyle(i)"
        ></div>
      </div>

      <!-- 暗色主题星星 -->
      <div v-if="isDarkTheme && enableStars" class="stars-container">
        <div
          v-for="i in particleCount"
          :key="`star-${i}`"
          class="star"
          :style="getStarStyle(i)"
        ></div>
      </div>
    </div>

    <!-- 动态元素容器 -->
    <div class="dynamic-elements">
      <!-- 浅色主题蒲公英 -->
      <div v-if="!isDarkTheme && enableDandelions" class="dandelions-container">
        <div
          v-for="i in particleCount"
          :key="`dandelion-${i}`"
          class="dandelion-seed"
          :style="getDandelionSeedStyle(i)"
        >
          <!-- 移除多根蒲公英绒毛循环，只保留一个主茎 -->
          <div class="main-stem"></div>
        </div>
      </div>

      <!-- 暗色主题萤火虫 -->
      <div v-if="isDarkTheme && enableFireflies" class="fireflies-container">
        <div
          v-for="i in particleCount"
          :key="`firefly-${i}`"
          class="firefly"
          :style="getFireflyStyle(i)"
        ></div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch } from 'vue'

// REFACTOR: 使用抽取出的composables
import {
  useBackgroundStyles,
  useCloudStyles,
  useStarStyles,
  useDandelionStyles,
  useFireflyStyles,
} from '@/composables/background'

// 定义可配置的属性，使组件更灵活
const props = defineProps({
  particleCount: {
    type: Number,
    default: 30,
    description: '动态元素的数量（蒲公英/萤火虫）',
  },
  cloudCount: {
    type: Number,
    default: 9,
    description: '云朵数量',
  },
  enableClouds: {
    type: Boolean,
    default: true,
    description: '是否显示云朵',
  },
  enableDandelions: {
    type: Boolean,
    default: true,
    description: '是否显示蒲公英（浅色模式）',
  },
  enableStars: {
    type: Boolean,
    default: true,
    description: '是否显示星星（暗色模式）',
  },
  enableFireflies: {
    type: Boolean,
    default: true,
    description: '是否显示萤火虫（暗色模式）',
  },
  customLightGradient: {
    type: String,
    default: '',
    description: '自定义浅色主题背景渐变',
  },
  customDarkGradient: {
    type: String,
    default: '',
    description: '自定义暗色主题背景渐变',
  },
  zIndex: {
    type: Number,
    default: 0,
    description: '背景层级（z-index）',
  },
})

// 导出自定义事件
const emit = defineEmits(['theme-change'])

// REFACTOR: 使用背景样式composable
const { isDarkTheme, backgroundStyle } = useBackgroundStyles({
  customLightGradient: props.customLightGradient,
  customDarkGradient: props.customDarkGradient,
  zIndex: props.zIndex,
})

// REFACTOR: 使用云朵样式composable
const { getCloudStyle, generateCloudPseudoElementsCSS } = useCloudStyles()

// REFACTOR: 使用星星样式composable
const { getStarStyle } = useStarStyles()

// REFACTOR: 使用蒲公英样式composable
const { getDandelionSeedStyle } = useDandelionStyles()

// REFACTOR: 使用萤火虫样式composable
const { getFireflyStyle } = useFireflyStyles()

// 当主题变化时触发事件
watch(isDarkTheme, (newTheme) => {
  emit('theme-change', newTheme ? 'dark' : 'light')
})

// 当组件挂载完成后，动态生成云朵伪元素的CSS
onMounted(() => {
  // REFACTOR: 使用抽取出的generateCloudPseudoElementsCSS方法
  generateCloudPseudoElementsCSS()
})

// 导出方法和属性以便其他组件可以访问
defineExpose({
  isDarkTheme,
})
</script>

<style scoped>
.background-animation {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  overflow: hidden;
}

/* 静态元素样式 */
.static-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

/* 云朵样式 */
.clouds-container {
  position: absolute;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.cloud {
  position: absolute;
  background-color: transparent; /* 不使用背景色，使用伪元素构建云朵 */

  /* 添加非常缓慢的漂浮动画 */
  animation: cloud-float 180s linear infinite;

  /* 去除边框 */
  border: none !important;
  outline: none !important;

  /* 添加更大的模糊效果 */
  filter: blur(3px);

  /* 确保云朵容器本身没有背景和边框 */
  box-shadow: none !important;

  /* 确保没有可见的边界 */
  background-clip: padding-box;
  background-clip: padding-box;
}

/* 云朵的主体部分 */
.cloud::before {
  content: '';
  position: absolute;
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
  background-color: rgba(255, 255, 255, 85%);
  border-radius: 50%;
  box-shadow: 0 0 60px 15px rgba(255, 255, 255, 45%);

  /* 添加微妙的透明渐变 */
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 90%) 0%,
    rgba(255, 255, 255, 80%) 40%,
    rgba(255, 255, 255, 40%) 80%,
    rgba(255, 255, 255, 0%) 100%
  );
}

/* 创建伪元素来表示云朵的不同部分 */
.cloud::after {
  content: '';
  position: var(--pos-1, absolute);
  width: var(--width-1, 0);
  height: var(--height-1, 0);
  top: var(--top-1, 0);
  left: var(--left-1, 0);
  background-color: rgba(255, 255, 255, 85%);
  border-radius: var(--radius-1, 50%);
  box-shadow: 0 0 50px 10px rgba(255, 255, 255, 35%);

  /* 添加微妙的透明渐变 */
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 85%) 0%,
    rgba(255, 255, 255, 70%) 50%,
    rgba(255, 255, 255, 30%) 85%,
    rgba(255, 255, 255, 0%) 100%
  );
}

/* 针对不同类型云朵的特定样式 - 移除所有box-shadow */

/* 蓬松积云 */
.cloud[style*='--cloud-type: 0'] {
  box-shadow: none !important;
  animation-duration: 200s;
}

/* 长层云 */
.cloud[style*='--cloud-type: 1'] {
  box-shadow: none !important;
  animation-duration: 190s;
}

/* 小团层积云 */
.cloud[style*='--cloud-type: 2'] {
  box-shadow: none !important;
  animation-duration: 210s;
}

/* 绵羊状云朵 */
.cloud[style*='--cloud-type: 3'] {
  box-shadow: none !important;
  animation-duration: 170s;
}

/* 卷云 */
.cloud[style*='--cloud-type: 4'] {
  box-shadow: none !important;
  animation-duration: 160s;
}

/* 星星样式 */
.stars-container {
  position: absolute;
  width: 100%;
  height: 100%;
}

.star {
  position: absolute;
  background-color: #fff;
  border-radius: 50%;
  box-shadow:
    0 0 1px #fff,
    0 0 2px #fff; /* 减小光晕大小 */
}

/* 动态元素样式 */
.dynamic-elements {
  position: absolute;
  width: 100%;
  height: 100%;
}

/* 蒲公英种子样式 */
.dandelions-container {
  position: absolute;
  width: 100%;
  height: 100%;

  /* 添加视角效果 */
  perspective: 1000px;
}

.dandelion-seed {
  position: absolute;
  background-color: rgba(255, 255, 255, 90%);
  border-radius: 50%;

  /* 使用新的动画 */
  animation: float-dandelion-sky var(--animation-duration, 25s) cubic-bezier(0.4, 0, 0.2, 1)
    infinite;
  opacity: 0;
  transform-style: preserve-3d;

  /* 确保种子本体的圆形 */
  box-shadow: 0 0 2px rgba(255, 255, 255, 60%);
}

/* 为种子添加立体感的圆形种子部分 */
.dandelion-seed::before,
.dandelion-seed::after {
  content: '';
  position: absolute;
  width: calc(var(--core-size, 3px) * 6);
  height: calc(var(--core-size, 3px) * 6);
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  background: radial-gradient(
    ellipse at center,
    rgba(255, 255, 255, 90%) 0%,
    rgba(255, 255, 255, 70%) 25%,
    rgba(255, 255, 255, 40%) 50%,
    rgba(255, 255, 255, 10%) 75%,
    rgba(255, 255, 255, 0%) 100%
  );
  border-radius: 50%;

  /* 添加轻微摇摆动画 */
  animation: seed-puff-sway 3s ease-in-out infinite;
  opacity: 0.9;
  pointer-events: none;
}

.dandelion-seed::after {
  width: calc(var(--core-size, 3px) * 4.2);
  height: calc(var(--core-size, 3px) * 4.2);
  opacity: 0.7;
  animation-delay: -1.5s;
}

/* 蒲公英绒毛的摇摆动画 */
@keyframes seed-puff-sway {
  0%,
  100% {
    transform: translate(-50%, -50%) scale(1);
  }

  50% {
    transform: translate(-50%, -50%) scale(1.05);
  }
}

/* 添加主茎样式，取代多根绒毛 */
.dandelion-seed .main-stem {
  position: absolute;
  width: calc(var(--core-size, 3px) * 0.6);
  height: calc(var(--core-size, 3px) * 10); /* 更长的单一茎 */
  background: linear-gradient(to bottom, rgba(255, 255, 255, 90%), rgba(255, 255, 255, 0%));
  transform-origin: bottom center;
  top: calc(var(--core-size, 3px) * -10); /* 相应调整顶部位置 */
  left: calc(50% - var(--core-size, 3px) * 0.3);
  border-radius: 30% 30% 0 0;
  animation: stem-sway calc(var(--animation-duration, 25s) / 5) ease-in-out infinite alternate;
}

/* 第一种类型的种子 - 均匀伞状 */
.dandelion-seed[style*='--seed-type: 0'] .main-stem {
  height: calc(var(--core-size, 3px) * 12); /* 更长的茎 */
  top: calc(var(--core-size, 3px) * -12); /* 相应调整顶部位置 */
  width: calc(var(--core-size, 3px) * 0.7);
}

/* 第二种类型的种子 - 更蓬松 */
.dandelion-seed[style*='--seed-type: 1'] .main-stem {
  height: calc(var(--core-size, 3px) * 14); /* 最长的茎 */
  top: calc(var(--core-size, 3px) * -14); /* 相应调整顶部位置 */
  width: calc(var(--core-size, 3px) * 0.8);
  border-radius: 40% 40% 0 0;
}

/* 第三种类型的种子 - 更纤细 */
.dandelion-seed[style*='--seed-type: 2'] .main-stem {
  height: calc(var(--core-size, 3px) * 13); /* 适中的长度 */
  top: calc(var(--core-size, 3px) * -13); /* 相应调整顶部位置 */
  width: calc(var(--core-size, 3px) * 0.5); /* 更细的茎 */
  border-radius: 20% 20% 0 0;
}

/* 单茎的摇摆动画 */
@keyframes stem-sway {
  0%,
  100% {
    transform: rotate(-8deg);
  }

  25% {
    transform: rotate(3deg);
  }

  75% {
    transform: rotate(-5deg);
  }
}

/* 新的蒲公英飘向天空的动画 */
@keyframes float-dandelion-sky {
  0% {
    transform: translateY(0) translateX(0) rotate(0deg) translateZ(0);
    opacity: 0;
  }

  5% {
    opacity: var(--max-opacity, 0.8);
    transform: translateY(-5vh) translateX(0) rotate(calc(var(--rotation, 360deg) * 0.1))
      translateZ(0);
  }

  25% {
    transform: translateY(calc(-0.25 * var(--float-height, 100vh)))
      translateX(calc(var(--float-side-wave, 10vw) * 0.5))
      rotate(calc(var(--rotation, 360deg) * 0.3)) translateZ(20px);
  }

  50% {
    transform: translateY(calc(-0.5 * var(--float-height, 100vh)))
      translateX(calc(var(--float-side, 20vw) * 0.7)) rotate(calc(var(--rotation, 360deg) * 0.6))
      translateZ(40px);
  }

  75% {
    transform: translateY(calc(-0.75 * var(--float-height, 100vh)))
      translateX(calc(var(--float-side-wave, 10vw) * 0.2))
      rotate(calc(var(--rotation, 360deg) * 0.8)) translateZ(60px);
    opacity: var(--max-opacity, 0.8);
  }

  95% {
    opacity: 0.2;
  }

  100% {
    transform: translateY(calc(-1 * var(--float-height, 100vh))) translateX(var(--float-side, 20vw))
      rotate(var(--rotation, 360deg)) translateZ(80px);
    opacity: 0;
  }
}

/* 云朵漂浮动画 */
@keyframes cloud-float {
  0% {
    transform: translateX(-3%) translateY(0%);
  }

  50% {
    transform: translateX(0%) translateY(-0.5%);
  }

  100% {
    transform: translateX(3%) translateY(0%);
  }
}

/* 萤火虫生命周期动画：出现->移动->停留->消失->等待->重复 */
@keyframes firefly-lifecycle {
  /* 开始时不可见 */
  0% {
    opacity: 0;
    transform: translate(0, 0);
  }

  /* 淡入阶段 */
  5% {
    opacity: 0.7;
    transform: translate(0, 0);
  }

  /* 保持可见并开始移动 */
  15% {
    opacity: 0.7;
    transform: translate(0, 0);
  }

  /* 移动到目标位置 */
  35% {
    opacity: 0.7;
    transform: translate(var(--move-x), var(--move-y));
  }

  /* 在目标位置停留 */
  50% {
    opacity: 0.7;
    transform: translate(var(--move-x), var(--move-y));
  }

  /* 开始淡出 */
  55% {
    opacity: 0;
    transform: translate(var(--move-x), var(--move-y));
  }

  /* 保持不可见状态直到循环结束 */
  100% {
    opacity: 0;
    transform: translate(var(--move-x), var(--move-y));
  }
}

/* 萤火虫闪烁动画 */
@keyframes firefly-pulse {
  0%,
  100% {
    box-shadow: 0 0 var(--glow-size) var(--glow-color);
  }

  50% {
    box-shadow:
      0 0 calc(var(--glow-size) * 2) var(--glow-color),
      0 0 calc(var(--glow-size) * 3.5) rgba(255, 255, 255, 35%); /* 增加光晕大小和亮度 */
  }
}

/* 萤火虫样式 */
.fireflies-container {
  position: absolute;
  width: 100%;
  height: 100%;
}

.firefly {
  position: absolute;
  border-radius: 50%;
  opacity: 0;

  /* 闪烁光晕 */
  box-shadow: 0 0 var(--glow-size) var(--glow-color);

  /* 萤火虫移动和闪烁的复合动画 */
  animation:
    firefly-lifecycle var(--total-duration) ease-in-out infinite,
    firefly-pulse var(--pulse-duration, 2s) ease-in-out infinite;

  /* 使用will-change提高动画性能 */
  will-change: transform, opacity;
}

/* 动画定义 */
@keyframes twinkle {
  0%,
  100% {
    opacity: 0.3;
  }

  50% {
    opacity: 1;
  }
}
</style>
