# Theme configuration

This starter kit provides the default theme that defines common modification items through a series of css variables and you can override it with either vanilla css or any css framework. Or you can write css rule overrides directly.

```css
:root {
  --tiptap-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto,
  Oxygen, Ubuntu, Cantarell, "Open Sans", "Helvetica Neue", sans-serif;
  --tiptap-font-family-mono: "Fira Code", "JetBrains Mono", ui-monospace,
  SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New",
  monospace;
  --tiptap-font-weight: 400;
  --tiptap-font-size: 1rem;
  --tiptap-line-height: 1.7;
  --tiptap-color: #000000;
  --tiptap-color-primary: #6e56cf;
  --tiptap-color-secondly: #cdced6;
  --tiptap-background: #ffffff;
  --tiptap-background-selected: #008cff0e;
  --tiptap-card-color: #000000;
  --tiptap-card-background: #f9f9f9;
  --tiptap-code-color: #e5484d;
  --tiptap-code-background: #feebec;
  --tiptap-mark-color: #000000;
  --tiptap-mark-background: #fffab8;
  --tiptap-operator-shadow: rgba(0, 0, 0, 0.08);
  --tiptap-operator-border: #e8e8e8;
  --tiptap-operator-color: #60646c;
  --tiptap-operator-background: #ffffff;
  --tiptap-operator-hover-border: #cecece;
  --tiptap-operator-hover-color: #6e56cf;
  --tiptap-operator-hover-background: #f0f0f3;
  --tiptap-operator-dark-border: #e8e8e8;
  --tiptap-operator-dark-color: #edeef0;
  --tiptap-operator-dark-background: #111111;
  --tiptap-operator-dark-hover-border: #484848;
  --tiptap-operator-dark-hover-color: #6e56cf;
  --tiptap-operator-dark-hover-background: #212225;
}
```
