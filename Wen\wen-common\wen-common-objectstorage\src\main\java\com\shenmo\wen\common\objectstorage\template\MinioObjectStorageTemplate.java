package com.shenmo.wen.common.objectstorage.template;

import com.shenmo.wen.common.constant.StringConstant;
import com.shenmo.wen.common.objectstorage.response.*;
import com.shenmo.wen.common.util.AssertUtils;
import com.shenmo.wen.common.util.ThrowUtils;
import io.minio.*;
import io.minio.http.Method;
import io.minio.messages.Item;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * minio操作模板
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RequiredArgsConstructor
public class MinioObjectStorageTemplate extends AbstractObjectStorageTemplate implements ObjectStorageTemplate {

    /**
     * minio客户端
     */
    private final MinioClient minioClient;


    @Override
    public boolean bucketExists(String bucket) {
        try {
            return minioClient.bucketExists(BucketExistsArgs.builder().bucket(bucket).build());
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError(String.format("判断桶是否存在时失败: %s", bucket), e);
        }
    }

    @Override
    public void makeBucket(String... bucket) {
        for (String b : bucket) {
            try {
                if (!bucketExists(b)) {

                    minioClient.makeBucket(MakeBucketArgs.builder().bucket(b).build());
                }
            } catch (Exception e) {
                throw ThrowUtils.getThrow().internalServerError(String.format("创建桶时失败: %s", b), e);
            }
        }
    }

    @NonNull
    @Override
    public List<MinioBucketResponse> listBuckets() {
        try {
            return minioClient.listBuckets().stream().map(MinioBucketResponse::new).collect(Collectors.toList());
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError("获取所有桶时失败", e);
        }
    }

    @NonNull
    @Override
    public List<String> listBucketNames() {
        try {
            return minioClient.listBuckets().stream()
                    .map(io.minio.messages.Bucket::name)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError("获取所有桶名称时失败", e);
        }
    }

    @Override
    public String getPublicReadableBucketPolicy(String bucket) {

        return "{\n" +
                "    \"Version\": \"2012-10-17\",\n" +
                "    \"Statement\": [\n" +
                "        {\n" +
                "            \"Effect\": \"Allow\",\n" +
                "            \"Principal\": {\n" +
                "                \"AWS\": [\n" +
                "                    \"*\"\n" +
                "                ]\n" +
                "            },\n" +
                "            \"Action\": [\n" +
                "                \"s3:ListBucket\",\n" +
                "                \"s3:GetBucketLocation\"\n" +
                "            ],\n" +
                "            \"Resource\": [\n" +
                "                \"arn:aws:s3:::" + bucket + "\"\n" +
                "            ]\n" +
                "        },\n" +
                "        {\n" +
                "            \"Effect\": \"Allow\",\n" +
                "            \"Principal\": {\n" +
                "                \"AWS\": [\n" +
                "                    \"*\"\n" +
                "                ]\n" +
                "            },\n" +
                "            \"Action\": [\n" +
                "                \"s3:GetObject\"\n" +
                "            ],\n" +
                "            \"Resource\": [\n" +
                "                \"arn:aws:s3:::" + bucket + "/*\"\n" +
                "            ]\n" +
                "        }\n" +
                "    ]\n" +
                "}";
    }

    @Override
    public void removeBucket(String... bucket) {
        for (String b : bucket) {
            if (bucketExists(b)) {
                removeObjects(b);
                try {
                    minioClient.removeBucket(RemoveBucketArgs.builder()
                            .bucket(b)
                            .build());
                } catch (Exception e) {
                    throw ThrowUtils.getThrow().internalServerError(String.format("移除桶时失败: %s", b), e);
                }
            }
        }
    }

    @Override
    public void setBucketPolicy(String bucket, String policyConfig) {
        try {
            if (bucketExists(bucket)) {
                minioClient.setBucketPolicy(SetBucketPolicyArgs.builder()
                        .bucket(bucket)
                        .config(policyConfig)
                        .build());
            }
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError(String.format("设置桶策略时失败: %s -> %s", bucket, policyConfig), e);
        }
    }


    @Override
    public boolean existObject(String bucket, String object) {
        try {
            return Objects.nonNull(minioClient.statObject(StatObjectArgs.builder()
                    .bucket(bucket)
                    .object(object)
                    .build()));
        } catch (Exception e) {
            return false;
        }
    }


    @NonNull
    @Override
    public PutObjectResponse putObject(String bucket, String object, InputStream inputStream, long size, String contentType) {

        AssertUtils.isTrue(bucketExists(bucket), String.format("新增对象时桶 %s 不存在", bucket));
        try {
            return new PutObjectResponse(minioClient.putObject(PutObjectArgs.builder()
                    .bucket(bucket)
                    .object(object)
                    .stream(inputStream, size, -1)
                    .contentType(contentType)
                    .build()));
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError(String.format("新增对象时失败: %s -> %s:%s", bucket, object, contentType), e);
        }
    }

    @NonNull
    @Override
    public UploadObjectResponse uploadObject(String bucket, String object, String filePath) {

        AssertUtils.isTrue(bucketExists(bucket), String.format("上传对象时桶 %s 不存在", bucket));
        try {
            return new UploadObjectResponse(minioClient.uploadObject(UploadObjectArgs.builder()
                    .bucket(bucket)
                    .object(object)
                    .filename(filePath)
                    .contentType(tika.detect(object))
                    .build()), bucket, object);
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError(String.format("上传对象时失败: %s -> %s:%s", bucket, object, filePath), e);
        }
    }

    @NonNull
    @Override
    public CopyObjectResponse copyObject(String originBucket, String originObject, String targetBucket, String targetObject) {

        AssertUtils.isTrue(bucketExists(originBucket), String.format("复制对象时源桶 %s 不存在", originBucket));
        AssertUtils.isTrue(bucketExists(targetBucket), String.format("复制对象时目标桶 %s 不存在", targetBucket));
        try {
            return new CopyObjectResponse(minioClient.copyObject(CopyObjectArgs.builder()
                    .bucket(targetBucket)
                    .object(targetObject)
                    .source(CopySource.builder()
                            .bucket(originBucket)
                            .object(originObject)
                            .build())
                    .build()));
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError(String.format("复制对象时失败: %s:%s -> %s:%s", originBucket, originObject, targetBucket, targetObject), e);
        }
    }

    @NonNull
    @Override
    public MinioGetObjectResponse getObject(String bucket, String object) {
        if (!bucketExists(bucket)) {
            return new MinioGetObjectResponse(new io.minio.GetObjectResponse(null, null, null, null, null));
        }
        try {
            return new MinioGetObjectResponse(minioClient.getObject(GetObjectArgs.builder()
                    .bucket(bucket)
                    .object(object)
                    .build()));
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError(String.format("获取对象时失败: %s -> %s", bucket, object), e);
        }
    }

    /**
     * 递归获取对象列表
     *
     * @param resultList 结果列表
     * @param bucket     同名
     * @param prefix     对象前缀
     * @param recursive  是否递归获取对象
     * @throws Exception 获取结果时抛出的异常
     * <AUTHOR>
     */
    private void listObjects(List<Result<Item>> resultList, String bucket, @Nullable String prefix, boolean recursive) throws Exception {

        final ListObjectsArgs build = ListObjectsArgs.builder()
                .bucket(bucket)
                .prefix(prefix)
                .startAfter(resultList.get(resultList.size() - 1).get().objectName())
                .maxKeys(1000)
                .recursive(recursive)
                .build();
        final Iterable<Result<Item>> results = minioClient.listObjects(build);
        if (results.iterator().hasNext()) {
            results.forEach(resultList::add);
            listObjects(resultList, bucket, prefix, recursive);
        }
    }

    @NonNull
    @Override
    public MinioListObjectResponse listObjects(String bucket, @Nullable String prefix, int maxKeys, boolean recursive) {
        prefix = prefixFormatting(prefix);
        if (!bucketExists(bucket)) {
            return new MinioListObjectResponse(List.of(), bucket, prefix);
        }
        final List<Result<Item>> resultList = new ArrayList<>();
        if (maxKeys < 1 || maxKeys > 1000) {
            final Iterable<Result<Item>> results = minioClient.listObjects(ListObjectsArgs.builder()
                    .bucket(bucket)
                    .prefix(prefix)
                    .maxKeys(1000)
                    .recursive(recursive)
                    .build());
            results.forEach(resultList::add);
            if (CollectionUtils.isEmpty(resultList)) {
                return new MinioListObjectResponse(resultList, bucket, prefix);
            }
            try {
                listObjects(resultList, bucket, prefix, recursive);
            } catch (Exception e) {
                throw ThrowUtils.getThrow().internalServerError(String.format("获取某个桶下指定前缀下所有对象时失败: %s -> %s:%s:%s", bucket, prefix, maxKeys, recursive), e);
            }
        } else {
            minioClient.listObjects(ListObjectsArgs.builder()
                            .bucket(bucket)
                            .prefix(prefix)
                            .maxKeys(maxKeys)
                            .recursive(recursive)
                            .build())
                    .forEach(resultList::add);
        }
        return new MinioListObjectResponse(resultList, bucket, prefix);
    }

    @Override
    public void removeObject(String bucket, String... object) {
        for (String obj : object) {
            try {
                while (existObject(bucket, obj)) {
                    minioClient.removeObject(RemoveObjectArgs.builder()
                            .bucket(bucket)
                            .object(obj)
                            .build());
                }
            } catch (Exception e) {
                throw ThrowUtils.getThrow().internalServerError(String.format("移除对象时失败: %s -> %s", bucket, obj), e);
            }
        }
    }

    @NonNull
    @Override
    public String getPresignedObjectUrl(String bucket, String object, int expiry) {

        if (!bucketExists(bucket)) {
            return StringConstant.EMPTY;
        }
        try {
            return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                    .bucket(bucket)
                    .object(object)
                    .expiry(Long.valueOf(TimeUnit.DAYS.toSeconds(expiry)).intValue())
                    .method(Method.GET)
                    .build());
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError(String.format("获取指定对象url时失败: %s -> %s:%s", bucket, object, expiry), e);
        }
    }

    @Override
    public void downloadObject(String bucket, String object, String filePath) {
        try {
            if (localFilePrepare(bucket, object, filePath)) {
                return;
            }
            minioClient.downloadObject(DownloadObjectArgs.builder()
                    .bucket(bucket)
                    .object(object)
                    .filename(filePath)
                    .build());
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError(String.format("下载对象到本地时失败: %s -> %s:%s", bucket, object, filePath), e);
        }
    }


}
