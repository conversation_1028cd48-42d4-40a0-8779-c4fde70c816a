package com.shenmo.wen.app.core.notification.exception;

import com.shenmo.wen.common.exception.enumeration.ExceptionEnum;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnumOption;
import com.shenmo.wen.common.exception.enumeration.ExceptionType;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@ExceptionType(type = NotificationException.class, module = NotificationExceptionEnum.MODULE)
public enum NotificationExceptionEnum implements ExceptionEnum {

    /**
     * 当前用户未拥有通知修改权限
     */
    NOTIFICATION_MODIFY_PERMISSION(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "消息也一样，不是你的别乱弄！")),
    ;

    public static final String MODULE = "007";

    /**
     * 异常枚举选项
     */
    private final ExceptionEnumOption exceptionEnumOption;

    NotificationExceptionEnum(ExceptionEnumOption exceptionEnumOption) {

        this.exceptionEnumOption = exceptionEnumOption;
    }

    @Override
    public ExceptionEnumOption getOption() {

        return exceptionEnumOption;
    }
}
