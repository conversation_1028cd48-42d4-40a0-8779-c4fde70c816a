package com.shenmo.wen.common.exception.enumeration;

import org.springframework.http.HttpStatus;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@ExceptionType(module = BaseExceptionEnum.MODULE)
public enum BaseExceptionEnum implements ExceptionEnum {


    /**
     * 请求JSON参数格式不正确
     */
    HTTP_MESSAGE_NOT_READABLE(ExceptionEnumOption.of(HttpStatus.BAD_REQUEST, "请求JSON参数格式不正确，请检查参数格式")),

    /**
     * 参数传递格式不支持
     */
    HTTP_MEDIA_TYPE_NOT_SUPPORTED(ExceptionEnumOption.of(HttpStatus.BAD_REQUEST, "参数传递格式不支持，请使用JSON格式传参")),

    /**
     * 不支持该请求方法
     */
    HTTP_REQUEST_METHOD(ExceptionEnumOption.of(HttpStatus.METHOD_NOT_ALLOWED, "不支持该请求方法")),

    /**
     * 资源路径不存在
     */
    NO_HANDLER_FOUND(ExceptionEnumOption.of(HttpStatus.NOT_FOUND, "资源路径不存在")),

    /**
     * 参数校验失败
     */
    METHOD_ARGUMENT_NOT_VALID(ExceptionEnumOption.of(HttpStatus.BAD_REQUEST, "参数校验失败")),

    /**
     * 参数绑定失败
     */
    BIND(ExceptionEnumOption.of(HttpStatus.BAD_REQUEST, "参数绑定失败")),

    /**
     * 参数类型不匹配
     */
    METHOD_ARGUMENT_TYPE_MISMATCH(ExceptionEnumOption.of(HttpStatus.BAD_REQUEST, "参数类型不匹配")),

    /**
     * 文件超过最大上传大小
     */
    MAX_UPLOAD_SIZE_EXCEEDED(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "文件超过最大上传大小")),

    /**
     * 字符编码不支持
     */
    UN_SUPPORTED(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "字符编码不支持")),

    /**
     * 数据访问异常
     */
    DATA_ACCESS(ExceptionEnumOption.of(HttpStatus.INTERNAL_SERVER_ERROR, "蓝瘦, 数据获取失败了~")),

    /**
     * token为空
     */
    TOKEN_NOT_FOUND(ExceptionEnumOption.of(HttpStatus.UNAUTHORIZED, "未获取到token")),

    /**
     * 服务器出现异常，请联系管理员
     */
    SERVER_ERROR(ExceptionEnumOption.of(HttpStatus.INTERNAL_SERVER_ERROR, "服务器出现异常，请联系管理员"));

    public static final String MODULE = "001";

    /**
     * 异常枚举选项
     */
    private final ExceptionEnumOption exceptionEnumOption;

    BaseExceptionEnum(ExceptionEnumOption exceptionEnumOption) {

        this.exceptionEnumOption = exceptionEnumOption;
    }

    @Override
    public ExceptionEnumOption getOption() {

        return exceptionEnumOption;
    }
}
