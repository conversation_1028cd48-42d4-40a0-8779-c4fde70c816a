<template>
  <div class="test-extensions-page">
    <h1>TipTap Extensions Test Page</h1>
    
    <div class="test-section">
      <h2>Extension Status</h2>
      <div class="status-grid">
        <div class="status-item">
          <span class="label">Drag Handle Extension:</span>
          <span :class="['status', dragHandleStatus.loaded ? 'success' : 'error']">
            {{ dragHandleStatus.loaded ? '✅ Loaded' : '❌ Not Loaded' }}
          </span>
        </div>
        <div class="status-item">
          <span class="label">Click Menu Extension:</span>
          <span :class="['status', clickMenuStatus.loaded ? 'success' : 'error']">
            {{ clickMenuStatus.loaded ? '✅ Loaded' : '❌ Not Loaded' }}
          </span>
        </div>
        <div class="status-item">
          <span class="label">Drag Handles in DOM:</span>
          <span :class="['status', dragHandleCount > 0 ? 'success' : 'warning']">
            {{ dragHandleCount }} found
          </span>
        </div>
        <div class="status-item">
          <span class="label">Click Menus in DOM:</span>
          <span :class="['status', clickMenuCount > 0 ? 'success' : 'info']">
            {{ clickMenuCount }} found
          </span>
        </div>
      </div>
    </div>

    <div class="test-section">
      <h2>Test Editor</h2>
      <p class="instructions">
        Try the following:
        <br>• Hover over paragraphs to see drag handles
        <br>• Click on empty areas to see click menu
        <br>• Use Ctrl+Space to open click menu
      </p>
      
      <div class="editor-container">
        <TiptapEditor
          ref="editorRef"
          v-model="content"
          :all-extensions="true"
          :editable="true"
          :toolbar="true"
          :bubble-menu="true"
          :floating-menu="true"
          placeholder="Type something to test the extensions..."
          :show-character-count="true"
          :character-limit="10000"
        />
      </div>
    </div>

    <div class="test-section">
      <h2>Debug Information</h2>
      <div class="debug-info">
        <h3>Editor Extensions:</h3>
        <ul>
          <li v-for="ext in editorExtensions" :key="ext">{{ ext }}</li>
        </ul>
        
        <h3>Console Errors:</h3>
        <div v-if="consoleErrors.length === 0" class="no-errors">No errors detected</div>
        <ul v-else>
          <li v-for="(error, index) in consoleErrors" :key="index" class="error">{{ error }}</li>
        </ul>
      </div>
    </div>

    <div class="test-section">
      <h2>Manual Tests</h2>
      <div class="test-buttons">
        <button @click="testDragHandle" class="test-btn">Test Drag Handle</button>
        <button @click="testClickMenu" class="test-btn">Test Click Menu</button>
        <button @click="refreshStatus" class="test-btn">Refresh Status</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import TiptapEditor from '@/components/tiptap/core/TipTapEditor.vue'

const editorRef = ref()
const content = ref({
  type: 'doc',
  content: [
    {
      type: 'paragraph',
      content: [{ type: 'text', text: 'This is a test paragraph. Hover over it to see the drag handle.' }]
    },
    {
      type: 'heading',
      attrs: { level: 2 },
      content: [{ type: 'text', text: 'This is a heading' }]
    },
    {
      type: 'paragraph',
      content: [{ type: 'text', text: 'Another paragraph for testing.' }]
    }
  ]
})

const dragHandleStatus = ref({ loaded: false })
const clickMenuStatus = ref({ loaded: false })
const dragHandleCount = ref(0)
const clickMenuCount = ref(0)
const editorExtensions = ref<string[]>([])
const consoleErrors = ref<string[]>([])

// Capture console errors
const originalError = console.error
console.error = function(...args) {
  consoleErrors.value.push(args.join(' '))
  originalError.apply(console, arguments)
}

const checkExtensionStatus = () => {
  if (!editorRef.value?.editor) return

  const editor = editorRef.value.editor
  const extensions = editor.extensionManager.extensions

  // Check if extensions are loaded
  dragHandleStatus.value.loaded = extensions.some(ext => ext.name === 'dragHandle')
  clickMenuStatus.value.loaded = extensions.some(ext => ext.name === 'clickMenu')
  
  // Get all extension names
  editorExtensions.value = extensions.map(ext => ext.name)
}

const checkDOMElements = () => {
  dragHandleCount.value = document.querySelectorAll('.drag-handle').length
  clickMenuCount.value = document.querySelectorAll('.click-menu').length
}

const refreshStatus = () => {
  checkExtensionStatus()
  checkDOMElements()
}

const testDragHandle = () => {
  const paragraphs = document.querySelectorAll('.ProseMirror p')
  if (paragraphs.length > 0) {
    const firstP = paragraphs[0] as HTMLElement

    // Simulate mousemove over the paragraph
    const mouseMoveEvent = new MouseEvent('mousemove', {
      bubbles: true,
      cancelable: true,
      view: window,
      clientX: firstP.getBoundingClientRect().left - 20,
      clientY: firstP.getBoundingClientRect().top + 10
    })

    document.dispatchEvent(mouseMoveEvent)

    setTimeout(() => {
      checkDOMElements()
      console.log('Drag handle test completed')
      console.log('Drag handles found:', document.querySelectorAll('.drag-handle').length)
    }, 300)
  }
}

const testClickMenu = () => {
  const paragraphs = document.querySelectorAll('.ProseMirror p')
  if (paragraphs.length > 0) {
    const firstP = paragraphs[0] as HTMLElement

    // Simulate mousemove over the paragraph to trigger click menu
    const mouseMoveEvent = new MouseEvent('mousemove', {
      bubbles: true,
      cancelable: true,
      view: window,
      clientX: firstP.getBoundingClientRect().left - 30,
      clientY: firstP.getBoundingClientRect().top + 10
    })

    document.dispatchEvent(mouseMoveEvent)

    setTimeout(() => {
      checkDOMElements()
      console.log('Click menu test completed')
      console.log('Click menus found:', document.querySelectorAll('.click-menu').length)
    }, 300)
  }
}

onMounted(() => {
  nextTick(() => {
    setTimeout(() => {
      refreshStatus()
    }, 1000)
  })
})
</script>

<style scoped>
.test-extensions-page {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.test-section {
  margin: 30px 0;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  background: #fafafa;
}

.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px;
  background: white;
  border-radius: 6px;
  border: 1px solid #ddd;
}

.label {
  font-weight: 500;
}

.status.success {
  color: #22c55e;
  font-weight: 600;
}

.status.error {
  color: #ef4444;
  font-weight: 600;
}

.status.warning {
  color: #f59e0b;
  font-weight: 600;
}

.status.info {
  color: #3b82f6;
  font-weight: 600;
}

.instructions {
  background: #e3f2fd;
  padding: 15px;
  border-radius: 6px;
  border-left: 4px solid #2196f3;
  margin-bottom: 20px;
}

.editor-container {
  border: 2px solid #ddd;
  border-radius: 8px;
  min-height: 300px;
}

.debug-info {
  background: white;
  padding: 15px;
  border-radius: 6px;
  border: 1px solid #ddd;
}

.debug-info h3 {
  margin: 15px 0 10px 0;
  color: #333;
}

.debug-info ul {
  margin: 0;
  padding-left: 20px;
}

.debug-info li {
  margin: 5px 0;
}

.debug-info li.error {
  color: #ef4444;
  font-family: monospace;
}

.no-errors {
  color: #22c55e;
  font-weight: 500;
}

.test-buttons {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.test-btn {
  padding: 10px 20px;
  background: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: background 0.2s;
}

.test-btn:hover {
  background: #2563eb;
}
</style>
