import type { Node as ProseMirrorNode } from '@tiptap/pm/model'
import type { ResolvedPos } from '@tiptap/pm/model'
import type { 
  NodeTypeConfig, 
  NodeValidationResult, 
  NestingValidation, 
  ContentPreservation 
} from '../types'

/**
 * Node validation utilities for multi-node type drag operations
 */
export class NodeValidationUtils {
  /**
   * Validate if a source node can be moved to a target position
   */
  static validateNodeMovement(
    sourceNode: ProseMirrorNode,
    targetNode: ProseMirrorNode,
    sourceConfig: NodeTypeConfig,
    targetConfig: NodeTypeConfig,
    $targetPos: ResolvedPos
  ): NodeValidationResult {
    // Check basic type compatibility
    if (!sourceConfig.canDragInto.includes(targetNode.type.name)) {
      return {
        isValid: false,
        reason: `${sourceNode.type.name} cannot be dragged into ${targetNode.type.name}`
      }
    }

    if (!targetConfig.canReceiveFrom.includes(sourceNode.type.name)) {
      return {
        isValid: false,
        reason: `${targetNode.type.name} cannot receive ${sourceNode.type.name}`
      }
    }

    // Validate nesting rules
    const nestingValidation = this.validateNesting(sourceNode, sourceConfig, $targetPos)
    if (!nestingValidation.canNest) {
      return {
        isValid: false,
        reason: this.getNestingErrorMessage(nestingValidation),
        suggestedPosition: nestingValidation.suggestedAlternative?.position
      }
    }

    return { isValid: true }
  }

  /**
   * Validate nesting rules for a node at a specific position
   */
  static validateNesting(
    node: ProseMirrorNode,
    config: NodeTypeConfig,
    $pos: ResolvedPos
  ): NestingValidation {
    const result: NestingValidation = {
      canNest: true,
      maxDepthExceeded: false,
      invalidParent: false,
      invalidChild: false
    }

    const { nestingRules } = config

    // Check allowed parents
    if (nestingRules.allowedParents && 
        !nestingRules.allowedParents.includes($pos.parent.type.name)) {
      result.canNest = false
      result.invalidParent = true
    }

    // Check maximum depth
    if (nestingRules.maxDepth && $pos.depth > nestingRules.maxDepth) {
      result.canNest = false
      result.maxDepthExceeded = true
      
      // Suggest alternative position at allowed depth
      result.suggestedAlternative = {
        position: this.findPositionAtDepth($pos, nestingRules.maxDepth)
      }
    }

    // Check allowed children (for complex scenarios)
    if (nestingRules.allowedChildren && nestingRules.allowedChildren.length > 0) {
      // This would need more complex validation based on the node's content
      // For now, we'll assume it's valid if allowedChildren is specified
    }

    return result
  }

  /**
   * Preserve node content and attributes during movement
   */
  static preserveNodeContent(
    node: ProseMirrorNode,
    config: NodeTypeConfig
  ): ContentPreservation {
    const preservation: ContentPreservation = {
      attributes: {},
      marks: [...(node.marks || [])],
      content: node.content ? node.content.toJSON() : [],
      metadata: {}
    }

    // Preserve specified attributes
    config.preserveAttributes.forEach(attr => {
      if (attr.endsWith('*')) {
        // Handle wildcard attributes like 'data-*'
        const prefix = attr.slice(0, -1)
        Object.keys(node.attrs || {}).forEach(key => {
          if (key.startsWith(prefix)) {
            preservation.attributes[key] = node.attrs[key]
          }
        })
      } else if (node.attrs && node.attrs[attr] !== undefined) {
        preservation.attributes[attr] = node.attrs[attr]
      }
    })

    // Store additional metadata
    preservation.metadata = {
      originalType: node.type.name,
      originalSize: node.nodeSize,
      hasContent: node.content && node.content.size > 0
    }

    return preservation
  }

  /**
   * Create a new node with preserved content
   */
  static createPreservedNode(
    originalNode: ProseMirrorNode,
    preservation: ContentPreservation
  ): ProseMirrorNode {
    // Merge original attributes with preserved ones
    const mergedAttrs = {
      ...originalNode.attrs,
      ...preservation.attributes
    }

    // Create new node with preserved attributes and content
    return originalNode.type.create(
      mergedAttrs,
      originalNode.content,
      preservation.marks
    )
  }

  /**
   * Check if two node types are compatible for swapping
   */
  static areNodesCompatible(
    node1: ProseMirrorNode,
    node2: ProseMirrorNode,
    config1: NodeTypeConfig,
    config2: NodeTypeConfig
  ): boolean {
    // Check if node1 can go where node2 is
    const canSwap1 = config1.canDragInto.includes(node2.type.name) &&
                     config2.canReceiveFrom.includes(node1.type.name)

    // Check if node2 can go where node1 is
    const canSwap2 = config2.canDragInto.includes(node1.type.name) &&
                     config1.canReceiveFrom.includes(node2.type.name)

    return canSwap1 && canSwap2
  }

  /**
   * Get supported node types for a given node
   */
  static getSupportedTargets(
    sourceNode: ProseMirrorNode,
    config: NodeTypeConfig
  ): string[] {
    return config.canDragInto
  }

  /**
   * Get nodes that can be dragged into this node
   */
  static getSupportedSources(
    targetNode: ProseMirrorNode,
    config: NodeTypeConfig
  ): string[] {
    return config.canReceiveFrom
  }

  /**
   * Find position at specific depth
   */
  private static findPositionAtDepth($pos: ResolvedPos, maxDepth: number): number {
    let currentPos = $pos
    while (currentPos.depth > maxDepth && currentPos.depth > 0) {
      currentPos = currentPos.doc.resolve(currentPos.before(currentPos.depth))
    }
    return currentPos.pos
  }

  /**
   * Get human-readable nesting error message
   */
  private static getNestingErrorMessage(validation: NestingValidation): string {
    if (validation.maxDepthExceeded) {
      return 'Maximum nesting depth exceeded'
    }
    if (validation.invalidParent) {
      return 'Cannot be nested in this parent element'
    }
    if (validation.invalidChild) {
      return 'Cannot contain this type of child element'
    }
    return 'Nesting rules violation'
  }

  /**
   * Check if a node type supports specific features
   */
  static supportsFeature(
    nodeType: string,
    config: NodeTypeConfig,
    feature: 'drag' | 'drop' | 'nesting' | 'transformation'
  ): boolean {
    switch (feature) {
      case 'drag':
        return config.canDragInto.length > 0
      case 'drop':
        return config.canReceiveFrom.length > 0
      case 'nesting':
        return !!config.nestingRules && (
          !!config.nestingRules.allowedChildren ||
          !!config.nestingRules.allowedParents ||
          !!config.nestingRules.maxDepth
        )
      case 'transformation':
        return config.preserveAttributes.length > 0
      default:
        return false
    }
  }

  /**
   * Get node type configuration by selector
   */
  static getConfigBySelector(
    selector: string,
    configs: Record<string, NodeTypeConfig>
  ): NodeTypeConfig | null {
    return Object.values(configs).find(config => 
      config.selector.split(', ').some(sel => 
        sel.trim() === selector.trim()
      )
    ) || null
  }

  /**
   * Validate entire drag operation
   */
  static validateDragOperation(
    sourceNode: ProseMirrorNode,
    targetNode: ProseMirrorNode,
    sourceConfig: NodeTypeConfig,
    targetConfig: NodeTypeConfig,
    $sourcePos: ResolvedPos,
    $targetPos: ResolvedPos
  ): NodeValidationResult {
    // Basic compatibility check
    const compatibilityResult = this.validateNodeMovement(
      sourceNode, targetNode, sourceConfig, targetConfig, $targetPos
    )
    
    if (!compatibilityResult.isValid) {
      return compatibilityResult
    }

    // Check if this would create circular references or invalid structures
    if (this.wouldCreateCircularReference($sourcePos, $targetPos)) {
      return {
        isValid: false,
        reason: 'Operation would create circular reference'
      }
    }

    // All validations passed
    return { isValid: true }
  }

  /**
   * Check for circular references in drag operations
   */
  private static wouldCreateCircularReference(
    $sourcePos: ResolvedPos,
    $targetPos: ResolvedPos
  ): boolean {
    // Check if target is within source (would create circular reference)
    let currentPos = $targetPos
    while (currentPos.depth > 0) {
      if (currentPos.pos >= $sourcePos.start() && currentPos.pos <= $sourcePos.end()) {
        return true
      }
      currentPos = currentPos.doc.resolve(currentPos.before(currentPos.depth))
    }
    return false
  }
}