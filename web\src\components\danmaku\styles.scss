.vue-danmaku {
  position: relative;
  overflow: hidden;

  .danmus {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    opacity: 0;
    transition: all 0.3s;
    transform: translateZ(0);
    backface-visibility: hidden;

    &.show {
      opacity: 1;
    }

    &.paused {
      .dm.move {
        animation-play-state: paused;
      }
    }

    .dm {
      position: absolute;
      font-size: 1.25rem;
      color: var(--white-2);
      white-space: pre;
      transform: translate3d(0, 0, 0);
      will-change: transform;
      height: 3rem;
      display: flex;
      align-items: center;

      &.move {
        animation-name: move-left;
        animation-timing-function: linear;
        animation-play-state: running;
      }

      &.pause {
        animation-play-state: paused;
        z-index: 100;
      }
    }

    @keyframes move-left {
      from {
        transform: translateX(0);
      }

      to {
        transform: translateX(var(--dm-scroll-width));
      }
    }
  }
}
