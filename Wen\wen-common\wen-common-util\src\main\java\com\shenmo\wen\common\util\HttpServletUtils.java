package com.shenmo.wen.common.util;

import com.shenmo.wen.common.exception.BaseException;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnumOption;
import com.shenmo.wen.common.pojo.domain.VirtualCopyRequest;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.Objects;

/**
 * HttpServlet工具类，获取当前request和response
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public abstract class HttpServletUtils {

    /**
     * 获取当前请求的request对象
     *
     * <AUTHOR>
     */
    @NonNull
    public static HttpServletRequest getRequest() {

        final ServletRequestAttributes attributes = getServletRequestAttributes();
        return Objects.nonNull(attributes) ? attributes.getRequest() : new VirtualCopyRequest(null);
    }

    /**
     * 获取servlet 请求和 HTTP 会话范围访问对象
     *
     * @return servlet 请求和 HTTP 会话范围访问对象
     * <AUTHOR>
     */
    public static ServletRequestAttributes getServletRequestAttributes() {

        return (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
    }

    /**
     * 设置servlet 请求属性
     *
     * @param attributes 请求和 HTTP 会话范围访问对象
     * <AUTHOR>
     */
    public static void setServletRequestAttributes(@Nullable RequestAttributes attributes) {

        RequestContextHolder.setRequestAttributes(attributes);
    }

    /**
     * 获取当前请求的response对象
     *
     * <AUTHOR>
     */
    public static HttpServletResponse getResponse() {
        ServletRequestAttributes requestAttributes = (ServletRequestAttributes) RequestContextHolder.getRequestAttributes();
        if (requestAttributes == null) {
            throw new BaseException(ExceptionEnumOption.of(HttpStatus.BAD_REQUEST, "无效的请求"));
        } else {
            return requestAttributes.getResponse();
        }
    }

    /**
     * 判定servlet是否可用
     *
     * @return servlet是否可用
     * <AUTHOR>
     */
    public static boolean available() {

        return Objects.nonNull(getServletRequestAttributes());
    }

    /**
     * 根据HttpServletResponse构建OutputStream
     *
     * @param originName 原文件名
     * @return OutputStream
     * @throws IOException IOException
     * <AUTHOR>
     */
    public static ServletOutputStream buildFileOutputStream(String originName) throws IOException {

        HttpServletResponse response = getResponse();
        String fileName = URLEncoder.encode(originName, StandardCharsets.UTF_8);
        response.reset();
        response.setHeader(HttpHeaders.ACCESS_CONTROL_ALLOW_ORIGIN, "*");
        response.setHeader(HttpHeaders.ACCESS_CONTROL_EXPOSE_HEADERS, HttpHeaders.CONTENT_DISPOSITION);
        response.setHeader(HttpHeaders.CONTENT_DISPOSITION, "attachment; filename=" + fileName);
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        response.setCharacterEncoding(StandardCharsets.UTF_8.name());
        return response.getOutputStream();
    }

    /**
     * 是否为GET请求
     *
     * @param request 请求对象{@link HttpServletRequest}
     * @return 是否为GET请求
     */
    public static boolean isGetMethod(HttpServletRequest request) {
        return HttpMethod.GET.name().equalsIgnoreCase(request.getMethod());
    }

    /**
     * 是否为POST请求
     *
     * @param request 请求对象{@link HttpServletRequest}
     * @return 是否为POST请求
     */
    public static boolean isPostMethod(HttpServletRequest request) {
        return HttpMethod.POST.name().equalsIgnoreCase(request.getMethod());
    }
}
