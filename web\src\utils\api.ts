import { type AxiosRequestConfig, type AxiosError } from 'axios'

import service from '@/config/request'
import { API } from '@/constants/frequency_key.constants'
import { type ResponseData } from '@/types/response_data.types'
import frequencyLimit from '@/utils/frequency-limit'
import Message from '@/utils/message'

// 定义通用的请求参数类型
type RequestParams = Record<string, string | number | boolean | null | undefined>

const api = {
  handleError: (err: AxiosError | null | undefined): ResponseData<unknown> => {
    let data = {} as ResponseData<unknown>

    // 处理空值情况
    if (!err) {
      return data
    }

    // 如果是请求取消，直接返回空数据
    if (err.name === 'CanceledError' || err.message === 'canceled') {
      return data
    }

    if (err?.response) {
      data = err.response.data as ResponseData<unknown>
      const code = data.code
      // 只有在非取消请求的情况下才显示错误提示
      frequencyLimit.debounce(API + code, () => {
        Message.warning(data.message || '网络请求失败')
      })
    } else if (err?.request) {
      // 处理网络错误（如断网）
      frequencyLimit.debounce(API + 'network_error', () => {
        Message.warning('网络连接失败，请检查网络设置')
      })
    } else {
      // 其他错误
      frequencyLimit.debounce(API + 'unknown_error', () => {
        Message.warning('请求发生错误，请稍后重试')
      })
    }
    return data
  },

  // GET 请求
  get: <T>(
    url: string,
    params?: RequestParams,
    config?: AxiosRequestConfig,
  ): Promise<ResponseData<T>> => {
    return service.get(url, { ...config, params })
  },

  // POST 请求
  post: <T>(url: string, data?: RequestParams): Promise<ResponseData<T>> => {
    return service.post(url, data)
  },

  // POST FormData 请求
  postFormData: <T>(
    url: string,
    data?: FormData,
    config?: AxiosRequestConfig,
  ): Promise<ResponseData<T>> => {
    return service.post(url, data, config)
  },

  // PUT 请求
  put: <T>(url: string, data?: RequestParams): Promise<ResponseData<T>> => {
    return service.put(url, data)
  },

  // PUT FormData 请求
  putFormData: <T>(
    url: string,
    data?: FormData,
    config?: AxiosRequestConfig,
  ): Promise<ResponseData<T>> => {
    return service.put(url, data, config)
  },

  // DELETE 请求
  del: <T>(url: string, params?: RequestParams): Promise<ResponseData<T>> => {
    return service.delete(url, { params })
  },
}

export default api
