const BASE_URL =
  window.location.hostname == 'localhost'
    ? `${window.location.protocol}//localhost:20001`
    : `${window.location.protocol}//${window.location.host}/api`
const config = {
  logLevel: import.meta.env.VITE_LOG_LEVEL,
  cloudflareTurnstileSecret: import.meta.env.VITE_CLOUDFLARE_TURNSTILE_SECRET,
  backend: {
    baseURL: BASE_URL,
    wsURL: `${BASE_URL}/core/ws`,
    resourcePrefix: `/osr`,
    resourceURL: `${BASE_URL}/osr`,
  },
}

console.log(config)
export default config
