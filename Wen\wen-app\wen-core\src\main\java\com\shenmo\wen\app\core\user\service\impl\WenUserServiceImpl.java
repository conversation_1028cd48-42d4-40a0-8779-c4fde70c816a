package com.shenmo.wen.app.core.user.service.impl;

import cn.dev33.satoken.stp.StpUtil;

import com.shenmo.wen.app.core.file.controller.service.WenFileService;
import com.shenmo.wen.app.core.user.service.WenUserService;
import com.shenmo.wen.common.constant.BucketConstant;
import com.shenmo.wen.common.constant.RedisKeyConstant;
import com.shenmo.wen.common.util.IpUtils;
import com.shenmo.wen.common.util.spring.SpringRedisUtils;
import com.shenmo.wen.modules.user.mapper.WenUserMapper;
import com.shenmo.wen.modules.user.pojo.domain.WenSearchUser;
import com.shenmo.wen.modules.user.pojo.entity.WenUser;
import com.shenmo.wen.modules.user.pojo.vo.WenUserVo;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
public class WenUserServiceImpl implements WenUserService {
    private final WenUserMapper mapper;
    private final WenFileService fileService;

    @Override
    public Long online() {
        return Optional.ofNullable(SpringRedisUtils.forSet().size(RedisKeyConstant.USER_ONLINE)).orElse(0L);
    }

    @Override
    public WenUserVo info() {
        final long loginId = StpUtil.getLoginIdAsLong();
        final WenUser user = mapper.byId(loginId);
        final WenUserVo userVo = new WenUserVo();
        BeanUtils.copyProperties(user, userVo);
        final String ip = IpUtils.getIp();
        userVo.setIpLocation(IpUtils.getIpLocation(ip));
        return userVo;
    }

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public String changeAvatar(MultipartFile avatarFile) throws IOException {
        final long loginId = StpUtil.getLoginIdAsLong();
        return fileService.save(BucketConstant.AVATAR, avatarFile, fileUri -> mapper.updateAvatarById(loginId, fileUri));
    }

    @Override
    public boolean updateNotificationReceiveType(Integer notificationReceiveType) {
        final long loginId = StpUtil.getLoginIdAsLong();
        return mapper.updateNotificationReceiveTypeById(loginId, notificationReceiveType);
    }

    @Override
    public List<WenSearchUser> search(String username) {
        return mapper.searchByUsernameLike(username);
    }
}
