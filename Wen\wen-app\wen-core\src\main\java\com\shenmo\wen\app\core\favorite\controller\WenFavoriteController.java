package com.shenmo.wen.app.core.favorite.controller;

import com.shenmo.wen.app.core.favorite.pojo.param.WenFavoriteParam;
import com.shenmo.wen.app.core.favorite.pojo.vo.WenFavoriteCount;
import com.shenmo.wen.app.core.favorite.service.WenFavoriteService;
import com.shenmo.wen.app.core.interaction.pojo.entity.WenInteraction;
import com.shenmo.wen.common.pojo.response.ResponseData;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("favorite")
@RequiredArgsConstructor
public class WenFavoriteController {
    private final WenFavoriteService service;

    @GetMapping
    public ResponseData<WenInteraction> load() {
        return ResponseData.success();
    }

    @PostMapping
    public ResponseData<WenFavoriteCount> save(@RequestBody WenFavoriteParam param) {
        return ResponseData.success(service.save(param));
    }
}
