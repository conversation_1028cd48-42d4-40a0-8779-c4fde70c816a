// 拖拽测试工具函数
export function testDragEvents() {
  console.log('开始测试拖拽事件处理...')
  
  // 模拟触摸事件
  const simulateTouchEvent = (type: string, element: HTMLElement) => {
    const event = new TouchEvent(type, {
      bubbles: true,
      cancelable: true,
      touches: type === 'touchend' ? [] : [
        new Touch({
          identifier: 0,
          target: element,
          clientX: 100,
          clientY: 100,
          radiusX: 10,
          radiusY: 10,
          rotationAngle: 0,
          force: 1
        })
      ],
      changedTouches: [
        new Touch({
          identifier: 0,
          target: element,
          clientX: 100,
          clientY: 100,
          radiusX: 10,
          radiusY: 10,
          rotationAngle: 0,
          force: 1
        })
      ]
    })
    
    element.dispatchEvent(event)
  }
  
  // 测试函数
  const testTouchSequence = () => {
    const testElement = document.createElement('div')
    document.body.appendChild(testElement)
    
    console.log('模拟 touchstart...')
    simulateTouchEvent('touchstart', testElement)
    
    setTimeout(() => {
      console.log('模拟 touchend...')
      simulateTouchEvent('touchend', testElement)
    }, 600) // 超过长按时间
    
    setTimeout(() => {
      document.body.removeChild(testElement)
      console.log('测试完成')
    }, 1000)
  }
  
  return { testTouchSequence, simulateTouchEvent }
} 