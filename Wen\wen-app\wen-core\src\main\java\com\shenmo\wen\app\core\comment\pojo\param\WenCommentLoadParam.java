package com.shenmo.wen.app.core.comment.pojo.param;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class WenCommentLoadParam {

    private Long id;

    @NotNull(message = "文章id不可为空")
    private Long articleId;

    /**
     * 父评论ID，如果该评论是回复其他评论的，则记录被回复评论的ID，可为空，表示是直接对文章的评论，而非回复其他评论。
     */
    private Long parentCommentId;

    private Integer loadSize = 5;

    /**
     * 排序类型：0-按点赞数排序（默认），1-按时间排序，2-按回复数排序
     */
    private Integer sortType = 0;
}
