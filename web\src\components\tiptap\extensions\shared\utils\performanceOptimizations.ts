/**
 * Performance optimization utilities for TipTap extensions
 * Provides throttling, debouncing, and memory management utilities
 */

/**
 * Throttle function to limit the rate of function execution
 * @param func Function to throttle
 * @param limit Time limit in milliseconds
 * @returns Throttled function
 */
export function throttle<T extends (...args: any[]) => any>(
  func: T,
  limit: number
): (...args: Parameters<T>) => void {
  let inThrottle: boolean
  return function (this: any, ...args: Parameters<T>) {
    if (!inThrottle) {
      func.apply(this, args)
      inThrottle = true
      setTimeout(() => (inThrottle = false), limit)
    }
  }
}

/**
 * Debounce function to delay function execution until after delay
 * @param func Function to debounce
 * @param delay Delay in milliseconds
 * @returns Debounced function
 */
export function debounce<T extends (...args: any[]) => any>(
  func: T,
  delay: number
): (...args: Parameters<T>) => void {
  let timeoutId: NodeJS.Timeout
  return function (this: any, ...args: Parameters<T>) {
    clearTimeout(timeoutId)
    timeoutId = setTimeout(() => func.apply(this, args), delay)
  }
}

/**
 * Request animation frame throttle for smooth animations
 * @param func Function to throttle
 * @returns RAF throttled function
 */
export function rafThrottle<T extends (...args: any[]) => any>(
  func: T
): (...args: Parameters<T>) => void {
  let rafId: number | null = null
  return function (this: any, ...args: Parameters<T>) {
    if (rafId === null) {
      rafId = requestAnimationFrame(() => {
        func.apply(this, args)
        rafId = null
      })
    }
  }
}

/**
 * Memory management utilities
 */
export class MemoryManager {
  private static domReferences = new WeakMap<object, Set<HTMLElement>>()
  private static eventListeners = new WeakMap<object, Set<() => void>>()
  private static timers = new WeakMap<object, Set<NodeJS.Timeout>>()

  /**
   * Track DOM reference for cleanup
   * @param owner Owner object
   * @param element DOM element to track
   */
  static trackDOMReference(owner: object, element: HTMLElement): void {
    if (!this.domReferences.has(owner)) {
      this.domReferences.set(owner, new Set())
    }
    this.domReferences.get(owner)!.add(element)
  }

  /**
   * Track event listener for cleanup
   * @param owner Owner object
   * @param cleanup Cleanup function
   */
  static trackEventListener(owner: object, cleanup: () => void): void {
    if (!this.eventListeners.has(owner)) {
      this.eventListeners.set(owner, new Set())
    }
    this.eventListeners.get(owner)!.add(cleanup)
  }

  /**
   * Track timer for cleanup
   * @param owner Owner object
   * @param timer Timer to track
   */
  static trackTimer(owner: object, timer: NodeJS.Timeout): void {
    if (!this.timers.has(owner)) {
      this.timers.set(owner, new Set())
    }
    this.timers.get(owner)!.add(timer)
  }

  /**
   * Clean up all tracked resources for an owner
   * @param owner Owner object
   */
  static cleanup(owner: object): void {
    // Clean up DOM references
    const domRefs = this.domReferences.get(owner)
    if (domRefs) {
      domRefs.forEach(element => {
        if (element.parentNode) {
          element.parentNode.removeChild(element)
        }
      })
      domRefs.clear()
    }

    // Clean up event listeners
    const listeners = this.eventListeners.get(owner)
    if (listeners) {
      listeners.forEach(cleanup => cleanup())
      listeners.clear()
    }

    // Clean up timers
    const timers = this.timers.get(owner)
    if (timers) {
      timers.forEach(timer => clearTimeout(timer))
      timers.clear()
    }
  }
}

/**
 * Performance monitoring utilities
 */
export class PerformanceMonitor {
  private static measurements = new Map<string, number[]>()

  /**
   * Start performance measurement
   * @param label Measurement label
   * @returns End function
   */
  static start(label: string): () => void {
    const startTime = performance.now()
    return () => {
      const endTime = performance.now()
      const duration = endTime - startTime
      
      if (!this.measurements.has(label)) {
        this.measurements.set(label, [])
      }
      this.measurements.get(label)!.push(duration)
    }
  }

  /**
   * Get performance statistics
   * @param label Measurement label
   * @returns Performance stats
   */
  static getStats(label: string): {
    count: number
    average: number
    min: number
    max: number
    total: number
  } | null {
    const measurements = this.measurements.get(label)
    if (!measurements || measurements.length === 0) {
      return null
    }

    const total = measurements.reduce((sum, time) => sum + time, 0)
    const average = total / measurements.length
    const min = Math.min(...measurements)
    const max = Math.max(...measurements)

    return {
      count: measurements.length,
      average,
      min,
      max,
      total,
    }
  }

  /**
   * Clear measurements for a label
   * @param label Measurement label
   */
  static clear(label: string): void {
    this.measurements.delete(label)
  }

  /**
   * Clear all measurements
   */
  static clearAll(): void {
    this.measurements.clear()
  }
}

/**
 * Optimized event handling for large documents
 */
export class OptimizedEventHandler {
  private static eventDelegates = new Map<string, {
    handler: (event: Event) => void
    selector: string
    element: HTMLElement
  }>()

  /**
   * Add delegated event listener for better performance
   * @param element Container element
   * @param eventType Event type
   * @param selector Target selector
   * @param handler Event handler
   * @param key Unique key for the handler
   */
  static addDelegatedListener(
    element: HTMLElement,
    eventType: string,
    selector: string,
    handler: (event: Event, target: HTMLElement) => void,
    key: string
  ): void {
    const delegatedHandler = (event: Event) => {
      const target = (event.target as HTMLElement).closest(selector)
      if (target && element.contains(target as Node)) {
        handler(event, target as HTMLElement)
      }
    }

    element.addEventListener(eventType, delegatedHandler)
    
    this.eventDelegates.set(key, {
      handler: delegatedHandler,
      selector,
      element,
    })
  }

  /**
   * Remove delegated event listener
   * @param eventType Event type
   * @param key Handler key
   */
  static removeDelegatedListener(eventType: string, key: string): void {
    const delegate = this.eventDelegates.get(key)
    if (delegate) {
      delegate.element.removeEventListener(eventType, delegate.handler)
      this.eventDelegates.delete(key)
    }
  }

  /**
   * Clean up all delegated listeners
   */
  static cleanup(): void {
    this.eventDelegates.forEach((delegate, key) => {
      // We need to know the event type to remove properly
      // This is a limitation of this approach - we'd need to store event type
      this.eventDelegates.delete(key)
    })
  }
}

/**
 * Virtual scrolling utilities for large document handling
 */
export class VirtualScrollManager {
  private container: HTMLElement
  private itemHeight: number
  private visibleRange: { start: number; end: number } = { start: 0, end: 0 }
  private totalItems: number = 0

  constructor(container: HTMLElement, itemHeight: number) {
    this.container = container
    this.itemHeight = itemHeight
  }

  /**
   * Calculate visible range based on scroll position
   * @param scrollTop Current scroll position
   * @param containerHeight Container height
   * @param buffer Buffer items to render outside visible area
   * @returns Visible range
   */
  calculateVisibleRange(
    scrollTop: number,
    containerHeight: number,
    buffer: number = 5
  ): { start: number; end: number } {
    const startIndex = Math.max(0, Math.floor(scrollTop / this.itemHeight) - buffer)
    const endIndex = Math.min(
      this.totalItems - 1,
      Math.ceil((scrollTop + containerHeight) / this.itemHeight) + buffer
    )

    this.visibleRange = { start: startIndex, end: endIndex }
    return this.visibleRange
  }

  /**
   * Get current visible range
   */
  getVisibleRange(): { start: number; end: number } {
    return this.visibleRange
  }

  /**
   * Set total number of items
   * @param count Total item count
   */
  setTotalItems(count: number): void {
    this.totalItems = count
  }

  /**
   * Get total height of all items
   */
  getTotalHeight(): number {
    return this.totalItems * this.itemHeight
  }
}

/**
 * Batch DOM operations for better performance
 */
export class DOMBatcher {
  private operations: (() => void)[] = []
  private scheduled = false

  /**
   * Add operation to batch
   * @param operation DOM operation function
   */
  add(operation: () => void): void {
    this.operations.push(operation)
    this.schedule()
  }

  /**
   * Schedule batch execution
   */
  private schedule(): void {
    if (!this.scheduled) {
      this.scheduled = true
      requestAnimationFrame(() => {
        this.flush()
      })
    }
  }

  /**
   * Execute all batched operations
   */
  private flush(): void {
    const ops = this.operations.splice(0)
    ops.forEach(op => op())
    this.scheduled = false
  }

  /**
   * Force immediate execution of all operations
   */
  flushSync(): void {
    const ops = this.operations.splice(0)
    ops.forEach(op => op())
    this.scheduled = false
  }
}

/**
 * Intersection Observer utilities for efficient visibility detection
 */
export class VisibilityManager {
  private static observers = new Map<string, IntersectionObserver>()
  private static callbacks = new Map<HTMLElement, (isVisible: boolean) => void>()

  /**
   * Observe element visibility
   * @param element Element to observe
   * @param callback Visibility change callback
   * @param options Intersection observer options
   * @param key Unique key for the observer
   */
  static observe(
    element: HTMLElement,
    callback: (isVisible: boolean) => void,
    options: IntersectionObserverInit = {},
    key: string = 'default'
  ): void {
    if (!this.observers.has(key)) {
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          const callback = this.callbacks.get(entry.target as HTMLElement)
          if (callback) {
            callback(entry.isIntersecting)
          }
        })
      }, options)
      
      this.observers.set(key, observer)
    }

    const observer = this.observers.get(key)!
    this.callbacks.set(element, callback)
    observer.observe(element)
  }

  /**
   * Stop observing element
   * @param element Element to unobserve
   * @param key Observer key
   */
  static unobserve(element: HTMLElement, key: string = 'default'): void {
    const observer = this.observers.get(key)
    if (observer) {
      observer.unobserve(element)
      this.callbacks.delete(element)
    }
  }

  /**
   * Clean up all observers
   */
  static cleanup(): void {
    this.observers.forEach(observer => observer.disconnect())
    this.observers.clear()
    this.callbacks.clear()
  }
}

/**
 * Export all utilities as a single object for convenience
 */
export const PerformanceUtils = {
  throttle,
  debounce,
  rafThrottle,
  MemoryManager,
  PerformanceMonitor,
  OptimizedEventHandler,
  VirtualScrollManager,
  DOMBatcher,
  VisibilityManager,
}

export default PerformanceUtils