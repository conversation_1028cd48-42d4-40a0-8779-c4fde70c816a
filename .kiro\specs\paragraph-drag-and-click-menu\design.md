# Design Document

## Overview

This design implements paragraph dragging and click menu functionality for the TipTap editor, inspired by modern rich text editors like Notion. The solution consists of two main extensions: a **DragHandle Extension** for paragraph reordering and a **ClickMenu Extension** for contextual actions. Both extensions integrate seamlessly with the existing TipTap architecture and follow established patterns from the current codebase.

The implementation leverages TipTap's plugin system, Vue 3 composition API, and follows the existing architectural patterns found in extensions like SlashMenu and Image. The design prioritizes performance, accessibility, and maintainability while providing an intuitive user experience.

## Architecture

### High-Level Architecture

```mermaid
graph TB
    A[TipTap Editor Core] --> B[DragHandle Extension]
    A --> C[ClickMenu Extension]
    
    B --> D[DragHandle Plugin]
    B --> E[DragHandle View]
    B --> F[Drag Event Manager]
    
    C --> G[ClickMenu Plugin]
    C --> H[ClickMenu View]
    C --> I[Menu Item Registry]
    
    D --> J[ProseMirror State]
    G --> J
    
    E --> K[Vue Components]
    H --> K
    
    F --> L[DOM Event Handlers]
    I --> M[Action Handlers]
```

### Extension Integration

Both extensions follow the established TipTap extension pattern:

1. **Extension Definition**: Using `Extension.create()` with proper configuration options
2. **Plugin Registration**: Adding ProseMirror plugins via `addProseMirrorPlugins()`
3. **Vue Integration**: Using Vue components for UI elements following the existing pattern
4. **Event Management**: Leveraging the existing event optimization system
5. **Storage Integration**: Using extension storage for state management

### File Structure

```
web/src/components/tiptap/extensions/
├── drag-handle/
│   ├── index.ts                    # Main export
│   ├── DragHandleExtension.ts      # Extension definition
│   ├── DragHandlePlugin.ts         # ProseMirror plugin
│   ├── DragHandleView.vue          # Vue component for drag handle UI
│   ├── DragEventManager.ts         # Drag operation logic
│   ├── composables/
│   │   ├── useDragOperations.ts    # Drag logic composable
│   │   └── useDragVisualFeedback.ts # Visual feedback composable
│   └── styles/
│       └── drag-handle.scss        # Styling
└── click-menu/
    ├── index.ts                    # Main export
    ├── ClickMenuExtension.ts       # Extension definition
    ├── ClickMenuPlugin.ts          # ProseMirror plugin
    ├── ClickMenuView.vue           # Vue component for menu UI
    ├── MenuItemRegistry.ts         # Menu item management
    ├── composables/
    │   ├── useClickMenuLogic.ts    # Menu logic composable
    │   └── useMenuPositioning.ts   # Positioning logic composable
    ├── types.ts                    # TypeScript interfaces
    └── styles/
        └── click-menu.scss         # Styling
```

## Components and Interfaces

### DragHandle Extension

#### Core Interfaces

```typescript
interface DragHandleOptions {
  enabled: boolean
  supportedNodes: string[]
  dragHandleSelector: string
  ghostImageOpacity: number
  animationDuration: number
  keyboardShortcuts: Record<string, string>
}

interface DragOperation {
  sourceNode: ProseMirrorNode
  sourcePos: number
  targetPos: number | null
  dragElement: HTMLElement
  ghostImage: HTMLElement
  isActive: boolean
}

interface DropZone {
  element: HTMLElement
  position: number
  isValid: boolean
  visualIndicator: HTMLElement
}
```

#### DragHandleExtension.ts

The main extension follows the established pattern from SlashMenuExtension:

```typescript
export const DragHandleExtension = Extension.create<DragHandleOptions>({
  name: 'dragHandle',
  
  addOptions() {
    return {
      enabled: true,
      supportedNodes: ['paragraph', 'heading', 'listItem', 'codeBlock'],
      dragHandleSelector: '.drag-handle',
      ghostImageOpacity: 0.5,
      animationDuration: 200,
      keyboardShortcuts: {
        'Alt-ArrowUp': 'moveParagraphUp',
        'Alt-ArrowDown': 'moveParagraphDown'
      }
    }
  },

  addProseMirrorPlugins() {
    return [new DragHandlePlugin(this.options)]
  },

  addCommands() {
    return {
      moveParagraphUp: () => ({ commands }) => {
        // Implementation for keyboard shortcuts
      },
      moveParagraphDown: () => ({ commands }) => {
        // Implementation for keyboard shortcuts
      }
    }
  }
})
```

#### DragHandlePlugin.ts

ProseMirror plugin managing drag operations:

```typescript
export class DragHandlePlugin extends Plugin {
  constructor(options: DragHandleOptions) {
    super({
      key: new PluginKey('dragHandle'),
      
      view(editorView) {
        return new DragHandlePluginView(editorView, options)
      },
      
      props: {
        handleDOMEvents: {
          dragstart: this.handleDragStart.bind(this),
          dragover: this.handleDragOver.bind(this),
          drop: this.handleDrop.bind(this),
          dragend: this.handleDragEnd.bind(this)
        }
      }
    })
  }
}
```

### ClickMenu Extension

#### Core Interfaces

```typescript
interface ClickMenuOptions {
  enabled: boolean
  menuItems: ClickMenuItem[]
  positioning: 'auto' | 'left' | 'right'
  showDelay: number
  hideDelay: number
  keyboardNavigation: boolean
}

interface ClickMenuItem {
  id: string
  label: string
  icon: string
  action: (editor: Editor, node: ProseMirrorNode, pos: number) => void
  isVisible: (node: ProseMirrorNode) => boolean
  isEnabled: (node: ProseMirrorNode) => boolean
  shortcut?: string
  group?: string
}

interface MenuState {
  visible: boolean
  targetNode: ProseMirrorNode | null
  targetPos: number | null
  position: { x: number; y: number }
  selectedIndex: number
}
```

#### ClickMenuExtension.ts

```typescript
export const ClickMenuExtension = Extension.create<ClickMenuOptions>({
  name: 'clickMenu',
  
  addOptions() {
    return {
      enabled: true,
      menuItems: createDefaultMenuItems(),
      positioning: 'auto',
      showDelay: 100,
      hideDelay: 300,
      keyboardNavigation: true
    }
  },

  addProseMirrorPlugins() {
    return [new ClickMenuPlugin(this.options)]
  }
})
```

## Data Models

### Drag Operation State

```typescript
interface DragState {
  operation: DragOperation | null
  dropZones: DropZone[]
  visualFeedback: {
    ghostImage: HTMLElement | null
    dropIndicators: HTMLElement[]
    dragPreview: HTMLElement | null
  }
}
```

### Menu State Management

```typescript
interface ClickMenuState {
  activeMenu: MenuState | null
  menuHistory: MenuState[]
  keyboardFocus: {
    enabled: boolean
    selectedIndex: number
    navigationMode: 'mouse' | 'keyboard'
  }
}
```

### Node Position Tracking

```typescript
interface NodePosition {
  node: ProseMirrorNode
  pos: number
  depth: number
  parent: ProseMirrorNode | null
  index: number
  element: HTMLElement
}
```

## Error Handling

### Drag Operation Error Handling

1. **Invalid Drop Targets**: Gracefully handle drops on invalid locations by returning content to original position
2. **Transaction Conflicts**: Use proper transaction merging to handle concurrent editor operations
3. **DOM Manipulation Errors**: Wrap DOM operations in try-catch blocks with fallback behaviors
4. **Memory Leaks**: Proper cleanup of event listeners and DOM references

```typescript
class DragErrorHandler {
  static handleInvalidDrop(operation: DragOperation): void {
    // Animate back to original position
    // Clean up visual indicators
    // Reset editor state
  }

  static handleTransactionConflict(tr: Transaction): Transaction {
    // Merge conflicting transactions
    // Preserve user intent
    // Maintain document consistency
  }
}
```

### Menu Error Handling

1. **Positioning Failures**: Fallback positioning strategies when menu goes off-screen
2. **Action Execution Errors**: Graceful handling of failed menu actions with user feedback
3. **State Synchronization**: Ensure menu state stays synchronized with editor state

```typescript
class MenuErrorHandler {
  static handlePositioningError(menu: MenuState, viewport: DOMRect): MenuState {
    // Calculate fallback positions
    // Ensure menu stays within viewport
    // Adjust for mobile devices
  }

  static handleActionError(action: ClickMenuItem, error: Error): void {
    // Log error details
    // Show user-friendly message
    // Maintain editor stability
  }
}
```

## Enhanced Keyboard Accessibility Implementation

### Completed Features

The keyboard accessibility system has been significantly enhanced with the following features:

#### Enhanced Keyboard Shortcuts
- **Alt + 1-6**: Direct conversion to heading levels 1-6
- **Alt + d**: Duplicate current node
- **Alt + Delete**: Delete current node
- **Alt + m**: Alternative menu activation shortcut
- **Alt + Shift + ↑/↓**: Move to document top/bottom

#### Improved Screen Reader Support
- **Context-aware announcements**: Position information (e.g., "第2项，共5项")
- **Operation feedback**: Detailed success/failure messages
- **Enhanced ARIA attributes**: Better semantic markup for assistive technologies
- **Keyboard help system**: Built-in help announcements for shortcuts

#### Advanced Menu Navigation
- **Quick transformation keys**: 1-6 for headings, p/c/q/l for content types
- **Enhanced navigation**: Home/End keys for first/last item navigation
- **Improved focus management**: Better focus handling during operations
- **Group-aware navigation**: Support for menu item grouping

### Implementation Details

```typescript
// Enhanced keyboard shortcut mapping
static getEnhancedDragKeyboardShortcuts(): Record<string, string> {
  return {
    'Alt-1': 'convertToHeading1',
    'Alt-2': 'convertToHeading2',
    'Alt-3': 'convertToHeading3',
    'Alt-d': 'duplicateNode',
    'Alt-Delete': 'deleteNode',
    'Alt-m': 'showClickMenu',
    // ... additional shortcuts
  }
}

// Context-aware announcements
static announceWithContext(message: string, context?: {
  nodeType?: string
  position?: number
  totalItems?: number
  operation?: string
}): void
```

## Testing Strategy

### Unit Testing

1. **Extension Configuration**: Test all configuration options and defaults
2. **Command Execution**: Verify drag and menu commands work correctly
3. **State Management**: Test state transitions and cleanup
4. **Event Handling**: Mock DOM events and verify proper handling
5. **Keyboard Accessibility**: Test enhanced keyboard shortcuts and screen reader support

### Integration Testing

1. **Editor Integration**: Test extensions work with existing TipTap setup
2. **Extension Compatibility**: Verify no conflicts with other extensions
3. **Performance Impact**: Measure performance with large documents
4. **Cross-browser Compatibility**: Test on different browsers and devices
5. **Accessibility Integration**: Test screen reader compatibility across platforms

### E2E Testing

1. **User Workflows**: Test complete drag and drop scenarios
2. **Menu Interactions**: Test menu opening, navigation, and actions
3. **Keyboard Accessibility**: Verify keyboard-only usage works
4. **Mobile Touch Events**: Test touch-based interactions
5. **Screen Reader Testing**: Test with NVDA, JAWS, and VoiceOver

### Testing Implementation

```typescript
// Example test structure
describe('DragHandleExtension', () => {
  describe('Configuration', () => {
    it('should use default options when none provided')
    it('should merge custom options with defaults')
  })

  describe('Drag Operations', () => {
    it('should initiate drag on handle click')
    it('should show drop zones during drag')
    it('should reorder content on successful drop')
    it('should cancel drag on escape key')
  })

  describe('Accessibility', () => {
    it('should support keyboard shortcuts')
    it('should provide proper ARIA labels')
    it('should announce drag operations to screen readers')
  })
})
```

### Performance Testing

1. **Large Document Handling**: Test with documents containing 1000+ paragraphs
2. **Memory Usage**: Monitor memory consumption during extended drag operations
3. **Animation Performance**: Ensure smooth 60fps animations
4. **Event Handler Efficiency**: Optimize event listener performance

### Accessibility Testing

1. **Screen Reader Compatibility**: Test with NVDA, JAWS, and VoiceOver
2. **Keyboard Navigation**: Verify all functionality accessible via keyboard
3. **Focus Management**: Ensure proper focus handling during operations
4. **Color Contrast**: Verify visual indicators meet WCAG guidelines

The testing strategy ensures robust, accessible, and performant implementation that integrates seamlessly with the existing TipTap editor ecosystem.