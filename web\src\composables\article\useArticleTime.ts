import { type Ref } from 'vue'

import { type Article } from '@/types/article.types'

export function useArticleTime(article: Ref<Article>) {
  // 切换时间显示格式
  const toggleTimeFormat = (type: 'publish' | 'modify') => {
    if (type === 'publish') {
      if (article.value.showExactPublishTime === undefined) {
        article.value.showExactPublishTime = true
      } else {
        article.value.showExactPublishTime = !article.value.showExactPublishTime
      }
    } else {
      if (article.value.showExactModifyTime === undefined) {
        article.value.showExactModifyTime = true
      } else {
        article.value.showExactModifyTime = !article.value.showExactModifyTime
      }
    }
  }

  return {
    toggleTimeFormat,
  }
}
