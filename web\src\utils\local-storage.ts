import { LOGIN_USER } from '@/constants/storage.constants'
import { type LoginUser } from '@/types/user.types'
import Json from '@/utils/json'

class LocalStorage {
  /**
   * 设置数据到 localStorage
   * @param key 存储的键
   * @param value 存储的值，可以是任意类型
   */
  static set<T>(key: string, value: T): void {
    const jsonString = Json.stringify(value)
    localStorage.setItem(key, jsonString)
  }

  /**
   * 从 localStorage 获取数据
   * @param key 存储的键
   * @returns 返回解析后的值，若解析失败或不存在则返回 null
   */
  static get<T>(key: string): T | null {
    const jsonString = localStorage.getItem(key)
    if (!jsonString) return null
    return Json.parse(jsonString)
  }

  /**
   * 删除 localStorage 中的指定键
   * @param key 要删除的键
   */
  static remove(key: string): void {
    localStorage.removeItem(key)
  }

  /**
   * 清空 localStorage
   */
  static clear(): void {
    localStorage.clear()
  }

  static getLoginUser(): LoginUser {
    return this.get(LOGIN_USER) as LoginUser
  }

  static setLoginUser(user: LoginUser): void {
    this.set(LOGIN_USER, user)
  }

  static removeLoginUser(): void {
    this.remove(LOGIN_USER)
  }
}

export default LocalStorage
