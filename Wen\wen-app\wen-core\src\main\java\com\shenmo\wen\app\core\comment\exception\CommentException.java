package com.shenmo.wen.app.core.comment.exception;

import com.shenmo.wen.common.exception.BaseException;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnum;
import org.springframework.http.HttpStatus;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class CommentException extends BaseException {
    public CommentException(ExceptionEnum exceptionEnum) {
        super(exceptionEnum);
    }

    public CommentException(HttpStatus httpStatus, String message) {
        super(httpStatus, message);
    }

    public CommentException(HttpStatus httpStatus, String description, Throwable throwable) {
        super(httpStatus, description, throwable);
    }

    public CommentException(HttpStatus httpStatus, String description, String message) {
        super(httpStatus, description, message);
    }
}
