package com.shenmo.wen.common.pojo.response;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.*;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;

/**
 * 响应结果数据
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@AllArgsConstructor(access = AccessLevel.PRIVATE)
@NoArgsConstructor(access = AccessLevel.PRIVATE)
public class ResponseData<T> {

    /**
     * 默认成功信息
     */
    public static final String DEFAULT_SUCCESS_MESSAGE = "请求成功";

    /**
     * 默认错误信息
     */
    public static final String DEFAULT_ERROR_MESSAGE = "网络异常";

    /**
     * 默认成功状态码
     */
    public static final Integer DEFAULT_SUCCESS_CODE = 200;

    /**
     * 默认错误状态码
     */
    public static final Integer DEFAULT_ERROR_CODE = 500;

    /**
     * 请求是否成功
     */
    @Schema(description = "请求是否成功", example = "true")
    private Boolean success;

    /**
     * 响应状态码
     */
    @Schema(description = "响应状态码", example = "200")
    private Integer code;

    /**
     * 响应信息
     */
    @Schema(description = "响应信息", example = "请求成功")
    private String message;

    /**
     * 响应数据
     */
    @Schema(description = "响应数据")
    private T data;


    /**
     * 成功响应
     *
     * @return {@link ResponseData}
     * <AUTHOR>
     */
    public static <T> ResponseData<T> success() {
        return ResponseData.<T>builder().success(true).code(DEFAULT_SUCCESS_CODE).message(DEFAULT_SUCCESS_MESSAGE).build();
    }

    /**
     * 成功响应
     *
     * @param httpStatus {@link HttpStatus}
     * @return {@link ResponseData}
     * <AUTHOR>
     */
    public static <T> ResponseData<T> success(@NonNull HttpStatus httpStatus) {
        return ResponseData.<T>builder().success(true).code(httpStatus.value()).message(DEFAULT_SUCCESS_MESSAGE).build();
    }


    /**
     * 成功响应
     *
     * @param object {@link ResponseData#data}
     * @return {@link ResponseData}
     * <AUTHOR>
     */
    public static <T> ResponseData<T> success(T object) {
        return ResponseData.<T>builder().success(true).code(DEFAULT_SUCCESS_CODE).message(DEFAULT_SUCCESS_MESSAGE).data(object).build();
    }

    /**
     * 成功响应
     *
     * @param code    {@link ResponseData#code}
     * @param message {@link ResponseData#message}
     * @param object  {@link ResponseData#data}
     * @return {@link ResponseData}
     * <AUTHOR>
     */
    public static <T> ResponseData<T> success(Integer code, String message, T object) {
        return ResponseData.<T>builder().success(true).code(code).message(message).data(object).build();
    }

    /**
     * 错误响应
     *
     * @param httpStatus {@link HttpStatus}
     * @return {@link ResponseData}
     * <AUTHOR>
     */
    public static <T> ResponseData<T> error(HttpStatus httpStatus) {
        return ResponseData.<T>builder().success(false).code(httpStatus.value()).message(DEFAULT_SUCCESS_MESSAGE).build();
    }

    /**
     * 错误响应
     *
     * @param message {@link ResponseData#message}
     * @return {@link ResponseData}
     * <AUTHOR>
     */
    public static <T> ResponseData<T> error(String message) {
        return ResponseData.<T>builder().success(false).code(DEFAULT_SUCCESS_CODE).message(message).build();
    }

    /**
     * 错误响应
     *
     * @param code    {@link ResponseData#code}
     * @param message {@link ResponseData#message}
     * @return {@link ResponseData}
     * <AUTHOR>
     */
    public static <T> ResponseData<T> error(Integer code, String message) {
        return ResponseData.<T>builder().success(false).code(code).message(message).build();
    }

    /**
     * 错误响应
     *
     * @param code    {@link ResponseData#code}
     * @param message {@link ResponseData#message}
     * @param object  {@link ResponseData#data}
     * @return {@link ResponseData}
     * <AUTHOR>
     */
    public static <T> ResponseData<T> error(Integer code, String message, T object) {
        return ResponseData.<T>builder().success(false).code(code).message(message).data(object).build();
    }

    /**
     * 创建建造者
     *
     * @param <T> 响应数据泛型
     * @return 建造者
     * <AUTHOR>
     */
    public static <T> ResponseDataBuilder<T> builder() {
        return new ResponseDataBuilder<>();
    }

    /**
     * {@link ResponseData}建造者
     *
     * <AUTHOR>
     * @version 2.0.0
     */
    @ToString
    @NoArgsConstructor(access = AccessLevel.PRIVATE)
    public static class ResponseDataBuilder<T> {
        /**
         * {@link ResponseData#success}
         */
        private Boolean success;
        /**
         * {@link ResponseData#code}
         */
        private Integer code;
        /**
         * {@link ResponseData#message}
         */
        private String message;
        /**
         * {@link ResponseData#data}
         */
        private T data;


        /**
         * 构造{@link #success}
         *
         * @param success 请求是否成功
         * @return 建造者
         * <AUTHOR>
         */
        public ResponseDataBuilder<T> success(Boolean success) {
            this.success = success;
            return this;
        }

        /**
         * 构造{@link #code}
         *
         * @param code 响应状态码
         * @return 建造者
         * <AUTHOR>
         */
        public ResponseDataBuilder<T> code(Integer code) {
            this.code = code;
            return this;
        }

        /**
         * 构造{@link #message}
         *
         * @param message 响应信息
         * @return 建造者
         * <AUTHOR>
         */
        public ResponseDataBuilder<T> message(String message) {
            this.message = message;
            return this;
        }

        /**
         * 构造{@link #data}
         *
         * @param data 响应数据
         * @return 建造者
         * <AUTHOR>
         */
        public ResponseDataBuilder<T> data(T data) {
            this.data = data;
            return this;
        }

        /**
         * 构造 {@link ResponseData}
         *
         * @return 响应数据
         * <AUTHOR>
         */
        public ResponseData<T> build() {
            return new ResponseData<>(this.success, this.code, this.message, this.data);
        }
    }
}
