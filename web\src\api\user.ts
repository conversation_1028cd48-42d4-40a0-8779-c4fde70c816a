import { type ResponseData } from '@/types/response_data.types'
import api from '@/utils/api'

import type { AxiosError } from 'axios'

const userApi = {
  URL: '/core/users',

  // 在线
  online: async <T>(): Promise<ResponseData<T>> => {
    const res = await api.get<T>(userApi.URL + '/online').catch((err: AxiosError) => {
      return api.handleError(err)
    })
    return res.data as ResponseData<T>
  },
  // 信息
  info: async <T>(): Promise<ResponseData<T>> => {
    const res = await api.get<T>(userApi.URL + '/info').catch((err) => {
      return api.handleError(err)
    })
    return res.data as ResponseData<T>
  },
  // 更换头像
  changeAvatar: async <T>(file: File): Promise<ResponseData<T>> => {
    const formData = new FormData()
    formData.append('file', file)
    const res = await api
      .putFormData<T>(userApi.URL + '/avatar', formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      })
      .catch((err) => {
        return api.handleError(err)
      })
    return res.data as ResponseData<T>
  },
  // 更新通知接收类型
  updateNotificationReceiveType: async <T>(type: number): Promise<ResponseData<T>> => {
    const res = await api
      .put<T>(`${userApi.URL}/notification-receive-type/${type}`)
      .catch((err) => {
        return api.handleError(err)
      })
    return res.data as ResponseData<T>
  },
  // 搜索用户
  searchUser: async <T>(username: string): Promise<ResponseData<T>> => {
    const res = await api.get<T>(userApi.URL + '/search/' + username).catch((err) => {
      return api.handleError(err)
    })
    return res.data as ResponseData<T>
  },
}

export default userApi
