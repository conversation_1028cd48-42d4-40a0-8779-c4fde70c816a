package com.shenmo.wen.app.core.notification.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.shenmo.wen.app.core.notification.pojo.entity.WenNotification;
import com.shenmo.wen.app.core.notification.pojo.vo.WenNotificationVo;
import com.shenmo.wen.common.mybatis.handler.TimestampToLongTypeHandler;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface WenNotificationMapper extends BaseMapper<WenNotification> {

    /**
     * 根据用户ID分页查询通知列表
     * 包含权限检查：
     * - 文章相关通知：仅当文章公开或用户是文章作者时可见
     * - 评论相关通知：仅当评论所属文章公开或用户是文章作者时可见
     * - 系统通知：任何用户都可见
     * 已读状态根据用户-通知关系表动态判断
     * 
     * @param page   分页对象
     * @param userId 用户ID
     * @param ctTm   通知创建时间戳
     * @return 通知列表
     */
    @Select("""
            select
                n.id, n.user_id, n.article_id, n.comment_id, n.comment_notification_user_id, n.content, n.ct_tm,
                case when nr.notification_id is not null then 1 else 0 end as is_read
            from
                wen_notification n
            left join
                wen_article a on n.article_id = a.id
            left join
                wen_comment c on n.comment_id = c.id
            left join
                wen_article ac on c.article_id = ac.id
            left join
                wen_notification_user_read nr on n.id = nr.notification_id and nr.user_id = #{userId}
            where
                (n.comment_id is null or find_in_set(#{userId}, n.comment_notification_user_id))
                and unix_timestamp(n.ct_tm) >= #{ctTm}
                and (
                    (n.article_id is not null and (
                        a.published_scope = 0
                        or a.user_id = #{userId}
                    ))
                    or
                    (n.comment_id is not null and (
                        ac.published_scope = 0
                        or ac.user_id = #{userId}
                    ))
                    or
                    (n.article_id is null and n.comment_id is null)
                )
            order by n.ct_tm desc
            """)
    @Result(property = "ctTm", column = "ct_tm", typeHandler = TimestampToLongTypeHandler.class)
    IPage<WenNotification> pageByUser(Page<WenNotification> page, @Param("userId") Long userId,
            @Param("ctTm") Long ctTm);

    /**
     * 根据用户ID和通知接收类型获取过滤后的通知列表
     * 根据通知类型和用户设置进行精确过滤：
     * - 评论通知：对当前用户或文章作者或相关分享用户可见
     * - 文章通知：根据发布范围、用户关系和通知类型（发布/修改/收藏/分享）过滤
     * - 系统通知：仅在全部通知类型下可见
     * 
     * @param page                 分页对象
     * @param userId               用户ID
     * @param ctTm                 通知创建时间戳
     * @param notificationTypeCode 通知类型代码
     */
    @Select("""
            <script>
            select
                n.id, n.user_id, n.article_id, n.comment_id, n.comment_notification_user_id, n.content, n.type, n.ct_tm,
                case when nr.notification_id is not null then 1 else 0 end as is_read
            from
                wen_notification n
            left join
                wen_article a on n.article_id = a.id
            left join
                wen_comment c on n.comment_id = c.id
            left join
                wen_article ac on c.article_id = ac.id
            left join
                wen_notification_user_read nr on n.id = nr.notification_id and nr.user_id = #{userId}
            where
                unix_timestamp(n.ct_tm) >= #{ctTm}
                and (
                    (n.comment_id is not null and find_in_set(#{userId}, n.comment_notification_user_id) and (
                        ac.published_scope = 0
                        or ac.user_id = #{userId}
                        <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                        or ac.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        </if>
                    ))
                    or
                    (n.article_id is not null and n.comment_id is null and (
                        a.published_scope = 0
                        or a.user_id = #{userId}
                        <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                        or a.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        </if>
                    )
                    <if test="notificationTypeCode == 1">
                    and n.type = 1
                    </if>
                    <if test="notificationTypeCode == 2">
                    and n.type = 2
                    </if>
                    <if test="notificationTypeCode == 3">
                    and n.type = 3
                    </if>
                    <if test="notificationTypeCode == 4 and favoriteArticleIds != null and favoriteArticleIds.size() > 0">
                    and a.id in
                    <foreach item="item" collection="favoriteArticleIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    </if>
                    <if test="notificationTypeCode == 5 and sharedArticleIds != null and sharedArticleIds.size() > 0">
                    and a.id in
                    <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    </if>
                    <if test="notificationTypeCode == 6">
                    and n.type = 3
                    </if>
                    )
                    or
                    (n.article_id is null and n.comment_id is null and n.type = 0 and #{notificationTypeCode} = 1)
                )
            order by n.ct_tm desc
            </script>
            """)
    @Result(property = "ctTm", column = "ct_tm", typeHandler = TimestampToLongTypeHandler.class)
    IPage<WenNotification> pageFilteredByUser(
            Page<WenNotification> page,
            @Param("userId") Long userId,
            @Param("ctTm") Long ctTm,
            @Param("notificationTypeCode") Integer notificationTypeCode,
            @Param("favoriteArticleIds") List<Long> favoriteArticleIds,
            @Param("sharedArticleIds") List<Long> sharedArticleIds);

    /**
     * 查询通知对指定用户的已读状态
     * 通过EXISTS查询判断用户是否已读指定通知
     * 返回1表示已读，0表示未读
     * 
     * @param notificationId 通知ID
     * @param userId         用户ID
     * @return 已读状态
     */
    @Select("""
            select
                case when exists (
                    select 1 from wen_notification_user_read
                    where notification_id = #{notificationId}
                    and user_id = #{userId}
                ) then 1 else 0 end as is_read
            """)
    Integer isNotificationRead(@Param("notificationId") Long notificationId, @Param("userId") Long userId);

    /**
     * 标记通知为已读
     * 在通知用户已读关系表中插入记录
     * 记录通知ID、用户ID、阅读时间和创建时间
     * 
     * @param generatedId    雪花ID
     * @param notificationId 通知ID
     * @param userId         用户ID
     */
    @Update("""
            insert into wen_notification_user_read (id, notification_id, user_id, ct_tm)
            values (#{generatedId}, #{notificationId}, #{userId}, now())
            """)
    void markAsRead(@Param("generatedId") Long generatedId, @Param("notificationId") Long notificationId, @Param("userId") Long userId);

    /**
     * 标记通知为未读
     * 从通知用户已读关系表中删除对应记录
     * 
     * @param notificationId 通知ID
     * @param userId         用户ID
     */
    @Update("""
            delete from wen_notification_user_read
            where notification_id = #{notificationId} and user_id = #{userId}
            """)
    void markAsUnread(@Param("notificationId") Long notificationId, @Param("userId") Long userId);

    /**
     * 计算未读通知数量
     * 根据用户ID、注册时间和通知接收类型过滤通知
     * 只计算满足以下条件的通知：
     * 1. 用户尚未阅读的通知（通知用户已读关系表中无记录）
     * 2. 通知创建时间晚于用户注册时间的通知
     * 3. 根据通知类型（全部/发布/修改/收藏/分享）和内容关键字过滤
     * 
     * @param userId               用户ID
     * @param ctTm                 通知创建时间戳
     * @param notificationTypeCode 通知类型代码
     */
    @Select("""
            <script>
            select count(1) from wen_notification n
            left join wen_article a on n.article_id = a.id
            left join wen_comment c on n.comment_id = c.id
            left join wen_article ac on c.article_id = ac.id
            left join wen_notification_user_read nr on n.id = nr.notification_id and nr.user_id = #{userId}
            where nr.notification_id is null
            and unix_timestamp(n.ct_tm) >= #{ctTm}
            and (
                <if test="notificationTypeCode == 1">
                <!-- 全部类型：所有自己的文章与评论，公开文章及分享的文章发布/修改，以及相关评论 -->
                (
                    <!-- 评论通知: 自己的评论、自己文章下的评论、公开文章或分享给自己的文章下的评论 -->
                    (n.comment_id is not null and (
                        find_in_set(#{userId}, n.comment_notification_user_id)
                        or n.user_id = #{userId}
                        or ac.user_id = #{userId}
                        or ac.published_scope = 0
                        <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                        or ac.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        </if>
                    ))
                    or
                    <!-- 文章通知: 自己的文章、公开文章或分享给自己的文章的发布/修改 -->
                    (n.article_id is not null and n.comment_id is null and (
                        a.user_id = #{userId}
                        or a.published_scope = 0
                        <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                        or a.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        </if>
                    ))
                    or
                    <!-- 系统通知 -->
                    (n.article_id is null and n.comment_id is null and n.type = 0)
                )
                </if>

                <if test="notificationTypeCode == 2">
                <!-- 发布类型：所有自己的文章与评论发布，公开文章及分享的文章发布，以及相关评论发布 -->
                (
                    <!-- 评论通知: 自己的评论发布、自己文章下的评论发布、公开文章或分享给自己的文章下的评论发布 -->
                    (n.comment_id is not null and n.type = 3 and (
                        find_in_set(#{userId}, n.comment_notification_user_id)
                        or n.user_id = #{userId}
                        or ac.user_id = #{userId}
                        or ac.published_scope = 0
                        <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                        or ac.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        </if>
                    ))
                    or
                    <!-- 文章通知: 自己的文章发布、公开文章或分享给自己的文章的发布 -->
                    (n.article_id is not null and n.comment_id is null and n.type = 1 and (
                        a.user_id = #{userId}
                        or a.published_scope = 0
                        <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                        or a.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        </if>
                    ))
                )
                </if>

                <if test="notificationTypeCode == 3">
                <!-- 修改类型：所有自己的文章修改，公开文章及分享的文章修改 -->
                (
                    <!-- 文章通知: 自己的文章修改、公开文章或分享给自己的文章的修改 -->
                    (n.article_id is not null and n.comment_id is null and n.type = 2 and (
                        a.user_id = #{userId}
                        or a.published_scope = 0
                        <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                        or a.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        </if>
                    ))
                )
                </if>

                <if test="notificationTypeCode == 4">
                <!-- 收藏类型：所有公开的被收藏的文章的发布与修改，加上这些文章下的评论发布 -->
                <if test="favoriteArticleIds != null and favoriteArticleIds.size() > 0">
                (
                    <!-- 评论通知: 收藏文章下的评论发布 -->
                    (n.comment_id is not null and n.type = 3 and ac.id in
                    <foreach item="item" collection="favoriteArticleIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                    or
                    <!-- 文章通知: 收藏的文章发布或修改 -->
                    (n.article_id is not null and n.comment_id is null and (n.type = 1 or n.type = 2) and a.id in
                    <foreach item="item" collection="favoriteArticleIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                )
                </if>
                </if>

                <if test="notificationTypeCode == 5">
                <!-- 分享类型：所有分享给自己的文章的发布与修改，加上这些文章下的评论发布 -->
                <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                (
                    <!-- 评论通知: 分享文章下的评论发布 -->
                    (n.comment_id is not null and n.type = 3 and ac.id in
                    <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                    or
                    <!-- 文章通知: 分享的文章发布或修改 -->
                    (n.article_id is not null and n.comment_id is null and (n.type = 1 or n.type = 2) and a.id in
                    <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                )
                </if>
                </if>
            )
            </script>
            """)
    Long countUnreadNotifications(
            @Param("userId") Long userId,
            @Param("ctTm") Long ctTm,
            @Param("notificationTypeCode") Integer notificationTypeCode,
            @Param("favoriteArticleIds") List<Long> favoriteArticleIds,
            @Param("sharedArticleIds") List<Long> sharedArticleIds);

    /**
     * 根据用户ID和通知接收类型获取过滤后的通知VO列表
     * 类似pageFilteredByUser方法，但返回包含发布者信息的完整VO对象
     * 包含发布者用户名和头像信息
     *
     * @param page                        分页对象
     * @param userId                      用户ID
     * @param ctTm                        通知创建时间戳
     * @param notificationReceiveTypeCode 通知接收类型代码
     */
    @Select("""
            <script>
            select
                n.id, n.user_id, n.article_id, n.comment_id, n.comment_notification_user_id, n.content, n.type, n.ct_tm,
                case when nr.notification_id is not null then 1 else 0 end as is_read,
                u.username as publisher, u.avatar as publisher_avatar
            from
                wen_notification n
            left join
                wen_article a on n.article_id = a.id
            left join
                wen_comment c on n.comment_id = c.id
            left join
                wen_article ac on c.article_id = ac.id
            left join
                wen_notification_user_read nr on n.id = nr.notification_id and nr.user_id = #{userId}
            left join
                wen_user u on n.user_id = u.id
            where
                unix_timestamp(n.ct_tm) >= #{ctTm}
                and (
                    <if test="notificationReceiveTypeCode == 1">
                    <!-- 全部类型：所有自己的文章与评论，公开文章及分享的文章发布/修改，以及相关评论 -->
                    (
                        <!-- 评论通知: 自己的评论、自己文章下的评论、公开文章或分享给自己的文章下的评论 -->
                        (n.comment_id is not null and (
                            find_in_set(#{userId}, n.comment_notification_user_id)
                            or n.user_id = #{userId}
                            or ac.user_id = #{userId}
                            or ac.published_scope = 0
                            <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                            or ac.id in
                            <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            </if>
                        ))
                        or
                        <!-- 文章通知: 自己的文章、公开文章或分享给自己的文章的发布/修改 -->
                        (n.article_id is not null and n.comment_id is null and (
                            a.user_id = #{userId}
                            or a.published_scope = 0
                            <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                            or a.id in
                            <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            </if>
                        ))
                        or
                        <!-- 系统通知 -->
                        (n.article_id is null and n.comment_id is null and n.type = 0)
                    )
                    </if>

                    <if test="notificationReceiveTypeCode == 2">
                    <!-- 发布类型：所有自己的文章与评论发布，公开文章及分享的文章发布，以及相关评论发布 -->
                    (
                        <!-- 评论通知: 自己的评论发布、自己文章下的评论发布、公开文章或分享给自己的文章下的评论发布 -->
                        (n.comment_id is not null and n.type = 3 and (
                            find_in_set(#{userId}, n.comment_notification_user_id)
                            or n.user_id = #{userId}
                            or ac.user_id = #{userId}
                            or ac.published_scope = 0
                            <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                            or ac.id in
                            <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            </if>
                        ))
                        or
                        <!-- 文章通知: 自己的文章发布、公开文章或分享给自己的文章的发布 -->
                        (n.article_id is not null and n.comment_id is null and n.type = 1 and (
                            a.user_id = #{userId}
                            or a.published_scope = 0
                            <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                            or a.id in
                            <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            </if>
                        ))
                    )
                    </if>

                    <if test="notificationReceiveTypeCode == 3">
                    <!-- 修改类型：所有自己的文章修改，公开文章及分享的文章修改 -->
                    (
                        <!-- 文章通知: 自己的文章修改、公开文章或分享给自己的文章的修改 -->
                        (n.article_id is not null and n.comment_id is null and n.type = 2 and (
                            a.user_id = #{userId}
                            or a.published_scope = 0
                            <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                            or a.id in
                            <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                                #{item}
                            </foreach>
                            </if>
                        ))
                    )
                    </if>

                    <if test="notificationReceiveTypeCode == 4">
                    <!-- 收藏类型：所有公开的被收藏的文章的发布与修改，加上这些文章下的评论发布 -->
                    <if test="favoriteArticleIds != null and favoriteArticleIds.size() > 0">
                    (
                        <!-- 评论通知: 收藏文章下的评论发布 -->
                        (n.comment_id is not null and n.type = 3 and ac.id in
                        <foreach item="item" collection="favoriteArticleIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        )
                        or
                        <!-- 文章通知: 收藏的文章发布或修改 -->
                        (n.article_id is not null and n.comment_id is null and (n.type = 1 or n.type = 2) and a.id in
                        <foreach item="item" collection="favoriteArticleIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        )
                    )
                    </if>
                    </if>

                    <if test="notificationReceiveTypeCode == 5">
                    <!-- 分享类型：所有分享给自己的文章的发布与修改，加上这些文章下的评论发布 -->
                    <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                    (
                        <!-- 评论通知: 分享文章下的评论发布 -->
                        (n.comment_id is not null and n.type = 3 and ac.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        )
                        or
                        <!-- 文章通知: 分享的文章发布或修改 -->
                        (n.article_id is not null and n.comment_id is null and (n.type = 1 or n.type = 2) and a.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        )
                    )
                    </if>
                    </if>
                )
            order by n.ct_tm desc
            </script>
            """)
    @Result(property = "ctTm", column = "ct_tm", typeHandler = TimestampToLongTypeHandler.class)
    IPage<WenNotificationVo> pageFilteredVoByUser(
            Page<WenNotificationVo> page,
            @Param("userId") Long userId,
            @Param("ctTm") Long ctTm,
            @Param("notificationReceiveTypeCode") Integer notificationReceiveTypeCode,
            @Param("favoriteArticleIds") List<Long> favoriteArticleIds,
            @Param("sharedArticleIds") List<Long> sharedArticleIds);

    /**
     * 获取通知的所有接收用户数量
     * 统计已阅读指定通知的用户数量
     * 用于判断是否所有用户都已读通知
     * 
     * @param notificationId 通知ID
     * @return 已阅读用户数量
     */
    @Select("""
            select count(distinct user_id) from wen_notification_user_read
            where notification_id = #{notificationId}
            """)
    Long countReadUsersByNotificationId(@Param("notificationId") Long notificationId);

    /**
     * 获取通知的目标用户总数
     * 统计应该接收此通知的用户数量，只计算注册时间早于通知创建时间的用户
     * 用于确定全局已读状态
     * 
     * @param notificationId 通知ID
     * @return 目标用户数量
     */
    @Select("""
            SELECT
                COUNT(DISTINCT u.id) AS target_user_count
            FROM
                wen_user u
            JOIN
                wen_notification n ON n.id = #{notificationId}
            WHERE
                (
                    (n.comment_id IS NOT NULL AND FIND_IN_SET(u.id, n.comment_notification_user_id))
                    OR
                    (n.comment_id IS NULL AND (
                        n.user_id = u.id OR
                        u.id IN (SELECT user_id FROM wen_article_share WHERE article_id = n.article_id)
                    ))
                )
                AND unix_timestamp(u.ct_tm) <= unix_timestamp(n.ct_tm)
            """)
    Long countTargetUsersByNotificationId(@Param("notificationId") Long notificationId);

    /**
     * 查询指定用户相关的、未全部标记为已读的通知
     * 获取用户作为发布者或被@提及的，且尚未被全部标记为已读的通知
     * 根据通知接收类型进行过滤，与其他查询保持一致
     * 用于readAll方法中检查哪些通知需要更新全局已读状态
     * 
     * @param userId               用户ID
     * @param ctTm                 通知创建时间戳
     * @param notificationTypeCode 通知类型代码
     * @param favoriteArticleIds   收藏的文章ID列表
     * @param sharedArticleIds     分享的文章ID列表
     * @return 未全部标记为已读的通知列表
     */
    @Select("""
            <script>
            select n.* from wen_notification n
            left join wen_article a on n.article_id = a.id
            left join wen_comment c on n.comment_id = c.id
            left join wen_article ac on c.article_id = ac.id
            where n.is_read = 0
            and unix_timestamp(n.ct_tm) >= #{ctTm}
            and (
                <if test="notificationTypeCode == 1">
                <!-- 全部类型：所有自己的文章与评论，公开文章及分享的文章发布/修改，以及相关评论 -->
                (
                    <!-- 评论通知: 自己的评论、自己文章下的评论、公开文章或分享给自己的文章下的评论 -->
                    (n.comment_id is not null and (
                        find_in_set(#{userId}, n.comment_notification_user_id)
                        or n.user_id = #{userId}
                        or ac.user_id = #{userId}
                        or ac.published_scope = 0
                        <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                        or ac.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        </if>
                    ))
                    or
                    <!-- 文章通知: 自己的文章、公开文章或分享给自己的文章的发布/修改 -->
                    (n.article_id is not null and n.comment_id is null and (
                        a.user_id = #{userId}
                        or a.published_scope = 0
                        <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                        or a.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        </if>
                    ))
                    or
                    <!-- 系统通知 -->
                    (n.article_id is null and n.comment_id is null and n.type = 0)
                )
                </if>

                <if test="notificationTypeCode == 2">
                <!-- 发布类型：所有自己的文章与评论发布，公开文章及分享的文章发布，以及相关评论发布 -->
                (
                    <!-- 评论通知: 自己的评论发布、自己文章下的评论发布、公开文章或分享给自己的文章下的评论发布 -->
                    (n.comment_id is not null and n.type = 3 and (
                        find_in_set(#{userId}, n.comment_notification_user_id)
                        or n.user_id = #{userId}
                        or ac.user_id = #{userId}
                        or ac.published_scope = 0
                        <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                        or ac.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        </if>
                    ))
                    or
                    <!-- 文章通知: 自己的文章发布、公开文章或分享给自己的文章的发布 -->
                    (n.article_id is not null and n.comment_id is null and n.type = 1 and (
                        a.user_id = #{userId}
                        or a.published_scope = 0
                        <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                        or a.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        </if>
                    ))
                )
                </if>

                <if test="notificationTypeCode == 3">
                <!-- 修改类型：所有自己的文章修改，公开文章及分享的文章修改 -->
                (
                    <!-- 文章通知: 自己的文章修改、公开文章或分享给自己的文章的修改 -->
                    (n.article_id is not null and n.comment_id is null and n.type = 2 and (
                        a.user_id = #{userId}
                        or a.published_scope = 0
                        <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                        or a.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        </if>
                    ))
                )
                </if>

                <if test="notificationTypeCode == 4">
                <!-- 收藏类型：所有公开的被收藏的文章的发布与修改，加上这些文章下的评论发布 -->
                <if test="favoriteArticleIds != null and favoriteArticleIds.size() > 0">
                (
                    <!-- 评论通知: 收藏文章下的评论发布 -->
                    (n.comment_id is not null and n.type = 3 and ac.id in
                    <foreach item="item" collection="favoriteArticleIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                    or
                    <!-- 文章通知: 收藏的文章发布或修改 -->
                    (n.article_id is not null and n.comment_id is null and (n.type = 1 or n.type = 2) and a.id in
                    <foreach item="item" collection="favoriteArticleIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                )
                </if>
                </if>

                <if test="notificationTypeCode == 5">
                <!-- 分享类型：所有分享给自己的文章的发布与修改，加上这些文章下的评论发布 -->
                <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                (
                    <!-- 评论通知: 分享文章下的评论发布 -->
                    (n.comment_id is not null and n.type = 3 and ac.id in
                    <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                    or
                    <!-- 文章通知: 分享的文章发布或修改 -->
                    (n.article_id is not null and n.comment_id is null and (n.type = 1 or n.type = 2) and a.id in
                    <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                        #{item}
                    </foreach>
                    )
                )
                </if>
                </if>
            )
            order by n.ct_tm desc
            </script>
            """)
    @Result(property = "ctTm", column = "ct_tm", typeHandler = TimestampToLongTypeHandler.class)
    List<WenNotification> findUserRelatedUnreadNotifications(
            @Param("userId") Long userId,
            @Param("ctTm") Long ctTm,
            @Param("notificationTypeCode") Integer notificationTypeCode,
            @Param("favoriteArticleIds") List<Long> favoriteArticleIds,
            @Param("sharedArticleIds") List<Long> sharedArticleIds);

    /**
     * 获取用于"全部已读"操作的未读通知ID列表
     *
     * @param userId               用户ID
     * @param ctTm                 通知创建时间戳 (秒)
     * @param notificationTypeCode 通知类型代码
     * @param favoriteArticleIds   收藏的文章ID列表
     * @param sharedArticleIds     分享的文章ID列表
     * @return 未读通知ID列表
     */
    @Select("""
            <script>
            select n.id from wen_notification n
            left join wen_notification_user_read nr on n.id = nr.notification_id and nr.user_id = #{userId}
            left join wen_article a on n.article_id = a.id
            left join wen_comment c on n.comment_id = c.id
            left join wen_article ac on c.article_id = ac.id
            where nr.notification_id is null
            and unix_timestamp(n.ct_tm) >= #{ctTm}
            and (
                <if test="notificationTypeCode == 1">
                <!-- 全部类型：所有自己的文章与评论，公开文章及分享的文章发布/修改，以及相关评论 -->
                (
                    <!-- 评论通知: 自己的评论、自己文章下的评论、公开文章或分享给自己的文章下的评论 -->
                    (n.comment_id is not null and (
                        find_in_set(#{userId}, n.comment_notification_user_id)
                        or n.user_id = #{userId}
                        or ac.user_id = #{userId}
                        or ac.published_scope = 0
                        <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                        or ac.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        </if>
                    ))
                    or
                    <!-- 文章通知: 自己的文章、公开文章或分享给自己的文章的发布/修改 -->
                    (n.article_id is not null and n.comment_id is null and (
                        a.user_id = #{userId}
                        or a.published_scope = 0
                        <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                        or a.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">
                            #{item}
                        </foreach>
                        </if>
                    ))
                    or
                    <!-- 系统通知 -->
                    (n.article_id is null and n.comment_id is null and n.type = 0)
                )
                </if>

                <if test="notificationTypeCode == 2">
                <!-- 发布类型 -->
                (
                    (n.comment_id is not null and n.type = 3 and (
                        find_in_set(#{userId}, n.comment_notification_user_id)
                        or n.user_id = #{userId}
                        or ac.user_id = #{userId}
                        or ac.published_scope = 0
                        <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                        or ac.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">#{item}</foreach>
                        </if>
                    ))
                    or
                    (n.article_id is not null and n.comment_id is null and n.type = 1 and (
                        a.user_id = #{userId}
                        or a.published_scope = 0
                        <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                        or a.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">#{item}</foreach>
                        </if>
                    ))
                )
                </if>

                <if test="notificationTypeCode == 3">
                <!-- 修改类型 -->
                (
                    (n.article_id is not null and n.comment_id is null and n.type = 2 and (
                        a.user_id = #{userId}
                        or a.published_scope = 0
                        <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                        or a.id in
                        <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">#{item}</foreach>
                        </if>
                    ))
                )
                </if>

                <if test="notificationTypeCode == 4">
                <!-- 收藏类型 -->
                <if test="favoriteArticleIds != null and favoriteArticleIds.size() > 0">
                (
                    (n.comment_id is not null and n.type = 3 and ac.id in
                    <foreach item="item" collection="favoriteArticleIds" open="(" separator="," close=")">#{item}</foreach>
                    )
                    or
                    (n.article_id is not null and n.comment_id is null and (n.type = 1 or n.type = 2) and a.id in
                    <foreach item="item" collection="favoriteArticleIds" open="(" separator="," close=")">#{item}</foreach>
                    )
                )
                </if>
                <if test="favoriteArticleIds == null or favoriteArticleIds.size() == 0">
                    (1=0) <!-- No favorite articles, so no notifications of this type -->
                </if>
                </if>

                <if test="notificationTypeCode == 5">
                <!-- 分享类型 -->
                <if test="sharedArticleIds != null and sharedArticleIds.size() > 0">
                (
                    (n.comment_id is not null and n.type = 3 and ac.id in
                    <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">#{item}</foreach>
                    )
                    or
                    (n.article_id is not null and n.comment_id is null and (n.type = 1 or n.type = 2) and a.id in
                    <foreach item="item" collection="sharedArticleIds" open="(" separator="," close=")">#{item}</foreach>
                    )
                )
                </if>
                <if test="sharedArticleIds == null or sharedArticleIds.size() == 0">
                     (1=0) <!-- No shared articles, so no notifications of this type -->
                </if>
                </if>
            )
            </script>
            """)
    List<Long> getUnreadNotificationIdsForReadAll(
            @Param("userId") Long userId,
            @Param("ctTm") Long ctTm,
            @Param("notificationTypeCode") Integer notificationTypeCode,
            @Param("favoriteArticleIds") List<Long> favoriteArticleIds,
            @Param("sharedArticleIds") List<Long> sharedArticleIds);

    /**
     * 批量标记通知为已读
     * 一次性插入多条已读记录，提高性能
     * 
     * @param records 要插入的记录列表，每条记录包含(id, notification_id, user_id)
     */
    @Update("""
            <script>
            insert into wen_notification_user_read (id, notification_id, user_id, ct_tm) values
            <foreach collection="records" item="record" separator=",">
                (#{record.id}, #{record.notificationId}, #{record.userId}, now())
            </foreach>
            </script>
            """)
    void batchMarkAsRead(@Param("records") List<NotificationReadRecord> records);

    /**
     * 通知已读记录的数据传输对象
     */
    record NotificationReadRecord(
        Long id,
        Long notificationId,
        Long userId
    ) {}

    /**
     * 查询用户所有未读通知
     * 
     * @param userId 用户ID
     * @return 未读通知列表
     */
    @Select("""
            SELECT n.* FROM wen_notification n
            WHERE n.id NOT IN (
                SELECT notification_id FROM wen_notification_user_read WHERE user_id = #{userId}
            )
            ORDER BY n.ct_tm DESC
            """)
    @Result(property = "ctTm", column = "ct_tm", typeHandler = TimestampToLongTypeHandler.class)
    List<WenNotification> findUnreadNotificationsByUserId(@Param("userId") Long userId);

    /**
     * 删除文章相关的所有通知
     * 
     * @param articleId 文章ID
     */
    default void deleteByArticleId(Long articleId) {
        delete(Wrappers.<WenNotification>lambdaQuery().eq(WenNotification::getArticleId, articleId));
    }
}
