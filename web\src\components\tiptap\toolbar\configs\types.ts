import type { Editor } from '@tiptap/vue-3'
import type { Component } from 'vue'

/**
 * 工具栏按钮配置接口
 */
export interface ToolbarButtonConfig {
  icon: Component
  extensionName: string
  trigger: (
    editor: Editor,
    showModal?: (title: string, callback: () => void, needInput?: boolean) => void,
    modal?: { inputValue?: string },
  ) => void
  isActive?: (editor: Editor) => boolean
  tooltip: string
  emit?: string
}
