package com.shenmo.wen.app.core.user.exception;

import com.shenmo.wen.common.exception.enumeration.ExceptionEnum;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnumOption;
import com.shenmo.wen.common.exception.enumeration.ExceptionType;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@ExceptionType(type = UserException.class, module = UserExceptionEnum.MODULE)
public enum UserExceptionEnum implements ExceptionEnum {

    /**
     * 用户不存在
     */
    USER_NOT_EXISTS(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "啪！人没了...")),
    ;

    public static final String MODULE = "003";

    /**
     * 异常枚举选项
     */
    private final ExceptionEnumOption exceptionEnumOption;

    UserExceptionEnum(ExceptionEnumOption exceptionEnumOption) {

        this.exceptionEnumOption = exceptionEnumOption;
    }

    @Override
    public ExceptionEnumOption getOption() {

        return exceptionEnumOption;
    }
}
