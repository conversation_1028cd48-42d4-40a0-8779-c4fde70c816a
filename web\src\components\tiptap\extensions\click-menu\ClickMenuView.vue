<template>
  <Teleport to="body">
    <Transition
      name="click-menu"
      @enter="onEnter"
      @leave="onLeave"
    >
      <div
        v-if="isVisible"
        ref="menuRef"
        class="click-menu"
        :class="{
          'click-menu--keyboard': keyboardMode,
          'click-menu--mobile': isMobile
        }"
        :style="menuStyle"
        @mouseenter="handleMouseEnter"
        @mouseleave="handleMouseLeave"
        @keydown="handleKeyDown"
        role="menu"
        :aria-label="ariaLabel"
        :aria-orientation="'vertical'"
        :aria-activedescendant="getActiveDescendantId()"
        tabindex="-1"
      >
        <!-- 菜单项组 -->
        <template v-for="(group, groupIndex) in menuGroups" :key="group.id">
          <div
            v-if="group.items.length > 0"
            class="click-menu__group"
            :class="`click-menu__group--${group.id}`"
          >
            <!-- 组标题 -->
            <div
              v-if="group.label && menuGroups.length > 1"
              class="click-menu__group-label"
            >
              {{ group.label }}
            </div>

            <!-- 菜单项 -->
            <button
              v-for="(item, itemIndex) in group.items"
              :key="item.id"
              :id="getMenuItemId(item)"
              ref="menuItemRefs"
              class="click-menu__item"
              :class="{
                'click-menu__item--selected': isItemSelected(groupIndex, itemIndex),
                'click-menu__item--disabled': !isItemEnabled(item)
              }"
              :disabled="!isItemEnabled(item)"
              :aria-selected="isItemSelected(groupIndex, itemIndex)"
              :aria-describedby="item.shortcut ? `${getMenuItemId(item)}-shortcut` : undefined"
              :title="getItemTooltip(item)"
              role="menuitem"
              @click="executeItem(item)"
              @mouseenter="selectItem(groupIndex, itemIndex)"
            >
              <!-- 图标 -->
              <div class="click-menu__item-icon" v-if="item.icon">
                <span v-html="item.icon"></span>
              </div>

              <!-- 标签 -->
              <div class="click-menu__item-label">
                {{ item.label }}
              </div>

              <!-- 快捷键 -->
              <div
                v-if="item.shortcut"
                :id="`${getMenuItemId(item)}-shortcut`"
                class="click-menu__item-shortcut"
                :aria-label="`快捷键 ${formatShortcut(item.shortcut)}`"
              >
                {{ formatShortcut(item.shortcut) }}
              </div>
            </button>
          </div>

          <!-- 分隔线 -->
          <div
            v-if="groupIndex < menuGroups.length - 1 && group.items.length > 0"
            class="click-menu__divider"
            role="separator"
          ></div>
        </template>

        <!-- 空状态 -->
        <div
          v-if="visibleItems.length === 0"
          class="click-menu__empty"
        >
          {{ emptyMessage }}
        </div>

        <!-- 加载状态 -->
        <div
          v-if="loading"
          class="click-menu__loading"
        >
          <div class="click-menu__loading-spinner"></div>
          <span>加载中...</span>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { 
  ref, 
  computed, 
  watch, 
  nextTick, 
  onMounted, 
  onUnmounted,
  type CSSProperties 
} from 'vue'
import type { Editor } from '@tiptap/core'
import type { Node as ProseMirrorNode } from '@tiptap/pm/model'
import type { ClickMenuItem, MenuState, MenuItemGroup } from './types'
import { KeyboardAccessibilityUtils } from '../shared/utils/keyboardAccessibility'

interface Props {
  editor: Editor
  menuState: MenuState | null | undefined
  menuItems: ClickMenuItem[]
  keyboardNavigation?: boolean
  positioning?: 'auto' | 'left' | 'right'
}

const props = withDefaults(defineProps<Props>(), {
  keyboardNavigation: true,
  positioning: 'auto'
})

const emit = defineEmits<{
  itemExecute: [item: ClickMenuItem]
  menuHide: []
  positionUpdate: [position: { x: number; y: number }]
}>()

// 响应式引用
const menuRef = ref<HTMLElement>()
const menuItemRefs = ref<HTMLElement[]>([])
const keyboardMode = ref(false)
const loading = ref(false)
const selectedGroupIndex = ref(0)
const selectedItemIndex = ref(0)

// 计算属性
const isVisible = computed(() => props.menuState?.visible ?? false)

const targetNode = computed(() => props.menuState?.targetNode ?? null)

const menuPosition = computed(() => props.menuState?.position ?? { x: 0, y: 0 })

const isMobile = computed(() => {
  return window.innerWidth <= 768
})

const ariaLabel = computed(() => {
  const nodeType = getNodeTypeName(targetNode.value)
  return `${nodeType} 操作菜单`
})

const emptyMessage = computed(() => {
  return '暂无可用操作'
})

// 过滤可见的菜单项
const visibleItems = computed(() => {
  if (!targetNode.value) return []
  
  return props.menuItems.filter(item => 
    item.isVisible(targetNode.value!)
  )
})

// 按组分组菜单项
const menuGroups = computed((): MenuItemGroup[] => {
  const groups = new Map<string, ClickMenuItem[]>()
  
  visibleItems.value.forEach(item => {
    const groupId = item.group || 'default'
    if (!groups.has(groupId)) {
      groups.set(groupId, [])
    }
    groups.get(groupId)!.push(item)
  })

  const result: MenuItemGroup[] = []
  
  // 定义组的优先级和标签
  const groupConfig = {
    'edit': { label: '编辑', priority: 1 },
    'format': { label: '格式', priority: 2 },
    'insert': { label: '插入', priority: 3 },
    'default': { label: '', priority: 99 }
  }

  groups.forEach((items, groupId) => {
    const config = groupConfig[groupId as keyof typeof groupConfig] || groupConfig.default
    result.push({
      id: groupId,
      label: config.label,
      items,
      priority: config.priority
    })
  })

  // 按优先级排序
  return result.sort((a, b) => a.priority - b.priority)
})

// 菜单样式
const menuStyle = computed((): CSSProperties => {
  const position = menuPosition.value
  const style: CSSProperties = {
    position: 'fixed',
    left: `${position.x}px`,
    top: `${position.y}px`,
    zIndex: 1000
  }

  // 移动端调整
  if (isMobile.value) {
    style.maxWidth = 'calc(100vw - 32px)'
    style.maxHeight = 'calc(100vh - 100px)'
  }

  return style
})

// 方法
const getNodeTypeName = (node: ProseMirrorNode | null): string => {
  if (!node) return '内容'
  
  switch (node.type.name) {
    case 'paragraph': return '段落'
    case 'heading': return '标题'
    case 'listItem': return '列表项'
    case 'codeBlock': return '代码块'
    default: return '内容'
  }
}

const isItemSelected = (groupIndex: number, itemIndex: number): boolean => {
  return selectedGroupIndex.value === groupIndex && selectedItemIndex.value === itemIndex
}

const isItemEnabled = (item: ClickMenuItem): boolean => {
  return targetNode.value ? item.isEnabled(targetNode.value) : false
}

const getItemTooltip = (item: ClickMenuItem): string => {
  let tooltip = item.label
  if (item.shortcut) {
    tooltip += ` (${formatShortcut(item.shortcut)})`
  }
  return tooltip
}

const formatShortcut = (shortcut: string): string => {
  return shortcut
    .replace(/mod/i, navigator.userAgent.includes('Mac') ? '⌘' : 'Ctrl')
    .replace(/ctrl|control/i, 'Ctrl')
    .replace(/cmd|command/i, '⌘')
    .replace(/shift/i, 'Shift')
    .replace(/alt|option/i, 'Alt')
    .replace(/\+/g, ' + ')
}

const selectItem = (groupIndex: number, itemIndex: number): void => {
  selectedGroupIndex.value = groupIndex
  selectedItemIndex.value = itemIndex
  keyboardMode.value = false
}

const executeItem = (item: ClickMenuItem): void => {
  if (!targetNode.value || !isItemEnabled(item) || !props.menuState?.targetPos) {
    announceToScreenReader('该操作不可用')
    return
  }

  try {
    // 公告操作执行
    announceToScreenReader(`执行操作：${item.label}`)
    
    // 执行菜单项动作
    item.action(props.editor, targetNode.value, props.menuState.targetPos)
    
    // 触发事件
    emit('itemExecute', item)
    
    // 隐藏菜单
    hideMenu()
    
    // 公告操作完成
    setTimeout(() => {
      announceToScreenReader('操作已完成')
    }, 100)
  } catch (error) {
    console.error('Error executing menu item:', error)
    announceToScreenReader('操作执行失败，请重试')
  }
}

const hideMenu = (): void => {
  emit('menuHide')
}

const navigateMenu = (direction: 'up' | 'down' | 'left' | 'right'): void => {
  if (!props.keyboardNavigation) return

  keyboardMode.value = true
  
  const flatItems = menuGroups.value.flatMap((group, groupIndex) => 
    group.items.map((item, itemIndex) => ({ groupIndex, itemIndex, item }))
  )

  if (flatItems.length === 0) return

  const currentFlatIndex = flatItems.findIndex(
    ({ groupIndex, itemIndex }) => 
      groupIndex === selectedGroupIndex.value && itemIndex === selectedItemIndex.value
  )

  let newFlatIndex = currentFlatIndex

  switch (direction) {
    case 'up':
      newFlatIndex = currentFlatIndex > 0 ? currentFlatIndex - 1 : flatItems.length - 1
      break
    case 'down':
      newFlatIndex = currentFlatIndex < flatItems.length - 1 ? currentFlatIndex + 1 : 0
      break
  }

  const newItem = flatItems[newFlatIndex]
  if (newItem) {
    selectedGroupIndex.value = newItem.groupIndex
    selectedItemIndex.value = newItem.itemIndex
    
    // 滚动到可见区域
    nextTick(() => {
      scrollToSelectedItem()
    })
  }
}

const scrollToSelectedItem = (): void => {
  const selectedElement = menuItemRefs.value.find((el, index) => {
    // 计算当前元素在扁平列表中的索引
    let flatIndex = 0
    for (const group of menuGroups.value) {
      for (let i = 0; i < group.items.length; i++) {
        if (flatIndex === index) {
          return selectedGroupIndex.value === menuGroups.value.indexOf(group) && 
                 selectedItemIndex.value === i
        }
        flatIndex++
      }
    }
    return false
  })

  if (selectedElement && menuRef.value) {
    selectedElement.scrollIntoView({
      block: 'nearest',
      behavior: 'smooth'
    })
  }
}

const handleKeyDown = (event: KeyboardEvent): void => {
  if (!props.keyboardNavigation) return

  // Use enhanced keyboard handler first
  const menuState = props.menuState
  if (menuState) {
    const handled = KeyboardAccessibilityUtils.handleEnhancedClickMenuKeyboard(
      props.editor, event, menuState
    )
    
    if (handled) {
      event.preventDefault()
      event.stopPropagation()
      return
    }
  }

  // Handle component-specific navigation
  switch (event.key) {
    case 'F1':
    case '?':
      if (!event.shiftKey) {
        event.preventDefault()
        event.stopPropagation()
        KeyboardAccessibilityUtils.announceKeyboardHelp('menu')
      }
      break
      
    case 'ArrowUp':
      event.preventDefault()
      event.stopPropagation()
      navigateMenu('up')
      announceSelection()
      break
      
    case 'ArrowDown':
      event.preventDefault()
      event.stopPropagation()
      navigateMenu('down')
      announceSelection()
      break
      
    case 'Home':
      event.preventDefault()
      event.stopPropagation()
      navigateToFirst()
      announceSelection()
      break
      
    case 'End':
      event.preventDefault()
      event.stopPropagation()
      navigateToLast()
      announceSelection()
      break
      
    case 'Enter':
    case ' ':
      event.preventDefault()
      event.stopPropagation()
      executeSelectedItem()
      break
      
    case 'Escape':
      event.preventDefault()
      event.stopPropagation()
      hideMenu()
      KeyboardAccessibilityUtils.announceMenuOperation('close')
      break
      
    case 'Tab':
      // 允许Tab键在菜单项之间导航
      if (event.shiftKey) {
        event.preventDefault()
        navigateMenu('up')
      } else {
        event.preventDefault()
        navigateMenu('down')
      }
      announceSelection()
      break
  }
}

const executeSelectedItem = (): void => {
  const group = menuGroups.value[selectedGroupIndex.value]
  if (group) {
    const item = group.items[selectedItemIndex.value]
    if (item) {
      executeItem(item)
    }
  }
}

const navigateToFirst = (): void => {
  if (menuGroups.value.length === 0) return
  
  selectedGroupIndex.value = 0
  selectedItemIndex.value = 0
  
  // 找到第一个有项目的组
  for (let i = 0; i < menuGroups.value.length; i++) {
    if (menuGroups.value[i].items.length > 0) {
      selectedGroupIndex.value = i
      selectedItemIndex.value = 0
      break
    }
  }
  
  nextTick(() => {
    scrollToSelectedItem()
  })
}

const navigateToLast = (): void => {
  if (menuGroups.value.length === 0) return
  
  // 找到最后一个有项目的组
  for (let i = menuGroups.value.length - 1; i >= 0; i--) {
    if (menuGroups.value[i].items.length > 0) {
      selectedGroupIndex.value = i
      selectedItemIndex.value = menuGroups.value[i].items.length - 1
      break
    }
  }
  
  nextTick(() => {
    scrollToSelectedItem()
  })
}

const announceSelection = (): void => {
  if (!keyboardMode.value) return
  
  const group = menuGroups.value[selectedGroupIndex.value]
  if (!group) return
  
  const item = group.items[selectedItemIndex.value]
  if (!item) return
  
  // Calculate flat index for position context
  let flatIndex = 0
  for (let i = 0; i < selectedGroupIndex.value; i++) {
    flatIndex += menuGroups.value[i].items.length
  }
  flatIndex += selectedItemIndex.value
  
  const totalItems = menuGroups.value.reduce((sum, g) => sum + g.items.length, 0)
  
  // Use enhanced announcement with context
  KeyboardAccessibilityUtils.announceWithContext(
    item.label,
    {
      position: flatIndex,
      totalItems,
      operation: item.shortcut ? `快捷键 ${formatShortcut(item.shortcut)}` : '选中'
    }
  )
}

const announceToScreenReader = (message: string): void => {
  // 创建临时的 aria-live 区域
  const announcement = document.createElement('div')
  announcement.setAttribute('aria-live', 'polite')
  announcement.setAttribute('aria-atomic', 'true')
  announcement.style.position = 'absolute'
  announcement.style.left = '-10000px'
  announcement.style.width = '1px'
  announcement.style.height = '1px'
  announcement.style.overflow = 'hidden'
  
  document.body.appendChild(announcement)
  announcement.textContent = message
  
  // 清理
  setTimeout(() => {
    document.body.removeChild(announcement)
  }, 1000)
}

const getActiveDescendantId = (): string => {
  if (!keyboardMode.value) return ''
  
  const group = menuGroups.value[selectedGroupIndex.value]
  if (!group) return ''
  
  const item = group.items[selectedItemIndex.value]
  if (!item) return ''
  
  return `click-menu-item-${item.id}`
}

const getMenuItemId = (item: ClickMenuItem): string => {
  return `click-menu-item-${item.id}`
}

const handleMouseEnter = (): void => {
  keyboardMode.value = false
}

const handleMouseLeave = (): void => {
  // 可以在这里添加延迟隐藏逻辑
}

const updatePosition = (): void => {
  if (!menuRef.value || !isVisible.value) return

  nextTick(() => {
    const menuRect = menuRef.value!.getBoundingClientRect()
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    const padding = 16
    
    let { x, y } = menuPosition.value
    
    // 智能定位：优先显示在右侧，如果空间不足则显示在左侧
    if (props.positioning === 'auto') {
      // 检查右侧空间
      if (x + menuRect.width + padding > viewportWidth) {
        // 右侧空间不足，尝试左侧
        const leftX = x - menuRect.width - padding
        if (leftX >= padding) {
          x = leftX
        } else {
          // 两侧都不足，居中显示
          x = Math.max(padding, (viewportWidth - menuRect.width) / 2)
        }
      }
    } else if (props.positioning === 'left') {
      x = Math.max(padding, x - menuRect.width - padding)
    } else if (props.positioning === 'right') {
      x = Math.min(viewportWidth - menuRect.width - padding, x + padding)
    }
    
    // 垂直位置调整：优先显示在下方，空间不足时显示在上方
    if (y + menuRect.height + padding > viewportHeight) {
      const topY = y - menuRect.height - padding
      if (topY >= padding) {
        y = topY
      } else {
        // 上下都不足，居中显示
        y = Math.max(padding, (viewportHeight - menuRect.height) / 2)
      }
    }
    
    // 确保菜单不会超出视口边界
    x = Math.max(padding, Math.min(x, viewportWidth - menuRect.width - padding))
    y = Math.max(padding, Math.min(y, viewportHeight - menuRect.height - padding))
    
    // 如果位置发生变化，更新位置
    if (x !== menuPosition.value.x || y !== menuPosition.value.y) {
      emit('positionUpdate', { x, y })
    }
  })
}

const onEnter = (el: Element): void => {
  updatePosition()
  
  // Add enhanced ARIA attributes
  const nodeType = targetNode.value?.type.name
  KeyboardAccessibilityUtils.addEnhancedClickMenuAria(
    el as HTMLElement,
    visibleItems.value.length,
    nodeType,
    `click-menu-${Date.now()}`
  )
  
  // 聚焦菜单以支持键盘导航
  if (props.keyboardNavigation) {
    nextTick(() => {
      (el as HTMLElement).focus()
      
      // Enhanced menu opening announcement
      KeyboardAccessibilityUtils.announceMenuOperation(
        'open',
        `共 ${visibleItems.value.length} 个选项，使用方向键导航，F1获取帮助`
      )
      
      // Announce first item if available
      if (visibleItems.value.length > 0) {
        setTimeout(() => {
          announceSelection()
        }, 500)
      }
    })
  }
}

const onLeave = (): void => {
  // 清理状态
  selectedGroupIndex.value = 0
  selectedItemIndex.value = 0
  keyboardMode.value = false
}

// 监听菜单状态变化
watch(() => props.menuState, (newState) => {
  if (newState?.visible) {
    selectedGroupIndex.value = 0
    selectedItemIndex.value = 0
    nextTick(() => {
      updatePosition()
    })
  }
}, { immediate: true })

// 监听窗口大小变化
const handleResize = (): void => {
  if (isVisible.value) {
    updatePosition()
  }
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.click-menu {
  background: var(--color-background, #ffffff);
  border: 1px solid var(--color-border, #e5e7eb);
  border-radius: 8px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  padding: 4px;
  min-width: 200px;
  max-width: 300px;
  max-height: 400px;
  overflow-y: auto;
  font-size: 14px;
  outline: none;

  &--mobile {
    min-width: 250px;
    font-size: 16px;
  }

  &--keyboard {
    .click-menu__item--selected {
      background: var(--color-primary, #3b82f6);
      color: var(--color-primary-contrast, #ffffff);
    }
  }

  &__group {
    &:not(:last-child) {
      margin-bottom: 4px;
    }

    &-label {
      padding: 8px 12px 4px;
      font-size: 12px;
      font-weight: 600;
      color: var(--color-text-secondary, #6b7280);
      text-transform: uppercase;
      letter-spacing: 0.05em;
    }
  }

  &__item {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    color: var(--color-text, #111827);

    &:hover:not(&--disabled) {
      background: var(--color-background-hover, #f9fafb);
    }

    &--selected {
      background: var(--color-background-hover, #f9fafb);
    }

    &--disabled {
      opacity: 0.5;
      cursor: not-allowed;
    }

    &-icon {
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      
      :deep(svg) {
        width: 16px;
        height: 16px;
      }
    }

    &-label {
      flex: 1;
      font-weight: 500;
    }

    &-shortcut {
      font-size: 12px;
      color: var(--color-text-secondary, #6b7280);
      opacity: 0.7;
      font-family: var(--font-mono, 'SF Mono', Monaco, 'Cascadia Code', monospace);
    }
  }

  &__divider {
    height: 1px;
    background: var(--color-border, #e5e7eb);
    margin: 4px 0;
  }

  &__empty {
    padding: 16px 12px;
    text-align: center;
    color: var(--color-text-secondary, #6b7280);
    font-style: italic;
  }

  &__loading {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 16px 12px;
    color: var(--color-text-secondary, #6b7280);

    &-spinner {
      width: 16px;
      height: 16px;
      border: 2px solid var(--color-border, #e5e7eb);
      border-top-color: var(--color-primary, #3b82f6);
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
}

// 动画
.click-menu-enter-active,
.click-menu-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.click-menu-enter-from,
.click-menu-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(-8px);
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

// 移动端适配
@media (max-width: 768px) {
  .click-menu {
    &__item {
      padding: 12px 16px;
      
      &-icon {
        width: 20px;
        height: 20px;
        
        :deep(svg) {
          width: 20px;
          height: 20px;
        }
      }
    }
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .click-menu {
    border: 2px solid var(--color-border, #e5e7eb);
    
    &__item {
      border: 1px solid transparent;
      
      &:hover:not(&--disabled),
      &--selected {
        border-color: var(--color-primary, #3b82f6);
        background: var(--color-primary, #3b82f6);
        color: var(--color-primary-contrast, #ffffff);
      }
      
      &:focus {
        outline: 2px solid var(--color-primary, #3b82f6);
        outline-offset: 2px;
      }
    }
  }
}

// 焦点可见性增强
.click-menu__item:focus-visible,
.click-menu__item:focus {
  outline: 2px solid var(--color-primary, #3b82f6);
  outline-offset: 2px;
}

// 键盘导航模式下的增强焦点样式
.click-menu--keyboard .click-menu__item--selected {
  outline: 3px solid var(--color-primary, #3b82f6);
  outline-offset: 1px;
  box-shadow: 0 0 0 1px var(--color-background, #ffffff), 0 0 0 5px var(--color-primary, #3b82f6);
}

// 高对比度模式下的增强样式
@media (prefers-contrast: high) {
  .click-menu--keyboard .click-menu__item--selected {
    outline-width: 4px;
    background: var(--color-primary, #3b82f6) !important;
    color: var(--color-primary-contrast, #ffffff) !important;
  }
}

// 减少动画
@media (prefers-reduced-motion: reduce) {
  .click-menu-enter-active,
  .click-menu-leave-active,
  .click-menu__item,
  .click-menu__loading-spinner {
    transition: none !important;
    animation: none !important;
  }
}
</style>