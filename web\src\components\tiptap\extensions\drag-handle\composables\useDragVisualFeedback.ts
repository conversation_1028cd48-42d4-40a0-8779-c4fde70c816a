import { ref, reactive, onUnmounted } from 'vue'
import type { DragOperation, DropZone } from '../types'

interface VisualFeedbackState {
  ghostImages: HTMLElement[]
  dropIndicators: HTMLElement[]
  dragPreviews: HTMLElement[]
  activeAnimations: Animation[]
}

export function useDragVisualFeedback() {
  const feedbackState = reactive<VisualFeedbackState>({
    ghostImages: [],
    dropIndicators: [],
    dragPreviews: [],
    activeAnimations: []
  })

  const isAnimating = ref(false)

  /**
   * 创建拖拽幽灵图像
   */
  const createGhostImage = (element: HTMLElement, opacity: number = 0.5): HTMLElement => {
    try {
      // 克隆元素
      const ghost = element.cloneNode(true) as HTMLElement
      
      // 设置幽灵图像样式
      ghost.className = 'drag-ghost-image'
      ghost.style.cssText = `
        position: absolute !important;
        top: -1000px !important;
        left: -1000px !important;
        opacity: ${opacity} !important;
        pointer-events: none !important;
        user-select: none !important;
        transform: rotate(5deg) scale(0.95) !important;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;
        border-radius: 8px !important;
        max-width: 300px !important;
        z-index: 1000 !important;
        background: var(--color-background, #ffffff) !important;
        border: 1px solid var(--color-border, #e5e7eb) !important;
        transition: none !important;
      `

      // 清理内部不需要的元素
      cleanupGhostContent(ghost)
      
      // 添加到页面
      document.body.appendChild(ghost)
      feedbackState.ghostImages.push(ghost)
      
      return ghost
    } catch (error) {
      console.error('Error creating ghost image:', error)
      return element.cloneNode(true) as HTMLElement
    }
  }

  /**
   * 清理幽灵图像内容
   */
  const cleanupGhostContent = (ghost: HTMLElement): void => {
    // 移除拖拽句柄
    const handles = ghost.querySelectorAll('.drag-handle')
    handles.forEach(handle => handle.remove())
    
    // 移除点击菜单
    const menus = ghost.querySelectorAll('.click-menu')
    menus.forEach(menu => menu.remove())
    
    // 移除工具提示
    const tooltips = ghost.querySelectorAll('[title]')
    tooltips.forEach(el => el.removeAttribute('title'))
    
    // 移除事件监听器属性
    const interactiveElements = ghost.querySelectorAll('[onclick], [onmousedown], [onmouseup]')
    interactiveElements.forEach(el => {
      el.removeAttribute('onclick')
      el.removeAttribute('onmousedown')
      el.removeAttribute('onmouseup')
    })
  }

  /**
   * 创建拖拽预览
   */
  const createDragPreview = (element: HTMLElement, x: number, y: number): HTMLElement => {
    const preview = element.cloneNode(true) as HTMLElement
    
    preview.className = 'drag-preview'
    preview.style.cssText = `
      position: fixed !important;
      left: ${x}px !important;
      top: ${y}px !important;
      opacity: 0.8 !important;
      pointer-events: none !important;
      z-index: 1001 !important;
      transform: rotate(3deg) scale(0.9) !important;
      box-shadow: 0 12px 48px rgba(0, 0, 0, 0.3) !important;
      border-radius: 8px !important;
      max-width: 280px !important;
      background: var(--color-background, #ffffff) !important;
      border: 2px solid var(--color-primary, #3b82f6) !important;
      transition: transform 0.2s ease !important;
    `
    
    cleanupGhostContent(preview)
    document.body.appendChild(preview)
    feedbackState.dragPreviews.push(preview)
    
    return preview
  }

  /**
   * 更新拖拽预览位置
   */
  const updateDragPreview = (preview: HTMLElement, x: number, y: number): void => {
    if (preview && preview.parentNode) {
      preview.style.left = `${x - 10}px`
      preview.style.top = `${y - 10}px`
    }
  }

  /**
   * 显示拖放区域
   */
  const showDropZones = (zones: DropZone[]): void => {
    try {
      zones.forEach((zone, index) => {
        if (zone.isValid) {
          // 添加拖放区域样式
          zone.element.classList.add('drop-zone')
          
          // 创建拖放指示器
          if (!zone.visualIndicator) {
            zone.visualIndicator = createDropIndicator(zone.element, index)
          }
          
          // 添加悬停效果监听器
          addDropZoneListeners(zone)
        } else {
          // 无效拖放区域
          zone.element.classList.add('drop-zone-invalid')
        }
      })
    } catch (error) {
      console.error('Error showing drop zones:', error)
    }
  }

  /**
   * 创建拖放指示器
   */
  const createDropIndicator = (element: HTMLElement, index: number): HTMLElement => {
    const indicator = document.createElement('div')
    indicator.className = 'drop-zone-indicator'
    indicator.setAttribute('data-drop-index', index.toString())
    
    indicator.style.cssText = `
      position: absolute;
      top: -3px;
      left: -4px;
      right: -4px;
      height: 6px;
      background: linear-gradient(90deg, var(--color-primary, #3b82f6), var(--color-primary-light, #60a5fa));
      border-radius: 3px;
      opacity: 0;
      transform: scaleX(0);
      transform-origin: left center;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      z-index: 100;
      pointer-events: none;
      box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
    `
    
    // 添加脉动效果
    const pulse = document.createElement('div')
    pulse.style.cssText = `
      position: absolute;
      top: 50%;
      left: 50%;
      width: 12px;
      height: 12px;
      background: var(--color-primary, #3b82f6);
      border-radius: 50%;
      transform: translate(-50%, -50%) scale(0);
      animation: pulse 1.5s infinite;
    `
    
    indicator.appendChild(pulse)
    
    // 确保父元素有相对定位
    if (getComputedStyle(element).position === 'static') {
      element.style.position = 'relative'
    }
    
    element.appendChild(indicator)
    feedbackState.dropIndicators.push(indicator)
    
    return indicator
  }

  /**
   * 添加拖放区域监听器
   */
  const addDropZoneListeners = (zone: DropZone): void => {
    const element = zone.element
    const indicator = zone.visualIndicator
    
    if (!indicator) return
    
    const handleDragEnter = (e: DragEvent) => {
      e.preventDefault()
      showDropIndicator(indicator, true)
      element.classList.add('drop-zone-active')
    }
    
    const handleDragOver = (e: DragEvent) => {
      e.preventDefault()
      e.dataTransfer!.dropEffect = 'move'
    }
    
    const handleDragLeave = (e: DragEvent) => {
      // 只有当真正离开元素时才隐藏指示器
      if (!element.contains(e.relatedTarget as Node)) {
        showDropIndicator(indicator, false)
        element.classList.remove('drop-zone-active')
      }
    }
    
    element.addEventListener('dragenter', handleDragEnter)
    element.addEventListener('dragover', handleDragOver)
    element.addEventListener('dragleave', handleDragLeave)
    
    // 存储监听器以便清理
    ;(element as any)._dropListeners = {
      dragenter: handleDragEnter,
      dragover: handleDragOver,
      dragleave: handleDragLeave
    }
  }

  /**
   * 显示/隐藏拖放指示器
   */
  const showDropIndicator = (indicator: HTMLElement, show: boolean): void => {
    if (show) {
      indicator.style.opacity = '1'
      indicator.style.transform = 'scaleX(1)'
    } else {
      indicator.style.opacity = '0'
      indicator.style.transform = 'scaleX(0)'
    }
  }

  /**
   * 隐藏拖放区域
   */
  const hideDropZones = (): void => {
    try {
      // 清理拖放指示器
      feedbackState.dropIndicators.forEach(indicator => {
        if (indicator.parentNode) {
          indicator.parentNode.removeChild(indicator)
        }
      })
      
      // 清理拖放区域样式和监听器
      document.querySelectorAll('.drop-zone, .drop-zone-active, .drop-zone-invalid').forEach(element => {
        element.classList.remove('drop-zone', 'drop-zone-active', 'drop-zone-invalid')
        
        // 移除监听器
        const listeners = (element as any)._dropListeners
        if (listeners) {
          element.removeEventListener('dragenter', listeners.dragenter)
          element.removeEventListener('dragover', listeners.dragover)
          element.removeEventListener('dragleave', listeners.dragleave)
          delete (element as any)._dropListeners
        }
      })
      
      feedbackState.dropIndicators = []
    } catch (error) {
      console.error('Error hiding drop zones:', error)
    }
  }

  /**
   * 成功拖放动画
   */
  const animateSuccessfulDrop = (fromElement: HTMLElement, toElement?: HTMLElement): Promise<void> => {
    return new Promise((resolve) => {
      try {
        isAnimating.value = true
        
        // 添加成功样式
        fromElement.classList.add('drop-success')
        if (toElement) {
          toElement.classList.add('drop-target-success')
        }
        
        // 创建成功动画
        const successAnimation = fromElement.animate([
          { transform: 'scale(1)', opacity: '1' },
          { transform: 'scale(1.05)', opacity: '0.8' },
          { transform: 'scale(1)', opacity: '1' }
        ], {
          duration: 400,
          easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
        })
        
        feedbackState.activeAnimations.push(successAnimation)
        
        // 添加粒子效果
        createSuccessParticles(fromElement)
        
        successAnimation.onfinish = () => {
          fromElement.classList.remove('drop-success')
          if (toElement) {
            toElement.classList.remove('drop-target-success')
          }
          isAnimating.value = false
          resolve()
        }
      } catch (error) {
        console.error('Error in success animation:', error)
        isAnimating.value = false
        resolve()
      }
    })
  }

  /**
   * 创建成功粒子效果
   */
  const createSuccessParticles = (element: HTMLElement): void => {
    const rect = element.getBoundingClientRect()
    const particleCount = 6
    
    for (let i = 0; i < particleCount; i++) {
      const particle = document.createElement('div')
      particle.style.cssText = `
        position: fixed;
        left: ${rect.left + rect.width / 2}px;
        top: ${rect.top + rect.height / 2}px;
        width: 4px;
        height: 4px;
        background: var(--color-primary, #3b82f6);
        border-radius: 50%;
        pointer-events: none;
        z-index: 1002;
      `
      
      document.body.appendChild(particle)
      
      const angle = (i / particleCount) * Math.PI * 2
      const distance = 50 + Math.random() * 30
      const duration = 600 + Math.random() * 200
      
      const animation = particle.animate([
        { 
          transform: 'translate(0, 0) scale(1)',
          opacity: '1'
        },
        { 
          transform: `translate(${Math.cos(angle) * distance}px, ${Math.sin(angle) * distance}px) scale(0)`,
          opacity: '0'
        }
      ], {
        duration,
        easing: 'cubic-bezier(0.4, 0, 0.2, 1)'
      })
      
      animation.onfinish = () => {
        if (particle.parentNode) {
          particle.parentNode.removeChild(particle)
        }
      }
    }
  }

  /**
   * 取消拖拽动画
   */
  const animateCancelledDrag = (element: HTMLElement): Promise<void> => {
    return new Promise((resolve) => {
      try {
        isAnimating.value = true
        
        // 添加取消样式
        element.classList.add('drag-cancelled')
        
        // 创建摇摆动画
        const cancelAnimation = element.animate([
          { transform: 'translateX(0) rotate(0deg)', opacity: '0.5' },
          { transform: 'translateX(-10px) rotate(-2deg)', opacity: '0.7' },
          { transform: 'translateX(10px) rotate(2deg)', opacity: '0.7' },
          { transform: 'translateX(-5px) rotate(-1deg)', opacity: '0.8' },
          { transform: 'translateX(0) rotate(0deg)', opacity: '1' }
        ], {
          duration: 500,
          easing: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
        })
        
        feedbackState.activeAnimations.push(cancelAnimation)
        
        cancelAnimation.onfinish = () => {
          element.classList.remove('drag-cancelled')
          isAnimating.value = false
          resolve()
        }
      } catch (error) {
        console.error('Error in cancel animation:', error)
        isAnimating.value = false
        resolve()
      }
    })
  }

  /**
   * 创建拖拽轨迹效果
   */
  const createDragTrail = (x: number, y: number): void => {
    const trail = document.createElement('div')
    trail.style.cssText = `
      position: fixed;
      left: ${x}px;
      top: ${y}px;
      width: 8px;
      height: 8px;
      background: var(--color-primary, #3b82f6);
      border-radius: 50%;
      pointer-events: none;
      z-index: 999;
      opacity: 0.6;
    `
    
    document.body.appendChild(trail)
    
    // 淡出动画
    const fadeOut = trail.animate([
      { opacity: '0.6', transform: 'scale(1)' },
      { opacity: '0', transform: 'scale(0.3)' }
    ], {
      duration: 300,
      easing: 'ease-out'
    })
    
    fadeOut.onfinish = () => {
      if (trail.parentNode) {
        trail.parentNode.removeChild(trail)
      }
    }
  }

  /**
   * 高亮拖放目标
   */
  const highlightDropTarget = (element: HTMLElement, highlight: boolean): void => {
    if (highlight) {
      element.classList.add('drop-target-highlight')
      
      // 添加脉动边框效果
      const border = document.createElement('div')
      border.className = 'drop-target-border'
      border.style.cssText = `
        position: absolute;
        top: -2px;
        left: -2px;
        right: -2px;
        bottom: -2px;
        border: 2px solid var(--color-primary, #3b82f6);
        border-radius: 6px;
        pointer-events: none;
        z-index: 99;
        animation: pulse-border 1s infinite;
      `
      
      element.style.position = 'relative'
      element.appendChild(border)
    } else {
      element.classList.remove('drop-target-highlight')
      const border = element.querySelector('.drop-target-border')
      if (border) {
        border.remove()
      }
    }
  }

  /**
   * 清理所有视觉反馈
   */
  const cleanup = (): void => {
    try {
      // 停止所有动画
      feedbackState.activeAnimations.forEach(animation => {
        animation.cancel()
      })
      feedbackState.activeAnimations = []
      
      // 清理幽灵图像
      feedbackState.ghostImages.forEach(ghost => {
        if (ghost.parentNode) {
          ghost.parentNode.removeChild(ghost)
        }
      })
      feedbackState.ghostImages = []
      
      // 清理拖拽预览
      feedbackState.dragPreviews.forEach(preview => {
        if (preview.parentNode) {
          preview.parentNode.removeChild(preview)
        }
      })
      feedbackState.dragPreviews = []
      
      // 隐藏拖放区域
      hideDropZones()
      
      // 清理所有相关的CSS类
      document.querySelectorAll('.dragging, .drop-success, .drag-cancelled, .drop-target-success, .drop-target-highlight').forEach(element => {
        element.classList.remove('dragging', 'drop-success', 'drag-cancelled', 'drop-target-success', 'drop-target-highlight')
      })
      
      // 清理拖拽轨迹
      document.querySelectorAll('.drag-trail').forEach(trail => trail.remove())
      
      // 清理边框效果
      document.querySelectorAll('.drop-target-border').forEach(border => border.remove())
      
      isAnimating.value = false
    } catch (error) {
      console.error('Error during visual feedback cleanup:', error)
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    cleanup()
  })

  return {
    // 状态
    feedbackState,
    isAnimating,
    
    // 幽灵图像
    createGhostImage,
    createDragPreview,
    updateDragPreview,
    
    // 拖放区域
    showDropZones,
    hideDropZones,
    highlightDropTarget,
    
    // 动画
    animateSuccessfulDrop,
    animateCancelledDrag,
    
    // 效果
    createDragTrail,
    createSuccessParticles,
    
    // 清理
    cleanup
  }
}