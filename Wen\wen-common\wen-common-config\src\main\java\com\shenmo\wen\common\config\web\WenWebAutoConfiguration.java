package com.shenmo.wen.common.config.web;

import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.AutoConfigureAfter;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.jackson.Jackson2ObjectMapperBuilderCustomizer;
import org.springframework.boot.autoconfigure.web.client.RestTemplateAutoConfiguration;
import org.springframework.boot.web.client.RestTemplateBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.web.client.RestTemplate;

import java.util.TimeZone;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@AutoConfiguration
@AutoConfigureAfter(RestTemplateAutoConfiguration.class)
public class WenWebAutoConfiguration {

    /**
     * 自定义jackson对象映射
     *
     * <AUTHOR>
     */
    @Bean
    @ConditionalOnMissingBean(name = "wenJackson2ObjectMapperBuilderCustomizer")
    public Jackson2ObjectMapperBuilderCustomizer wenJackson2ObjectMapperBuilderCustomizer() {
        return jacksonObjectMapperBuilder -> jacksonObjectMapperBuilder
                // 时区配置
                .timeZone(TimeZone.getDefault())
                // 将Long类型转为String类型（前端无法处理Long类型数值 会造成精度丢失）
                .serializerByType(Long.class, ToStringSerializer.instance)
                .serializerByType(Long.TYPE, ToStringSerializer.instance);
    }

    /**
     * 自定义RestTemplate
     *
     * @param builder
     * @return
     */
    @Bean
    @ConditionalOnBean(RestTemplateBuilder.class)
    @ConditionalOnMissingBean(name = "wenRestTemplate")
    public RestTemplate wenRestTemplate(RestTemplateBuilder builder) {
        return builder.build();
    }
}
