<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.shenmo.wen</groupId>
    <artifactId>wen-common</artifactId>
    <version>1.0.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>common</name>
    <modules>
        <module>wen-common-config</module>
        <module>wen-common-util</module>
        <module>wen-common-pojo</module>
        <module>wen-common-exception</module>
        <module>wen-common-constant</module>
        <module>wen-common-enumeration</module>
        <module>wen-common-mybatis</module>
        <module>wen-common-objectstorage</module>
        <module>wen-common-lock</module>
        <module>wen-common-messagesynchronizer</module>
    </modules>

    <properties>
        <version>1.0.0-SNAPSHOT</version>
        <java.version>17</java.version>
        <character.encoding>UTF-8</character.encoding>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <project.build.sourceEncoding>${character.encoding}</project.build.sourceEncoding>
        <project.reporting.outputEncoding>${character.encoding}</project.reporting.outputEncoding>
        <wen-dependency.version>${version}</wen-dependency.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>com.shenmo.wen</groupId>
                <artifactId>wen-dependency</artifactId>
                <version>${wen-dependency.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.shenmo.wen</groupId>
                <artifactId>wen-common-config</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.shenmo.wen</groupId>
                <artifactId>wen-common-constant</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.shenmo.wen</groupId>
                <artifactId>wen-common-enumeration</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.shenmo.wen</groupId>
                <artifactId>wen-common-exception</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.shenmo.wen</groupId>
                <artifactId>wen-common-lock</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.shenmo.wen</groupId>
                <artifactId>wen-common-messagesynchronizer</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.shenmo.wen</groupId>
                <artifactId>wen-common-mybatis</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.shenmo.wen</groupId>
                <artifactId>wen-common-objectstorage</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.shenmo.wen</groupId>
                <artifactId>wen-common-pojo</artifactId>
                <version>${version}</version>
            </dependency>
            <dependency>
                <groupId>com.shenmo.wen</groupId>
                <artifactId>wen-common-util</artifactId>
                <version>${version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
