package com.shenmo.wen.common.exception;

import com.shenmo.wen.common.constant.StringConstant;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnum;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnumOption;
import lombok.Getter;
import org.springframework.http.HttpStatus;
import org.springframework.lang.NonNull;

import java.util.Objects;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Getter
public  class BaseException extends RuntimeException{


    /**
     * 异常响应码
     */
    private final Integer code;

    /**
     * 异常信息
     */
    private final String errorMessage;

    /**
     * 异常枚举
     */
    private final ExceptionEnum exceptionEnum;

    /**
     * 构造方法
     *
     * @param exceptionEnum 异常枚举
     * <AUTHOR>
     */
    public BaseException(ExceptionEnum exceptionEnum) {
        super(Objects.requireNonNull(exceptionEnum, "exceptionEnum requireNonNull").getMessage());
        this.exceptionEnum = exceptionEnum;
        this.code = exceptionEnum.getCode();
        this.errorMessage = exceptionEnum.getMessage();
    }

    /**
     * 构造方法
     *
     * @param httpStatus {@link HttpStatus}响应状态
     * @param message    异常信息
     * <AUTHOR>
     */
    public BaseException(HttpStatus httpStatus, String message) {
        super(Objects.requireNonNull(message, "message requireNonNull"));
        this.exceptionEnum = ExceptionEnumOption.of(httpStatus, message);
        this.code = exceptionEnum.getCode();
        this.errorMessage = message;
    }

    /**
     * 构造方法
     *
     * @param httpStatus  {@link HttpStatus}响应状态
     * @param description 异常简述
     * @param throwable   可抛出的错误信息
     * <AUTHOR>
     */
    public BaseException(HttpStatus httpStatus, String description, @NonNull Throwable throwable) {

        this(httpStatus, description, Optional.of(throwable)
                .map(t -> t.getMessage() + "\n" + Optional.ofNullable(t.getCause())
                        .map(tt -> tt.getMessage() + "\n" + Optional.ofNullable(tt.getCause())
                                .map(Throwable::getMessage)
                                .orElse(StringConstant.EMPTY))
                        .orElse(StringConstant.EMPTY))
                .get());
    }

    /**
     * 构造方法
     *
     * @param httpStatus  {@link HttpStatus}响应状态
     * @param description 异常简述
     * @param message     异常信息
     * <AUTHOR>
     */
    public BaseException(HttpStatus httpStatus, String description, String message) {
        this(httpStatus, description + System.lineSeparator() + message);
    }
}
