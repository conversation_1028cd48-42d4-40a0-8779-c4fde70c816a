/**
 * Menu positioning utilities
 */

export interface MenuPosition {
  x: number
  y: number
}

export type PositioningMode = 'auto' | 'left' | 'right'

export class MenuPositioningUtils {
  private static readonly MENU_WIDTH = 200
  private static readonly MENU_HEIGHT = 150
  private static readonly MARGIN = 8

  /**
   * Calculate optimal menu position based on target element and positioning mode
   */
  static calculatePosition(
    targetRect: DOMRect, 
    positioning: PositioningMode = 'auto'
  ): MenuPosition {
    const viewportWidth = window.innerWidth
    const viewportHeight = window.innerHeight
    const { MENU_WIDTH, MENU_HEIGHT, MARGIN } = this

    let x = targetRect.left
    let y = targetRect.bottom + MARGIN

    switch (positioning) {
      case 'left':
        x = targetRect.left - MENU_WIDTH - MARGIN
        y = targetRect.top
        break
      case 'right':
        x = targetRect.right + MARGIN
        y = targetRect.top
        break
      case 'auto':
      default:
        // Check right boundary
        if (x + MENU_WIDTH > viewportWidth) {
          x = targetRect.right - MENU_WIDTH
        }
        
        // Check bottom boundary
        if (y + MENU_HEIGHT > viewportHeight) {
          y = targetRect.top - MENU_HEIGHT - MARGIN
        }
        
        // Ensure within left boundary
        if (x < MARGIN) {
          x = MARGIN
        }
        
        // Ensure within top boundary
        if (y < MARGIN) {
          y = targetRect.bottom + MARGIN
        }
        break
    }

    return { x, y }
  }
}