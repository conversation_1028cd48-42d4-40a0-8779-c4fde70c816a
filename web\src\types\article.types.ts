import type { SearchUser } from '@/types/user.types'

import type { JSONContent } from '@tiptap/vue-3'
export interface Article {
  id: string
  title: string
  tags: string[]
  tag: string
  operationLevel: number
  publishedScope: number
  content: string
  contentObj: JSONContent
  publisher: string
  publisherAvatar: string
  isOwner: boolean
  ipLocation: string
  publishedAt: string
  likeCount: number
  isLike: boolean
  dislikeCount: number
  isDislike: boolean
  favoriteCount: number
  isFavorite: boolean
  commentCount: number
  lastModified: string
  exactPublishedAt?: string
  exactLastModified?: string
  showExactPublishTime?: boolean
  showExactModifyTime?: boolean
  shareUsers?: SearchUser[]
}
