<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>拖拽功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        
        .drag-handle {
            position: absolute;
            left: -2rem;
            top: 50%;
            transform: translateY(-50%);
            width: 1.5rem;
            height: 1.5rem;
            cursor: grab;
            opacity: 0;
            transition: all 0.2s ease;
            z-index: 10;
            border-radius: 6px;
            background: #f0f0f0;
            border: 1px solid #ccc;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .drag-handle:hover {
            opacity: 1;
            background: #e0e0e0;
        }
        
        .drag-handle:active {
            cursor: grabbing;
        }
        
        .test-paragraph {
            position: relative;
            margin: 10px 0;
            padding: 10px;
            border-left: 3px solid #007bff;
            background: #f8f9fa;
        }
        
        .test-paragraph:hover .drag-handle {
            opacity: 1;
        }
        
        .click-menu {
            position: fixed;
            background: white;
            border: 1px solid #ccc;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            padding: 8px;
            min-width: 200px;
            z-index: 1000;
            display: none;
        }
        
        .click-menu-item {
            padding: 8px 12px;
            cursor: pointer;
            border-radius: 4px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .click-menu-item:hover {
            background: #f0f0f0;
        }
    </style>
</head>
<body>
    <h1>编辑器拖拽功能测试</h1>
    
    <div class="test-section">
        <h2>问题描述</h2>
        <p>编辑文章的时候，编辑器怎么拖拽段落？click-menu与drag-handle都无效</p>
    </div>
    
    <div class="test-section">
        <h2>可能的原因</h2>
        <ul>
            <li><strong>样式文件未导入</strong> - drag-handle 和 click-menu 的样式文件没有在入口文件中导入</li>
            <li><strong>菜单项配置为空</strong> - click-menu 的 menuItems 配置为空数组</li>
            <li><strong>插件未正确初始化</strong> - 扩展的插件可能没有正确创建或绑定事件</li>
            <li><strong>DOM 元素选择器问题</strong> - 可能无法正确识别支持拖拽的元素</li>
        </ul>
    </div>
    
    <div class="test-section">
        <h2>解决方案</h2>
        <ol>
            <li><strong>修复样式导入</strong>
                <pre><code>// 在 web/src/components/tiptap/extensions/drag-handle/index.ts 中添加：
import './styles/drag-handle.scss'

// 在 web/src/components/tiptap/extensions/click-menu/index.ts 中添加：
import './styles/click-menu.scss'</code></pre>
            </li>
            
            <li><strong>配置默认菜单项</strong>
                <pre><code>// 在 ClickMenuExtension.ts 中：
import { createDefaultMenuItems } from './MenuItemRegistry'

addOptions() {
  return {
    enabled: true,
    menuItems: createDefaultMenuItems(), // 使用默认菜单项
    positioning: 'auto',
    showDelay: 100,
    hideDelay: 300,
    keyboardNavigation: true
  }
}</code></pre>
            </li>
            
            <li><strong>验证扩展配置</strong>
                <pre><code>// 在 web/src/utils/tiptap.ts 中确保正确配置：
[
  'dragHandle',
  DragHandleExtension.configure({
    enabled: true,
    supportedNodes: ['paragraph', 'heading', 'listItem', 'codeBlock'],
  }),
],
[
  'clickMenu',
  ClickMenuExtension.configure({
    enabled: true,
    positioning: 'auto',
  }),
]</code></pre>
            </li>
        </ol>
    </div>
    
    <div class="test-section">
        <h2>测试用例</h2>
        <div class="test-paragraph">
            <div class="drag-handle">⋮⋮</div>
            这是第一个测试段落。鼠标悬停应该显示拖拽手柄。
        </div>
        
        <div class="test-paragraph">
            <div class="drag-handle">⋮⋮</div>
            这是第二个测试段落。点击应该显示上下文菜单。
        </div>
        
        <div class="test-paragraph">
            <div class="drag-handle">⋮⋮</div>
            这是第三个测试段落。可以尝试拖拽重新排序。
        </div>
    </div>
    
    <div class="test-section">
        <h2>键盘快捷键</h2>
        <ul>
            <li><kbd>Ctrl+Space</kbd> - 显示点击菜单</li>
            <li><kbd>Alt+↑</kbd> - 向上移动段落</li>
            <li><kbd>Alt+↓</kbd> - 向下移动段落</li>
            <li><kbd>Ctrl+D</kbd> - 复制段落</li>
            <li><kbd>Delete</kbd> - 删除段落</li>
        </ul>
    </div>
    
    <!-- 模拟点击菜单 -->
    <div class="click-menu" id="clickMenu">
        <div class="click-menu-item">📋 复制段落</div>
        <div class="click-menu-item">🗑️ 删除段落</div>
        <div class="click-menu-item">↑ 上移</div>
        <div class="click-menu-item">↓ 下移</div>
        <div class="click-menu-item">H1 转为标题 1</div>
        <div class="click-menu-item">H2 转为标题 2</div>
        <div class="click-menu-item">P 转为段落</div>
    </div>
    
    <script>
        // 简单的点击菜单演示
        document.addEventListener('click', function(e) {
            const clickMenu = document.getElementById('clickMenu');
            
            if (e.target.classList.contains('test-paragraph')) {
                clickMenu.style.display = 'block';
                clickMenu.style.left = e.pageX + 'px';
                clickMenu.style.top = e.pageY + 'px';
            } else if (!clickMenu.contains(e.target)) {
                clickMenu.style.display = 'none';
            }
        });
        
        // 拖拽演示
        let draggedElement = null;
        
        document.querySelectorAll('.drag-handle').forEach(handle => {
            handle.addEventListener('dragstart', function(e) {
                draggedElement = this.parentElement;
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/html', draggedElement.outerHTML);
            });
        });
        
        document.querySelectorAll('.test-paragraph').forEach(paragraph => {
            paragraph.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
            });
            
            paragraph.addEventListener('drop', function(e) {
                e.preventDefault();
                if (draggedElement && draggedElement !== this) {
                    const parent = this.parentNode;
                    const draggedClone = draggedElement.cloneNode(true);
                    parent.insertBefore(draggedClone, this);
                    draggedElement.remove();
                    
                    // 重新绑定事件
                    bindDragEvents(draggedClone.querySelector('.drag-handle'));
                }
            });
        });
        
        function bindDragEvents(handle) {
            handle.addEventListener('dragstart', function(e) {
                draggedElement = this.parentElement;
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/html', draggedElement.outerHTML);
            });
        }
    </script>
</body>
</html>