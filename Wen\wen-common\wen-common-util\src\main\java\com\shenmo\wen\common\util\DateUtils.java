package com.shenmo.wen.common.util;


import com.google.common.collect.Lists;
import org.springframework.lang.Nullable;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;


/**
 * 时间工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public abstract class DateUtils {

    public static String YYYY = "yyyy";
    public static String YYYY_MM = "yyyy-MM";
    public static String YYYYMMDD = "yyyyMMdd";
    public static String YYYY_MM_DD = "yyyy-MM-dd";
    public static String YYYYMMDDHHMMSS = "yyyyMMddHHmmss";
    public static String YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
    private static final DateTimeFormatter FORMATTER_DATETIME = DateTimeFormatter.ofPattern(YYYY_MM_DD_HH_MM_SS);

    /**
     * 获取当前Date型日期
     *
     * @return Date() 当前日期
     * <AUTHOR>
     */
    public static Date nowDate() {
        return new Date();
    }

    /**
     * 获取默认格式的当前日期字符串，格式yyyy-MM-dd HH:mm:ss
     *
     * @return 当前日期字符串，格式yyyy-MM-dd HH:mm:ss
     * <AUTHOR>
     */
    public static String nowDateFormatDefault() {
        return nowDateFormat(YYYY_MM_DD_HH_MM_SS);
    }

    /**
     * 获取没有分割符的当前日期字符串，格式yyyyMMddHHmmss
     *
     * @return 当前日期字符串，格式yyyyMMddHHmmss
     * <AUTHOR>
     */
    public static String nowDateFormatNoSeparator() {
        return nowDateFormat(YYYYMMDDHHMMSS);
    }

    /**
     * 获取指定格式的当前日期字符串
     *
     * @param format 指定格式
     * @return 当前日期字符串
     * <AUTHOR>
     */
    public static String nowDateFormat(final String format) {
        return dateFormat(format, nowDate());
    }

    /**
     * date日期格式化为指定格式字符串
     *
     * @param format 指定格式
     * @param date   日期
     * @return 指定格式日期字符串
     * <AUTHOR>
     */
    @Nullable
    public static String dateFormat(final String format, final Date date) {
        final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        return Optional.ofNullable(date)
                .map(DateUtils::dateToLocalDateTime)
                .map(formatter::format)
                .orElse(null);
    }

    /**
     * date日期格式化为字符串
     *
     * @param date   日期
     * @return 指定格式日期字符串
     * <AUTHOR>
     */
    @Nullable
    public static String dateFormat( final Date date) {
        return dateFormat(YYYY_MM_DD_HH_MM_SS, date);
    }

    /**
     * 指定格式日期字符串转换为date
     *
     * @param format 指定格式
     * @param ts     指定格式日期字符串
     * @return date
     * <AUTHOR>
     */
    public static Date dateStrParse(final String format, final String ts) {
        final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(format);
        final LocalDateTime localDateTime = LocalDateTime.parse(ts, formatter);
        return localDateTimeToDate(localDateTime);
    }

    /**
     * 获得当天最大时间
     *
     * <AUTHOR>
     */
    public static Date getEndOfDay(Date date) {
        LocalDateTime localDateTime = java.time.LocalDateTime.ofInstant(Instant.ofEpochMilli(date.getTime()),
                ZoneId.systemDefault());
        LocalDateTime endOfDay = localDateTime.with(LocalTime.MAX);
        return Date.from(endOfDay.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 偏移日期
     *
     * @param date         日期
     * @param offset       偏移
     * @param calendarType 偏移类型，如Calendar.SECOND
     * @return 偏移后的日期
     */
    private static Date offset(Date date, int offset, int calendarType) {
        if (Objects.isNull(date)) {
            return null;
        }

        final Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(calendarType, offset);

        return calendar.getTime();
    }

    /**
     * 偏移毫秒数
     *
     * @param date   日期
     * @param offset 偏移毫秒数，正数向未来偏移，负数向历史偏移
     * @return 偏移后的日期
     */
    public static Date offsetMillisecond(Date date, int offset) {
        return offset(date, offset, Calendar.MILLISECOND);
    }

    /**
     * 偏移秒数
     *
     * @param date   日期
     * @param offset 偏移秒数，正数向未来偏移，负数向历史偏移
     * @return 偏移后的日期
     */
    public static Date offsetSecond(Date date, int offset) {
        return offset(date, offset, Calendar.SECOND);
    }

    /**
     * 偏移分钟
     *
     * @param date   日期
     * @param offset 偏移分钟数，正数向未来偏移，负数向历史偏移
     * @return 偏移后的日期
     */
    public static Date offsetMinute(Date date, int offset) {
        return offset(date, offset, Calendar.MINUTE);
    }

    /**
     * 偏移小时
     *
     * @param date   日期
     * @param offset 偏移小时数，正数向未来偏移，负数向历史偏移
     * @return 偏移后的日期
     */
    public static Date offsetHour(Date date, int offset) {
        return offset(date, offset, Calendar.HOUR);
    }

    /**
     * 偏移天
     *
     * @param date   日期
     * @param offset 偏移天数，正数向未来偏移，负数向历史偏移
     * @return 偏移后的日期
     */
    public static Date offsetDay(Date date, int offset) {
        return offset(date, offset, Calendar.DAY_OF_YEAR);
    }

    /**
     * 偏移周
     *
     * @param date   日期
     * @param offset 偏移周数，正数向未来偏移，负数向历史偏移
     * @return 偏移后的日期
     */
    public static Date offsetWeek(Date date, int offset) {
        return offset(date, offset, Calendar.WEEK_OF_YEAR);
    }

    /**
     * 偏移月
     *
     * @param date   日期
     * @param offset 偏移月数，正数向未来偏移，负数向历史偏移
     * @return 偏移后的日期
     */
    public static Date offsetMonth(Date date, int offset) {
        return offset(date, offset, Calendar.MONTH);
    }

    /**
     * 获取两个日期之间的所有日期，如果开始日期大于结束日期，方法将返回空list
     *
     * @param start   开始日期
     * @param end     结束时间
     * @param flag    是否检验开始时间和结束时间必须在当前时间之前
     * @param maxSize 最大返回数量（不做限制时，设置成null）
     * @return 该范围内的时间列表数据
     * <AUTHOR>
     */
    public static List<String> getBetweenDates(String start, String end, boolean flag, Integer maxSize) {
        List<String> result = new ArrayList<>(10);
        try {
            Date startDate = dateStrParse(DateUtils.YYYY_MM_DD_HH_MM_SS, start);
            Date endDate = dateStrParse(DateUtils.YYYY_MM_DD_HH_MM_SS, end);
            //是否检验开始时间和结束时间必须在当前时间之前
            Date now = getEndOfDay(new Date());
            if (flag && now.before(startDate)) {
                startDate = now;
            }
            if (flag && now.before(endDate)) {
                endDate = now;
            }
            Calendar tempStart = Calendar.getInstance();
            tempStart.setTime(startDate);
            Calendar tempEnd = Calendar.getInstance();
            tempEnd.setTime(endDate);
            int count = 0;
            while (tempStart.before(tempEnd) || tempStart.equals(tempEnd)) {
                result.add(dateFormat(DateUtils.YYYYMMDD, tempStart.getTime()));
                tempStart.add(Calendar.DAY_OF_YEAR, 1);
                count++;
                if (Objects.nonNull(maxSize) && 0 == maxSize.compareTo(count)) {
                    break;
                }
            }
        } catch (Exception e) {
            return Lists.newArrayList();
        }
        Collections.reverse(result);
        return result;
    }

    /**
     * Date转换为LocalDateTime，系统默认时区
     *
     * @param date date
     * @return localDateTime
     * <AUTHOR>
     */
    public static LocalDateTime dateToLocalDateTime(Date date) {
        if (Objects.isNull(date)) {
            return null;
        }

        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
    }

    /**
     * LocalDateTime转换为Date，系统默认时区
     *
     * @param localDateTime localDateTime
     * @return date
     * <AUTHOR>
     */
    public static Date localDateTimeToDate(LocalDateTime localDateTime) {
        if (Objects.isNull(localDateTime)) {
            return null;
        }

        return Date.from(localDateTime.atZone(ZoneId.systemDefault()).toInstant());
    }

    /**
     * 获取指定格式的当前时间字符串
     *
     * @param pattern 时间格式
     * @return 指定格式的当前时间字符串
     * <AUTHOR>
     */
    public static String now(String pattern) {

        return LocalDateTime.now().format(DateTimeFormatter.ofPattern(pattern));
    }
}
