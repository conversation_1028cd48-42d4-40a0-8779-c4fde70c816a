/**
 * 被动事件处理器
 * 用于确保滚轮和触摸事件使用正确的被动监听器配置
 * 专门处理编辑器中的各种事件监听需求
 */

import type { EditorEventListenerConfig } from './EditorEventOptimizer'

/**
 * 默认的被动事件配置
 */
const DEFAULT_PASSIVE_CONFIG: EditorEventListenerConfig = {
  passive: true,
}

/**
 * 需要被动监听的事件类型
 */
const PASSIVE_EVENTS = new Set([
  'wheel',
  'touchstart',
  'touchmove',
  'touchend',
  'touchcancel',
  'scroll',
])

/**
 * 需要非被动监听的事件类型（需要阻止默认行为）
 */
const NON_PASSIVE_EVENTS = new Set([
  'wheel', // 图片缩放等需要阻止默认滚动
])

/**
 * 安全的事件监听器添加函数
 * @param element 目标元素
 * @param eventType 事件类型
 * @param handler 事件处理函数
 * @param options 配置选项
 * @param forceNonPassive 强制使用非被动监听器
 */
export function addPassiveEventListener(
  element: Element | Document | Window,
  eventType: string,
  handler: EventListener,
  options: EditorEventListenerConfig = {},
  forceNonPassive = false,
): void {
  // 确定是否应该使用被动监听器
  const shouldBePassive = !forceNonPassive && PASSIVE_EVENTS.has(eventType)

  // 合并配置
  const config: EditorEventListenerConfig = {
    ...DEFAULT_PASSIVE_CONFIG,
    ...options,
    passive: shouldBePassive,
  }

  // 如果强制非被动或事件类型需要非被动，则设置为 false
  if (forceNonPassive || NON_PASSIVE_EVENTS.has(eventType)) {
    config.passive = false
  }

  try {
    element.addEventListener(eventType, handler, config)
  } catch (error) {
    console.warn(`Failed to add event listener for ${eventType}:`, error)
    // 降级到基本配置
    element.addEventListener(eventType, handler, config.passive)
  }
}

/**
 * 安全的事件监听器移除函数
 * @param element 目标元素
 * @param eventType 事件类型
 * @param handler 事件处理函数
 * @param options 配置选项
 */
export function removePassiveEventListener(
  element: Element | Document | Window,
  eventType: string,
  handler: EventListener,
  options: EditorEventListenerConfig = {},
): void {
  try {
    element.removeEventListener(eventType, handler, options)
  } catch (error) {
    console.warn(`Failed to remove event listener for ${eventType}:`, error)
  }
}

/**
 * 图片预览模态框的滚轮缩放事件监听器
 * @param modal 模态框元素
 * @param handler 滚轮事件处理函数
 * @returns 清理函数
 */
export function addImageZoomWheelListener(
  modal: Element,
  handler: (e: WheelEvent) => void,
): () => void {
  // 图片缩放需要阻止默认滚动行为，所以使用非被动监听器
  addPassiveEventListener(modal, 'wheel', handler as EventListener, {}, true)

  return () => {
    removePassiveEventListener(modal, 'wheel', handler as EventListener)
  }
}

/**
 * 触摸事件监听器配置
 * @param element 目标元素
 * @param handlers 事件处理函数映射
 * @returns 清理函数
 */
export function addTouchEventListeners(
  element: Element | Document,
  handlers: {
    touchstart?: EventListener
    touchmove?: EventListener
    touchend?: EventListener
    touchcancel?: EventListener
  },
): () => void {
  const cleanupFunctions: (() => void)[] = []

  Object.entries(handlers).forEach(([eventType, handler]) => {
    if (handler) {
      addPassiveEventListener(element, eventType, handler)
      cleanupFunctions.push(() => {
        removePassiveEventListener(element, eventType, handler)
      })
    }
  })

  return () => {
    cleanupFunctions.forEach((cleanup) => cleanup())
  }
}

// 编辑器事件优化函数已移至 EditorEventOptimizer.ts
// 如需使用这些函数，请直接从 EditorEventOptimizer.ts 导入
