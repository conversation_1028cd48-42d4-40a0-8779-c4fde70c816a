// Debug script to check extension loading
// Run this in the browser console when the article editor is open

console.log('=== TipTap Extensions Debug ===');

// Check if editor exists
const editorElement = document.querySelector('.ProseMirror');
if (!editorElement) {
  console.error('❌ No ProseMirror editor found');
} else {
  console.log('✅ ProseMirror editor found');
}

// Check for drag handles
const dragHandles = document.querySelectorAll('.drag-handle');
console.log(`Drag handles found: ${dragHandles.length}`);
if (dragHandles.length === 0) {
  console.warn('⚠️ No drag handles found in DOM');
} else {
  console.log('✅ Drag handles found:', dragHandles);
}

// Check for click menu
const clickMenus = document.querySelectorAll('.click-menu');
console.log(`Click menus found: ${clickMenus.length}`);

// Check for Vue app instance
const vueApp = document.querySelector('#app').__vueParentComponent;
if (vueApp) {
  console.log('✅ Vue app found');
} else {
  console.warn('⚠️ Vue app not found');
}

// Check for TipTap editor instance
try {
  const editorComponent = document.querySelector('[data-tiptap-editor]');
  if (editorComponent) {
    console.log('✅ TipTap editor component found');
  } else {
    console.warn('⚠️ TipTap editor component not found');
  }
} catch (e) {
  console.error('Error checking editor component:', e);
}

// Check for extension styles
const styleSheets = Array.from(document.styleSheets);
let foundDragHandleStyles = false;
let foundClickMenuStyles = false;

try {
  styleSheets.forEach(sheet => {
    try {
      const rules = Array.from(sheet.cssRules || sheet.rules || []);
      rules.forEach(rule => {
        if (rule.selectorText) {
          if (rule.selectorText.includes('.drag-handle')) {
            foundDragHandleStyles = true;
          }
          if (rule.selectorText.includes('.click-menu')) {
            foundClickMenuStyles = true;
          }
        }
      });
    } catch (e) {
      // Cross-origin stylesheet, skip
    }
  });
} catch (e) {
  console.error('Error checking stylesheets:', e);
}

console.log(`Drag handle styles found: ${foundDragHandleStyles ? '✅' : '❌'}`);
console.log(`Click menu styles found: ${foundClickMenuStyles ? '✅' : '❌'}`);

// Check for console errors
const originalError = console.error;
const errors = [];
console.error = function(...args) {
  errors.push(args.join(' '));
  originalError.apply(console, arguments);
};

setTimeout(() => {
  console.log('=== Console Errors ===');
  if (errors.length > 0) {
    errors.forEach(error => console.log('❌', error));
  } else {
    console.log('✅ No console errors detected');
  }
}, 2000);

// Test drag handle functionality
function testDragHandle() {
  console.log('=== Testing Drag Handle ===');
  
  const paragraphs = document.querySelectorAll('.ProseMirror p');
  if (paragraphs.length > 0) {
    console.log(`Found ${paragraphs.length} paragraphs`);
    
    // Simulate hover on first paragraph
    const firstP = paragraphs[0];
    const mouseEnterEvent = new MouseEvent('mouseenter', {
      bubbles: true,
      cancelable: true,
      view: window
    });
    
    firstP.dispatchEvent(mouseEnterEvent);
    
    setTimeout(() => {
      const dragHandles = document.querySelectorAll('.drag-handle');
      if (dragHandles.length > 0) {
        console.log('✅ Drag handle appeared on hover');
      } else {
        console.warn('⚠️ No drag handle appeared on hover');
      }
    }, 100);
  } else {
    console.warn('⚠️ No paragraphs found to test');
  }
}

// Test click menu functionality
function testClickMenu() {
  console.log('=== Testing Click Menu ===');
  
  const editorContent = document.querySelector('.ProseMirror');
  if (editorContent) {
    // Simulate click on editor
    const clickEvent = new MouseEvent('click', {
      bubbles: true,
      cancelable: true,
      view: window,
      clientX: 100,
      clientY: 100
    });
    
    editorContent.dispatchEvent(clickEvent);
    
    setTimeout(() => {
      const clickMenus = document.querySelectorAll('.click-menu');
      if (clickMenus.length > 0) {
        console.log('✅ Click menu appeared on click');
      } else {
        console.warn('⚠️ No click menu appeared on click');
      }
    }, 100);
  } else {
    console.warn('⚠️ No editor content found to test');
  }
}

// Run tests
setTimeout(() => {
  testDragHandle();
  testClickMenu();
}, 1000);

console.log('=== Debug script loaded ===');
console.log('Run testDragHandle() or testClickMenu() to test functionality');
