<template>
  <div
    ref="dragHandleRef"
    class="drag-handle"
    :class="{
      'drag-handle--visible': isVisible,
      'drag-handle--dragging': isDragging,
      'drag-handle--disabled': !enabled
    }"
    draggable="true"
    @dragstart="handleDragStart"
    @dragend="handleDragEnd"
    @mouseenter="handleMouseEnter"
    @mouseleave="handleMouseLeave"
    :aria-label="ariaLabel"
    :title="tooltip"
    role="button"
    tabindex="0"
    @keydown="handleKeyDown"
  >
    <div class="drag-handle__icon">
      <svg
        width="12"
        height="12"
        viewBox="0 0 12 12"
        fill="currentColor"
        aria-hidden="true"
      >
        <circle cx="3" cy="3" r="1" />
        <circle cx="9" cy="3" r="1" />
        <circle cx="3" cy="6" r="1" />
        <circle cx="9" cy="6" r="1" />
        <circle cx="3" cy="9" r="1" />
        <circle cx="9" cy="9" r="1" />
      </svg>
    </div>
    
    <!-- Screen reader feedback -->
    <div
      v-if="screenReaderMessage"
      class="sr-only"
      aria-live="polite"
      aria-atomic="true"
    >
      {{ screenReaderMessage }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import type { Editor } from '@tiptap/core'
import type { DragHandleOptions } from './types'
import { KeyboardAccessibilityUtils } from '../shared/utils/keyboardAccessibility'

interface Props {
  editor: Editor
  options: DragHandleOptions
  targetElement: HTMLElement
  position: number
}

const props = defineProps<Props>()

const dragHandleRef = ref<HTMLElement>()
const isVisible = ref(false)
const isDragging = ref(false)
const isHovered = ref(false)
const screenReaderMessage = ref('')

const enabled = computed(() => props.options.enabled)

const ariaLabel = computed(() => {
  const nodeType = getNodeTypeAtPosition()
  return `拖拽 ${nodeType} 以重新排序`
})

const tooltip = computed(() => {
  const nodeType = getNodeTypeAtPosition()
  return `拖拽此 ${nodeType} 或使用 Alt+↑/↓ 键移动`
})

function getNodeTypeAtPosition(): string {
  try {
    const node = props.editor.state.doc.nodeAt(props.position)
    if (!node) return '内容'
    
    switch (node.type.name) {
      case 'paragraph': return '段落'
      case 'heading': return '标题'
      case 'listItem': return '列表项'
      case 'codeBlock': return '代码块'
      default: return '内容'
    }
  } catch {
    return '内容'
  }
}

function handleDragStart(event: DragEvent) {
  if (!enabled.value || !event.dataTransfer) return

  isDragging.value = true
  screenReaderMessage.value = `开始拖拽 ${getNodeTypeAtPosition()}`

  // Set drag data
  event.dataTransfer.effectAllowed = 'move'
  event.dataTransfer.setData('text/html', props.targetElement.outerHTML)
  event.dataTransfer.setData('application/x-tiptap-drag-handle', props.position.toString())

  // Create ghost image
  const ghostImage = createGhostImage()
  event.dataTransfer.setDragImage(ghostImage, 0, 0)

  // Add dragging class to target element
  props.targetElement.classList.add('dragging')

  // Emit drag start event
  props.editor.emit('dragHandleStart', {
    position: props.position,
    element: props.targetElement,
    event
  })
}

function handleDragEnd(event: DragEvent) {
  isDragging.value = false
  screenReaderMessage.value = `结束拖拽 ${getNodeTypeAtPosition()}`

  // Remove dragging class
  props.targetElement.classList.remove('dragging')

  // Clean up ghost image
  const ghostImages = document.querySelectorAll('.drag-ghost-image')
  ghostImages.forEach(img => img.remove())

  // Emit drag end event
  props.editor.emit('dragHandleEnd', {
    position: props.position,
    element: props.targetElement,
    event
  })

  // Clear screen reader message after a delay
  setTimeout(() => {
    screenReaderMessage.value = ''
  }, 1000)
}

function createGhostImage(): HTMLElement {
  const ghost = props.targetElement.cloneNode(true) as HTMLElement
  ghost.className = 'drag-ghost-image'
  ghost.style.position = 'absolute'
  ghost.style.top = '-1000px'
  ghost.style.left = '-1000px'
  ghost.style.opacity = props.options.ghostImageOpacity.toString()
  ghost.style.pointerEvents = 'none'
  ghost.style.transform = 'rotate(5deg)'
  ghost.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.2)'
  ghost.style.borderRadius = '8px'
  ghost.style.maxWidth = '300px'
  ghost.style.zIndex = '1000'
  
  document.body.appendChild(ghost)
  return ghost
}

function handleMouseEnter() {
  if (!enabled.value) return
  isHovered.value = true
  isVisible.value = true
}

function handleMouseLeave() {
  if (!enabled.value) return
  isHovered.value = false
  
  // Keep visible for a short time to allow interaction
  setTimeout(() => {
    if (!isHovered.value && !isDragging.value) {
      isVisible.value = false
    }
  }, 100)
}

function handleKeyDown(event: KeyboardEvent) {
  if (!enabled.value) return

  const nodeType = getNodeTypeAtPosition()
  const node = props.editor.state.doc.nodeAt(props.position)
  
  if (!node) return

  // Use enhanced keyboard handler
  const handled = KeyboardAccessibilityUtils.handleEnhancedDragKeyboard(
    props.editor, event, node, props.position
  )
  
  if (handled) {
    event.preventDefault()
    return
  }

  // Handle component-specific keys
  switch (event.key) {
    case 'Enter':
    case ' ':
      event.preventDefault()
      screenReaderMessage.value = `已激活拖拽模式，使用Alt+方向键移动${nodeType}`
      KeyboardAccessibilityUtils.announceKeyboardHelp('drag')
      break
      
    case 'F1':
    case '?':
      if (!event.shiftKey) {
        event.preventDefault()
        KeyboardAccessibilityUtils.announceKeyboardHelp('drag')
      }
      break
      
    case 'Escape':
      if (isDragging.value) {
        event.preventDefault()
        isDragging.value = false
        screenReaderMessage.value = '已取消拖拽操作'
        KeyboardAccessibilityUtils.announceDragOperation('cancel', nodeType)
      }
      break
  }
}

// Show/hide based on parent element hover
function setupHoverDetection() {
  if (!props.targetElement) return

  const showHandle = () => {
    if (enabled.value) {
      isVisible.value = true
    }
  }

  const hideHandle = () => {
    if (enabled.value && !isHovered.value && !isDragging.value) {
      isVisible.value = false
    }
  }

  props.targetElement.addEventListener('mouseenter', showHandle)
  props.targetElement.addEventListener('mouseleave', hideHandle)

  // Cleanup function
  return () => {
    props.targetElement.removeEventListener('mouseenter', showHandle)
    props.targetElement.removeEventListener('mouseleave', hideHandle)
  }
}

let cleanupHoverDetection: (() => void) | undefined

onMounted(() => {
  cleanupHoverDetection = setupHoverDetection()
})

onUnmounted(() => {
  if (cleanupHoverDetection) {
    cleanupHoverDetection()
  }
  
  // Clean up any remaining ghost images
  const ghostImages = document.querySelectorAll('.drag-ghost-image')
  ghostImages.forEach(img => img.remove())
})
</script>

<style lang="scss" scoped>
.drag-handle {
  position: absolute;
  left: -2rem;
  top: 50%;
  transform: translateY(-50%);
  width: 1.5rem;
  height: 1.5rem;
  cursor: grab;
  opacity: 0;
  transition: opacity 0.2s ease, transform 0.2s ease;
  z-index: 10;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover,
  &:focus,
  &.keyboard-focused {
    opacity: 1;
    transform: translateY(-50%) scale(1.1);
    outline: 2px solid var(--color-primary, #3b82f6);
    outline-offset: 2px;
  }

  &.keyboard-focused {
    outline-width: 3px;
    outline-style: solid;
    box-shadow: 0 0 0 1px var(--color-background, #ffffff), 0 0 0 4px var(--color-primary, #3b82f6);
  }

  &--visible {
    opacity: 1;
  }

  &--dragging {
    cursor: grabbing;
    opacity: 0.8;
    transform: translateY(-50%) scale(0.9);
  }

  &--disabled {
    cursor: not-allowed;
    opacity: 0.3;
    
    &:hover,
    &:focus {
      opacity: 0.3;
      transform: translateY(-50%);
      outline: none;
    }
  }

  &__icon {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    background: var(--color-background, #ffffff);
    color: var(--color-text-secondary, #6b7280);
    border: 1px solid var(--color-border, #e5e7eb);
    transition: all 0.2s ease;

    &:hover {
      background: var(--color-background-hover, #f9fafb);
      color: var(--color-text, #111827);
      border-color: var(--color-border-hover, #d1d5db);
    }

    svg {
      transition: transform 0.2s ease;
    }
  }

  &:active &__icon svg {
    transform: scale(0.9);
  }
}

// Screen reader only class
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

// Global styles for dragging state
:global(.dragging) {
  opacity: 0.5;
  transform: rotate(2deg);
  transition: all 0.2s ease;
}

:global(.drag-ghost-image) {
  pointer-events: none !important;
  user-select: none !important;
}
</style>