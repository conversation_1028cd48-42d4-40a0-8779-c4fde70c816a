# Product Overview

Wen is a rich text editing and content management platform with real-time collaboration features. The system consists of:

- **Frontend**: Vue 3 web application with advanced rich text editing capabilities using TipTap editor
- **Backend**: Java-based microservices architecture with Spring Boot
- **Features**: 
  - Rich text editing with drag-and-drop functionality
  - Real-time collaboration and notifications
  - User authentication and authorization
  - Article management and publishing
  - Comment system with danmaku (bullet comments)
  - File upload and management
  - Interactive feedback system

The platform focuses on providing a modern, responsive writing experience with collaborative features similar to modern document editors.