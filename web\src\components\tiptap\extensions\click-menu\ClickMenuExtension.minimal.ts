import { Extension } from '@tiptap/core'
import type { ClickMenuOptions } from './types'

export const ClickMenuExtension = Extension.create<ClickMenuOptions>({
  name: 'clickMenu',

  addOptions() {
    return {
      enabled: true,
      menuItems: [
        {
          id: 'copy',
          label: '复制段落',
          icon: '📋',
          action: 'duplicateNode',
          shortcut: 'Ctrl+D'
        },
        {
          id: 'delete',
          label: '删除段落',
          icon: '🗑️',
          action: 'deleteNode',
          shortcut: 'Delete'
        },
        {
          id: 'moveUp',
          label: '上移',
          icon: '↑',
          action: 'moveUp',
          shortcut: 'Alt+↑'
        },
        {
          id: 'moveDown',
          label: '下移',
          icon: '↓',
          action: 'moveDown',
          shortcut: 'Alt+↓'
        }
      ],
      positioning: 'auto',
      showDelay: 100,
      hideDelay: 300,
      keyboardNavigation: true
    }
  },

  addStorage() {
    return {
      menuState: null,
      isMenuVisible: false,
      selectedIndex: 0,
      menuElement: null
    }
  },

  addCommands() {
    return {
      showClickMenu: (pos: number) => ({ state, dispatch, tr }) => {
        if (!this.options.enabled) return false
        
        try {
          const node = state.doc.nodeAt(pos)
          if (!node) return false

          // Store menu state
          this.storage.menuState = {
            visible: true,
            targetNode: node,
            targetPos: pos,
            selectedIndex: 0
          }
          this.storage.isMenuVisible = true

          return true
        } catch (error) {
          console.error('Error showing click menu:', error)
          return false
        }
      },

      hideClickMenu: () => ({ dispatch }) => {
        if (!this.storage.isMenuVisible) return false

        this.storage.menuState = null
        this.storage.isMenuVisible = false
        this.storage.selectedIndex = 0

        return true
      }
    }
  },

  addKeyboardShortcuts() {
    if (!this.options.enabled || !this.options.keyboardNavigation) {
      return {}
    }

    return {
      'Ctrl-Space': () => {
        const pos = this.editor.state.selection.from
        return this.editor.commands.showClickMenu(pos)
      },
      'Escape': () => {
        return this.storage.isMenuVisible ? this.editor.commands.hideClickMenu() : false
      }
    }
  },

  onCreate() {
    // Initialize menu element
    this.storage.menuElement = this.createMenuElement()
  },

  onDestroy() {
    // Clean up menu element
    if (this.storage.menuElement) {
      this.storage.menuElement.remove()
      this.storage.menuElement = null
    }
  },

  addProseMirrorPlugins() {
    return []
  },

  // Helper methods
  createMenuElement() {
    const menu = document.createElement('div')
    menu.className = 'click-menu'
    menu.style.cssText = `
      position: fixed;
      background: white;
      border: 1px solid #ccc;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
      padding: 8px;
      min-width: 200px;
      z-index: 1000;
      display: none;
    `
    document.body.appendChild(menu)
    return menu
  }
})