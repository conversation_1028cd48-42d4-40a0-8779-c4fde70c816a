<template>
  <div ref="commentInfoRef" class="comment-info-container">
    <!-- 评论头部组件 -->
    <CommentHeader
      :breadcrumb="commentState.breadcrumb.value"
      :model-value="commentState.sortType.value"
      @update:model-value="handleSortChange"
      @breadcrumb-click="handleBreadcrumbClick"
      @location-comment="commentState.locationComment"
    />

    <!-- 评论列表组件 -->
    <CommentList
      :comment-list="commentState.commentList.value"
      :flash-comment-id="commentState.flashCommentId.value"
      :show-reply-list-btn="commentState.showReplyListBtn.value"
      :comment-input-visible="commentInteraction.commentInputVisible.value"
      :comment-scroll-trigger="commentState.commentScrollTrigger.value"
      :comment-loading="commentState.commentLoading.value"
      :comment-no-more="commentState.commentNoMore.value"
      :has-comment-permission="commentState.hasCommentPermission.value"
      :quick-reply-loading="commentInteraction.quickReplyLoading.value"
      @load-more-comments="commentState.loadCurrentCommentList(false)"
      @show-reply-list="showReplyList"
      @handle-comment-reply-click="handleCommentReplyClick"
      @interaction-btn="commentInteraction.interactionBtn"
      @favorite-btn="commentInteraction.favoriteBtn"
      @quick-reply-comment="debouncedQuickReplyComment"
      @update-editor="commentInteraction.updateEditor"
      @update-comment-ref="commentState.updateCommentRef"
    />

    <!-- 评论输入组件 -->
    <CommentMainInput
      v-show="
        commentState.hasCommentPermission.value &&
        commentInteraction.commentInputVisible.value == '-1'
      "
      :comment-reply="commentInteraction.commentReply.value"
      @update:comment-reply="commentInteraction.commentReply.value = $event"
      :send-comment-loading="commentInteraction.sendCommentLoading.value"
      @send-comment="debouncedSendComment"
      ref="commentMainInputRef"
    />
  </div>
</template>

<script lang="ts" setup>
// 引入Vue核心功能
import { NRadioGroup, NRadioButton } from 'naive-ui'
import { nextTick, onBeforeUnmount, onMounted, ref, watch } from 'vue'

// 引入composables
import CommentHeader from '@/components/comment/CommentHeader.vue'
import CommentList from '@/components/comment/CommentList.vue'
import CommentMainInput from '@/components/comment/CommentMainInput.vue'
import { useCommentInteraction } from '@/composables/comment/useCommentInteraction'
import { useCommentState } from '@/composables/comment/useCommentState'
import type { Comment } from '@/types/comment.types'
import message from '@/utils/message'
import { activeTheme } from '@/utils/theme'

import type { PropType } from 'vue'

// Props 和 Emits 定义
const props = defineProps({
  articleId: {
    type: Function as PropType<() => string>,
    required: true,
  },
})

const emit = defineEmits<{
  (e: 'sendEnd'): void
  (e: 'quickReplyEnd'): void
}>()

// Refs
const inputBoxHeight = ref(0)
const commentInfoRef = ref()
const commentMainInputRef = ref()

// 状态管理
const commentState = useCommentState(props.articleId)
const commentInteraction = useCommentInteraction(props.articleId as () => string)

// 输入框高度管理
const updateInputBoxHeight = () => {
  nextTick(() => {
    if (commentMainInputRef.value?.commentInputWrapperRef) {
      inputBoxHeight.value = commentMainInputRef.value.commentInputWrapperRef.offsetHeight || 0
      const commentListContainer = document.querySelector(
        '.comment-list-container',
      ) as HTMLElement | null
      if (commentListContainer) {
        commentListContainer.style.paddingBottom =
          commentInteraction.commentInputVisible.value === '-1'
            ? `${inputBoxHeight.value + 12}px`
            : '1.25rem'
      }
    }
  })
}

// 在组件挂载时初始化权限
onMounted(() => {
  commentState.initCommentPermission()
  setAffixWidth()
  window.addEventListener('resize', setAffixWidth)

  // 加载评论
  const commentId = commentState.getCommentId()
  if (commentId) {
    commentState.locationComment(commentId)
  } else {
    commentState.loadCurrentCommentList()
  }
})

onBeforeUnmount(() => {
  window.removeEventListener('resize', setAffixWidth)
})

// 主题管理
const handleThemeChange = () => {
  nextTick(() => {
    const allEditors = document.querySelectorAll(
      '.ProseMirror, .editor-content, .tiptap-editor-wrapper',
    )
    allEditors.forEach((editor) => {
      if (editor instanceof HTMLElement) {
        const oldBackgroundColor = editor.style.backgroundColor
        editor.style.backgroundColor = 'transparent'
        void editor.offsetHeight
        editor.style.backgroundColor = oldBackgroundColor
      }
    })
  })
}

// Watch 监听器
watch(
  () => commentInteraction.commentInputVisible.value,
  () => nextTick(updateInputBoxHeight),
)

watch(
  () => commentState.hasCommentPermission.value,
  (newVal) => {
    if (newVal) {
      nextTick(updateInputBoxHeight)
    }
  },
)

watch(activeTheme, handleThemeChange)

watch(
  () => commentInteraction.commentReply.value,
  (newVal: any) => {
    // 只有在内容完全为空时才重置，避免在用户输入过程中误清空
    if (newVal && newVal.content && Array.isArray(newVal.content)) {
      // 检查是否所有段落都为空
      const hasContent = newVal.content.some((paragraph: any) => {
        if (paragraph.content && Array.isArray(paragraph.content)) {
          return paragraph.content.some((item: any) => {
            // 检查文本内容
            if (item.type === 'text' && item.text && item.text.trim()) {
              return true
            }
            // 检查其他非文本内容（如图片、mention等）
            if (item.type !== 'text') {
              return true
            }
            return false
          })
        }
        return false
      })

      // 只有在确实没有任何内容时才重置
      if (!hasContent) {
        commentInteraction.commentReply.value = undefined
      }
    }
  },
)

// 布局管理
const setAffixWidth = () => {
  nextTick(() => {
    if (commentInfoRef.value && commentMainInputRef.value?.commentInputWrapperRef) {
      const parentWidth = commentInfoRef.value.offsetWidth
      commentMainInputRef.value.commentInputWrapperRef.style.width = `${parentWidth}px`
      updateInputBoxHeight()
    }
  })
}

// 评论交互处理
const handleBreadcrumbClick = (index: number) => {
  commentState.resetCommentList()
  if (index !== commentState.lastBreadcrumbIndex.value) {
    commentState.breadcrumb.value.splice(index + 1)
  }
  const clearAllReplies = commentInteraction.clearAllQuickReplyContent()
  clearAllReplies({ commentList: commentState.commentList.value })
  commentState.loadCommentList(commentState.breadcrumb.value[index])
}

const showReplyList = (comment: Comment) => {
  commentState.resetCommentList()
  const clearAllReplies = commentInteraction.clearAllQuickReplyContent()
  clearAllReplies({ commentList: commentState.commentList.value })
  commentState.addBreadcrumb(comment)
  commentState.loadCommentList(comment)
}

const handleCommentReplyClick = (comment: Comment) => {
  commentInteraction.handleCommentReplyClick(comment, {
    isLastBreadcrumb: commentState.isLastBreadcrumb.value,
  })
}

// 使用项目自带的防抖方法为快捷回复添加防抖
const debouncedQuickReplyComment = (comment: Comment) => {
  commentInteraction.debouncedQuickReplyComment(comment, {
    isLastBreadcrumb: commentState.isLastBreadcrumb.value,
    onSuccess: (commentId) => {
      commentState.locationComment(commentId)
      commentInteraction.commentInputVisible.value = '-1'
    },
  })
  emit('quickReplyEnd')
}

// 使用项目自带的防抖方法为发送评论添加防抖
const debouncedSendComment = () => {
  commentInteraction.debouncedSendComment(commentMainInputRef.value, {
    lastBreadcrumbComment: commentState.lastBreadcrumbComment.value,
    onSuccess: (commentId) => {
      commentState.locationComment(commentId)
    },
  })
  emit('sendEnd')
}

// 处理排序变化
const handleSortChange = (value: string) => {
  commentState.sortType.value = value
  commentState.resetCommentList()
  commentState.loadCurrentCommentList()
}

// 暴露方法
defineExpose({
  loadCurrentCommentList: commentState.loadCurrentCommentList,
})
</script>

<style scoped lang="scss">
.comment-info-container {
  box-sizing: border-box;
  flex: 0 0 35vw;
  flex: 0 0 35dvw;
  width: 35vw;
  width: 35dvw;
  height: 100vh;
  height: 100dvh;
  background-color: var(--comment-info-bg, var(--creamy-white-1));
  display: flex;
  flex-direction: column;
  position: relative;

  @media (width <= 768px) {
    width: 100vw;
    width: 100dvw;
    flex: 0 0 100vw;
    flex: 0 0 100dvw;
    height: 100vh; /* 确保容器占满视口高度 */
    position: relative; /* 确保定位上下文正确 */
  }
}
</style>
