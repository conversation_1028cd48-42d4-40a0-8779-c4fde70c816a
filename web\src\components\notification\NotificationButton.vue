<template>
  <div class="notification-btn" style="cursor: pointer" ref="buttonRef">
    <NBadge
      :max="99"
      :value="unreadCount"
      :show-zero="false"
      :show="notificationReceiveType !== NotificationReceiveType.CLOSE"
    >
      <IosNotificationsOutline
        v-if="notificationReceiveType !== NotificationReceiveType.CLOSE"
        :size="24"
      />
      <IosNotificationsOff v-else :size="24" />
    </NBadge>
  </div>
</template>

<script setup lang="ts">
import { IosNotificationsOutline, IosNotificationsOff } from '@/icons'
import { NBadge } from 'naive-ui'
import { ref } from 'vue'

import { NotificationReceiveType } from '@/constants/notification_receive_type.constants'

defineProps({
  unreadCount: {
    type: Number,
    default: 0,
  },
  notificationReceiveType: {
    type: Number,
    required: true,
  },
})

const emit = defineEmits(['click', 'long-press'])

const buttonRef = ref<HTMLElement | null>(null)
</script>

<style scoped>
.notification-btn {
  position: relative;
  display: inline-block;
}
</style>
