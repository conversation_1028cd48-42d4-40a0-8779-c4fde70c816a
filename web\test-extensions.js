// 测试扩展加载的脚本
// 在浏览器控制台中运行此脚本来检查扩展是否正常加载

console.log('🔍 开始检查 TipTap 扩展...');

// 检查是否有语法错误
try {
  // 尝试动态导入扩展文件
  const testImports = async () => {
    try {
      console.log('📦 测试 ClickMenuExtension 导入...');
      const clickMenuModule = await import('/src/components/tiptap/extensions/click-menu/ClickMenuExtension.ts');
      console.log('✅ ClickMenuExtension 导入成功:', clickMenuModule);
      
      console.log('📦 测试 ClickMenuView 导入...');
      const clickMenuViewModule = await import('/src/components/tiptap/extensions/click-menu/ClickMenuView.ts');
      console.log('✅ ClickMenuView 导入成功:', clickMenuViewModule);
      
      console.log('📦 测试 DragHandleExtension 导入...');
      const dragHandleModule = await import('/src/components/tiptap/extensions/drag-handle/DragHandleExtension.ts');
      console.log('✅ DragHandleExtension 导入成功:', dragHandleModule);
      
      console.log('📦 测试 DragHandleView 导入...');
      const dragHandleViewModule = await import('/src/components/tiptap/extensions/drag-handle/DragHandleView.ts');
      console.log('✅ DragHandleView 导入成功:', dragHandleViewModule);
      
      console.log('🎉 所有扩展导入测试通过！');
      
      // 检查扩展是否正确导出
      if (clickMenuModule.ClickMenuExtension) {
        console.log('✅ ClickMenuExtension 正确导出');
      } else {
        console.error('❌ ClickMenuExtension 导出失败');
      }
      
      if (clickMenuViewModule.ClickMenuView) {
        console.log('✅ ClickMenuView 正确导出');
      } else {
        console.error('❌ ClickMenuView 导出失败');
      }
      
      if (dragHandleModule.DragHandleExtension) {
        console.log('✅ DragHandleExtension 正确导出');
      } else {
        console.error('❌ DragHandleExtension 导出失败');
      }
      
      if (dragHandleViewModule.DragHandleView) {
        console.log('✅ DragHandleView 正确导出');
      } else {
        console.error('❌ DragHandleView 导出失败');
      }
      
    } catch (error) {
      console.error('❌ 扩展导入失败:', error);
      console.error('错误详情:', error.message);
      console.error('错误堆栈:', error.stack);
    }
  };
  
  testImports();
  
} catch (error) {
  console.error('❌ 测试脚本执行失败:', error);
}

// 检查 DOM 中的编辑器
setTimeout(() => {
  console.log('🔍 检查 DOM 中的编辑器...');
  
  const proseMirrorEditor = document.querySelector('.ProseMirror');
  if (proseMirrorEditor) {
    console.log('✅ 找到 ProseMirror 编辑器:', proseMirrorEditor);
  } else {
    console.warn('⚠️ 未找到 ProseMirror 编辑器');
  }
  
  const clickMenus = document.querySelectorAll('.click-menu');
  console.log(`📊 找到 ${clickMenus.length} 个点击菜单元素`);
  
  const dragHandles = document.querySelectorAll('.drag-handle');
  console.log(`📊 找到 ${dragHandles.length} 个拖拽手柄元素`);
  
  // 检查是否有错误信息
  const errorElements = document.querySelectorAll('[data-error]');
  if (errorElements.length > 0) {
    console.warn('⚠️ 发现错误元素:', errorElements);
  } else {
    console.log('✅ 未发现错误元素');
  }
  
}, 2000);

console.log('✅ 扩展检查脚本已启动，请等待结果...');
