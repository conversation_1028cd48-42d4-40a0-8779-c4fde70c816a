/**
 * Strategy pattern for handling different node types
 */
export interface NodeTypeStrategy {
  getSelector(): string
  getIcon(): string
  positionDragHandle(element: HTMLElement, handle: HTMLElement): void
  positionDropIndicator(element: HTMLElement, indicator: HTMLElement): void
  getIndicatorHeight(): number
  canDragInto(targetType: string): boolean
  canReceiveFrom(sourceType: string): boolean
}

export class ParagraphStrategy implements NodeTypeStrategy {
  getSelector(): string {
    return 'p'
  }

  getIcon(): string {
    return `
      <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
        <circle cx="3" cy="3" r="1"/>
        <circle cx="9" cy="3" r="1"/>
        <circle cx="3" cy="6" r="1"/>
        <circle cx="9" cy="6" r="1"/>
        <circle cx="3" cy="9" r="1"/>
        <circle cx="9" cy="9" r="1"/>
      </svg>
    `
  }

  positionDragHandle(element: HTMLElement, handle: HTMLElement): void {
    element.style.position = 'relative'
    handle.style.cssText = `
      position: absolute;
      left: -20px;
      top: 2px;
      z-index: 10;
    `
    element.appendChild(handle)
  }

  positionDropIndicator(element: HTMLElement, indicator: HTMLElement): void {
    element.style.position = 'relative'
    indicator.style.top = '-2px'
    element.insertBefore(indicator, element.firstChild)
  }

  getIndicatorHeight(): number {
    return 4
  }

  canDragInto(targetType: string): boolean {
    return ['paragraph', 'heading', 'blockquote'].includes(targetType)
  }

  canReceiveFrom(sourceType: string): boolean {
    return ['paragraph', 'heading'].includes(sourceType)
  }
}

export class HeadingStrategy implements NodeTypeStrategy {
  getSelector(): string {
    return 'h1, h2, h3, h4, h5, h6'
  }

  getIcon(): string {
    return `
      <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
        <rect x="2" y="2" width="8" height="2" rx="1"/>
        <rect x="2" y="5" width="6" height="2" rx="1"/>
        <rect x="2" y="8" width="4" height="2" rx="1"/>
      </svg>
    `
  }

  positionDragHandle(element: HTMLElement, handle: HTMLElement): void {
    element.style.position = 'relative'
    handle.style.cssText = `
      position: absolute;
      left: -20px;
      top: 0;
      z-index: 10;
    `
    element.appendChild(handle)
  }

  positionDropIndicator(element: HTMLElement, indicator: HTMLElement): void {
    element.style.position = 'relative'
    indicator.style.top = '-3px'
    element.insertBefore(indicator, element.firstChild)
  }

  getIndicatorHeight(): number {
    return 6
  }

  canDragInto(targetType: string): boolean {
    return ['paragraph', 'heading', 'blockquote'].includes(targetType)
  }

  canReceiveFrom(sourceType: string): boolean {
    return ['paragraph', 'heading'].includes(sourceType)
  }
}

export class NodeTypeStrategyFactory {
  private static strategies: Map<string, NodeTypeStrategy> = new Map([
    ['paragraph', new ParagraphStrategy()],
    ['heading', new HeadingStrategy()],
    // Add other strategies as needed
  ])

  static getStrategy(nodeType: string): NodeTypeStrategy | null {
    return this.strategies.get(nodeType) || null
  }

  static registerStrategy(nodeType: string, strategy: NodeTypeStrategy): void {
    this.strategies.set(nodeType, strategy)
  }
}