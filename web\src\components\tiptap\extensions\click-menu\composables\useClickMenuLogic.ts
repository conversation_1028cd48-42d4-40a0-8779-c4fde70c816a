import { ref, computed, nextTick, onUnmounted, type Ref } from 'vue'
import type { Editor } from '@tiptap/core'
import type { Node as ProseMirrorNode } from '@tiptap/pm/model'
import type { MenuState, ClickMenuItem, ClickMenuOptions } from '../types'
import { menuItemRegistry } from '../MenuItemRegistry'

export function useClickMenuLogic(
  editor: Ref<Editor | undefined>, 
  options: Ref<ClickMenuOptions>
) {
  const menuState = ref<MenuState>({
    visible: false,
    targetNode: null,
    targetPos: null,
    position: { x: 0, y: 0 },
    selectedIndex: 0
  })

  // Track timeouts for show/hide delays
  let showTimeout: ReturnType<typeof setTimeout> | null = null
  let hideTimeout: ReturnType<typeof setTimeout> | null = null

  // Track keyboard navigation state
  const keyboardFocus = ref({
    enabled: false,
    selectedIndex: 0,
    navigationMode: 'mouse' as 'mouse' | 'keyboard'
  })

  // Get filtered menu items for current context
  const availableMenuItems = computed(() => {
    if (!menuState.value.targetNode) return []
    return menuItemRegistry.getFilteredItems(menuState.value.targetNode)
  })

  // Get enabled menu items only
  const enabledMenuItems = computed(() => {
    if (!menuState.value.targetNode) return []
    return availableMenuItems.value.filter(item => 
      item.isEnabled(menuState.value.targetNode!)
    )
  })

  /**
   * Show the click menu at specified position
   */
  const showMenu = (pos: number, x: number, y: number) => {
    try {
      if (!editor.value) {
        console.warn('Editor not available for menu display')
        return
      }

      // Clear any existing hide timeout
      if (hideTimeout) {
        clearTimeout(hideTimeout)
        hideTimeout = null
      }

      // Get the node at the specified position
      const resolvedPos = editor.value.state.doc.resolve(pos)
      const targetNode = resolvedPos.node()

      if (!targetNode) {
        console.warn('No node found at position:', pos)
        return
      }

      // Check if we have any visible menu items for this node
      const visibleItems = menuItemRegistry.getFilteredItems(targetNode)
      if (visibleItems.length === 0) {
        console.debug('No menu items available for node type:', targetNode.type.name)
        return
      }

      const showMenuInternal = () => {
        menuState.value = {
          visible: true,
          targetNode,
          targetPos: pos,
          position: { x, y },
          selectedIndex: 0
        }

        // Reset keyboard focus state
        keyboardFocus.value = {
          enabled: options.value.keyboardNavigation,
          selectedIndex: 0,
          navigationMode: 'mouse'
        }

        console.debug('Click menu shown at position:', { pos, x, y, nodeType: targetNode.type.name })
      }

      // Apply show delay if configured
      if (options.value.showDelay > 0) {
        showTimeout = setTimeout(showMenuInternal, options.value.showDelay)
      } else {
        showMenuInternal()
      }

    } catch (error) {
      console.error('Failed to show click menu:', error)
    }
  }

  /**
   * Hide the click menu with optional delay
   */
  const hideMenu = (immediate = false) => {
    try {
      // Clear any existing show timeout
      if (showTimeout) {
        clearTimeout(showTimeout)
        showTimeout = null
      }

      const hideMenuInternal = () => {
        menuState.value = {
          visible: false,
          targetNode: null,
          targetPos: null,
          position: { x: 0, y: 0 },
          selectedIndex: 0
        }

        // Reset keyboard focus
        keyboardFocus.value = {
          enabled: false,
          selectedIndex: 0,
          navigationMode: 'mouse'
        }

        console.debug('Click menu hidden')
      }

      if (immediate || options.value.hideDelay <= 0) {
        hideMenuInternal()
      } else {
        hideTimeout = setTimeout(hideMenuInternal, options.value.hideDelay)
      }

    } catch (error) {
      console.error('Failed to hide click menu:', error)
    }
  }

  /**
   * Execute a menu item action with error handling
   */
  const executeAction = async (item: ClickMenuItem) => {
    try {
      if (!editor.value || !menuState.value.targetNode || menuState.value.targetPos === null) {
        throw new Error('Invalid menu state for action execution')
      }

      // Check if the item is enabled
      if (!item.isEnabled(menuState.value.targetNode)) {
        console.warn('Menu item is disabled:', item.id)
        return
      }

      console.debug('Executing menu action:', item.id)

      // Execute the action
      await item.action(
        editor.value,
        menuState.value.targetNode,
        menuState.value.targetPos
      )

      // Hide menu after successful action
      hideMenu(true)

      console.debug('Menu action executed successfully:', item.id)

    } catch (error) {
      console.error('Failed to execute menu action:', error)
      
      // Still hide the menu even if action failed
      hideMenu(true)
      
      // Re-throw for potential UI error handling
      throw error
    }
  }

  /**
   * Navigate menu with keyboard
   */
  const navigateMenu = (direction: 'up' | 'down' | 'select' | 'escape') => {
    try {
      if (!menuState.value.visible || !options.value.keyboardNavigation) {
        return
      }

      const items = enabledMenuItems.value
      if (items.length === 0) return

      switch (direction) {
        case 'up':
          keyboardFocus.value.navigationMode = 'keyboard'
          keyboardFocus.value.selectedIndex = Math.max(0, keyboardFocus.value.selectedIndex - 1)
          menuState.value.selectedIndex = keyboardFocus.value.selectedIndex
          break

        case 'down':
          keyboardFocus.value.navigationMode = 'keyboard'
          keyboardFocus.value.selectedIndex = Math.min(items.length - 1, keyboardFocus.value.selectedIndex + 1)
          menuState.value.selectedIndex = keyboardFocus.value.selectedIndex
          break

        case 'select':
          if (keyboardFocus.value.navigationMode === 'keyboard') {
            const selectedItem = items[keyboardFocus.value.selectedIndex]
            if (selectedItem) {
              executeAction(selectedItem)
            }
          }
          break

        case 'escape':
          hideMenu(true)
          break
      }

      console.debug('Menu navigation:', { direction, selectedIndex: keyboardFocus.value.selectedIndex })

    } catch (error) {
      console.error('Failed to navigate menu:', error)
    }
  }

  /**
   * Handle mouse hover for menu items
   */
  const handleMouseEnter = (index: number) => {
    if (keyboardFocus.value.navigationMode === 'mouse') {
      keyboardFocus.value.selectedIndex = index
      menuState.value.selectedIndex = index
    }
  }

  /**
   * Switch to mouse navigation mode
   */
  const enableMouseNavigation = () => {
    keyboardFocus.value.navigationMode = 'mouse'
  }

  /**
   * Check if menu should be hidden when clicking outside
   */
  const shouldHideOnClickOutside = (event: MouseEvent) => {
    if (!menuState.value.visible) return false

    // Don't hide if clicking on the menu itself
    const target = event.target as Element
    if (target?.closest('.click-menu')) {
      return false
    }

    return true
  }

  /**
   * Get menu item by index
   */
  const getMenuItemByIndex = (index: number): ClickMenuItem | undefined => {
    return enabledMenuItems.value[index]
  }

  /**
   * Check if a menu item is currently selected
   */
  const isItemSelected = (index: number): boolean => {
    return menuState.value.selectedIndex === index
  }

  // Cleanup timeouts on unmount
  onUnmounted(() => {
    if (showTimeout) {
      clearTimeout(showTimeout)
    }
    if (hideTimeout) {
      clearTimeout(hideTimeout)
    }
  })

  return {
    // State
    menuState,
    keyboardFocus,
    availableMenuItems,
    enabledMenuItems,

    // Actions
    showMenu,
    hideMenu,
    executeAction,
    navigateMenu,
    handleMouseEnter,
    enableMouseNavigation,

    // Utilities
    shouldHideOnClickOutside,
    getMenuItemByIndex,
    isItemSelected
  }
}