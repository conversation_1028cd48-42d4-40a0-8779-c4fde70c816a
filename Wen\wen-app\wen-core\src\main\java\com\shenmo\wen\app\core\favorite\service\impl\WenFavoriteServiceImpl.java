package com.shenmo.wen.app.core.favorite.service.impl;

import cn.dev33.satoken.stp.StpUtil;

import com.shenmo.wen.app.core.article.exception.ArticleExceptionEnum;
import com.shenmo.wen.app.core.article.mapper.WenArticleMapper;
import com.shenmo.wen.app.core.comment.exception.CommentExceptionEnum;
import com.shenmo.wen.app.core.comment.mapper.WenCommentMapper;
import com.shenmo.wen.app.core.favorite.mapper.WenFavoriteMapper;
import com.shenmo.wen.app.core.favorite.pojo.entity.WenFavorite;
import com.shenmo.wen.app.core.favorite.pojo.param.WenFavoriteParam;
import com.shenmo.wen.app.core.favorite.pojo.vo.WenFavoriteCount;
import com.shenmo.wen.app.core.favorite.service.WenFavoriteService;
import com.shenmo.wen.common.enumeration.InteractionTargetEnum;
import com.shenmo.wen.common.util.AssertUtils;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
public class WenFavoriteServiceImpl implements WenFavoriteService {
    private final WenFavoriteMapper mapper;
    private final WenArticleMapper articleMapper;
    private final WenCommentMapper commentMapper;

    @Override
    public WenFavoriteCount save(WenFavoriteParam param) {
        // 验证目标id
        final Long targetId = param.getTargetId();
        final InteractionTargetEnum interactionTargetEnum = InteractionTargetEnum.of(param.getTargetType());
        switch (interactionTargetEnum) {
            case ARTICLE ->
                    AssertUtils.isTrue(articleMapper.existsById(targetId), ArticleExceptionEnum.ARTICLE_NOT_EXISTS);
            case COMMENT ->
                    AssertUtils.isTrue(commentMapper.existsById(targetId), CommentExceptionEnum.COMMENT_NOT_EXISTS);
        }
        final long loginId = StpUtil.getLoginIdAsLong();
        final int targetType = interactionTargetEnum.getCode();
        final WenFavorite alreadyFavorite = mapper.targetById(loginId, targetType, targetId);
        final boolean isAlreadyFavorite = Objects.nonNull(alreadyFavorite);
        if (isAlreadyFavorite) {
            mapper.deleteById(alreadyFavorite.getId());
            // 更新互动数
            decrementInteractionCount(interactionTargetEnum, targetId);
        } else {
            final WenFavorite favorite = new WenFavorite();
            favorite.setUserId(loginId);
            favorite.setTargetType(targetType);
            favorite.setTargetId(targetId);
            mapper.insert(favorite);
            // 新增互动数
            incrementInteractionCount(interactionTargetEnum, targetId);
        }
        int count = 0;
        switch (interactionTargetEnum) {
            case ARTICLE -> count = articleMapper.favoriteCountById(targetId);
            case COMMENT -> count = commentMapper.favoriteCountById(targetId);
        }
        final WenFavoriteCount favoriteCount = new WenFavoriteCount();
        favoriteCount.setCount(count);
        favoriteCount.setCancel(isAlreadyFavorite);
        return favoriteCount;
    }

    private void incrementInteractionCount(InteractionTargetEnum interactionTargetEnum, Long targetId) {
        switch (interactionTargetEnum) {
            case ARTICLE -> articleMapper.incrementFavoriteCount(targetId);
            case COMMENT -> commentMapper.incrementFavoriteCount(targetId);
        }
    }

    private void decrementInteractionCount(InteractionTargetEnum interactionTargetEnum, Long targetId) {
        switch (interactionTargetEnum) {
            case ARTICLE -> articleMapper.decrementFavoriteCount(targetId);
            case COMMENT -> commentMapper.decrementFavoriteCount(targetId);
        }
    }
}
