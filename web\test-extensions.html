<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Extensions</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-result {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        pre {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <h1>TipTap Extensions Test</h1>
    
    <div class="test-section">
        <h2>Extension Loading Test</h2>
        <div id="extension-test-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Style Loading Test</h2>
        <div id="style-test-results"></div>
    </div>
    
    <div class="test-section">
        <h2>DOM Structure Test</h2>
        <div id="dom-test-results"></div>
    </div>
    
    <div class="test-section">
        <h2>Console Errors</h2>
        <div id="console-errors"></div>
    </div>

    <script>
        // Capture console errors
        const consoleErrors = [];
        const originalError = console.error;
        console.error = function(...args) {
            consoleErrors.push(args.join(' '));
            originalError.apply(console, arguments);
        };

        function addResult(containerId, message, type = 'info') {
            const container = document.getElementById(containerId);
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            container.appendChild(div);
        }

        function testExtensionLoading() {
            // Test if extension files exist and can be loaded
            const extensionTests = [
                {
                    name: 'Drag Handle Extension',
                    path: '/src/components/tiptap/extensions/drag-handle/index.ts',
                    styles: '/src/components/tiptap/extensions/drag-handle/styles/drag-handle.scss'
                },
                {
                    name: 'Click Menu Extension',
                    path: '/src/components/tiptap/extensions/click-menu/index.ts',
                    styles: '/src/components/tiptap/extensions/click-menu/styles/click-menu.scss'
                }
            ];

            extensionTests.forEach(test => {
                // Test if files exist (this is a simplified test)
                addResult('extension-test-results', 
                    `✓ ${test.name} - Files should be available at ${test.path}`, 
                    'info');
            });
        }

        function testStyleLoading() {
            // Check if styles are loaded
            const styleSheets = Array.from(document.styleSheets);
            const hasClickMenuStyles = checkForStyles([
                '.click-menu',
                '.drag-handle'
            ]);

            if (hasClickMenuStyles.length > 0) {
                addResult('style-test-results', 
                    `✓ Found styles: ${hasClickMenuStyles.join(', ')}`, 
                    'success');
            } else {
                addResult('style-test-results', 
                    '⚠ No extension styles found - this might be why the extensions are not working', 
                    'warning');
            }
        }

        function checkForStyles(selectors) {
            const found = [];
            selectors.forEach(selector => {
                try {
                    const elements = document.querySelectorAll(selector);
                    if (elements.length > 0) {
                        found.push(selector);
                    }
                } catch (e) {
                    // Selector might not be valid in this context
                }
            });
            return found;
        }

        function testDOMStructure() {
            // Check for ProseMirror editor
            const proseMirrorElements = document.querySelectorAll('.ProseMirror');
            if (proseMirrorElements.length > 0) {
                addResult('dom-test-results', 
                    `✓ Found ${proseMirrorElements.length} ProseMirror editor(s)`, 
                    'success');
                
                // Check for drag handles
                const dragHandles = document.querySelectorAll('.drag-handle');
                if (dragHandles.length > 0) {
                    addResult('dom-test-results', 
                        `✓ Found ${dragHandles.length} drag handle(s)`, 
                        'success');
                } else {
                    addResult('dom-test-results', 
                        '⚠ No drag handles found in DOM', 
                        'warning');
                }
                
                // Check for click menu
                const clickMenus = document.querySelectorAll('.click-menu');
                if (clickMenus.length > 0) {
                    addResult('dom-test-results', 
                        `✓ Found ${clickMenus.length} click menu(s)`, 
                        'success');
                } else {
                    addResult('dom-test-results', 
                        '⚠ No click menus found in DOM', 
                        'warning');
                }
            } else {
                addResult('dom-test-results', 
                    '⚠ No ProseMirror editors found', 
                    'warning');
            }
        }

        function showConsoleErrors() {
            if (consoleErrors.length > 0) {
                consoleErrors.forEach(error => {
                    addResult('console-errors', `❌ ${error}`, 'error');
                });
            } else {
                addResult('console-errors', '✓ No console errors detected', 'success');
            }
        }

        // Run tests
        setTimeout(() => {
            testExtensionLoading();
            testStyleLoading();
            testDOMStructure();
            showConsoleErrors();
        }, 1000);

        // Add instructions
        addResult('extension-test-results', 
            '<strong>Instructions:</strong><br>' +
            '1. Open this file in your browser while the Vue app is running<br>' +
            '2. Navigate to a page with the article editor<br>' +
            '3. Check the test results below<br>' +
            '4. Look for any errors or warnings', 
            'info');
    </script>
</body>
</html>
