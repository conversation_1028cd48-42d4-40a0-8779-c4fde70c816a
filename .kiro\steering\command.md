---
inclusion: always
---

# Command Execution Guidelines

## Shell Environment
- **Platform**: Windows (win32)
- **Primary Shell**: PowerShell
- **Always use PowerShell syntax** when executing terminal commands
- Use semicolon (`;`) as command separator instead of `&&`

## Common PowerShell Commands
```powershell
# Directory operations
Get-ChildItem                    # List files (instead of ls/dir)
Set-Location path               # Change directory (instead of cd)
New-Item -ItemType Directory    # Create directory (instead of mkdir)

# File operations  
Get-Content file.txt            # View file content (instead of cat/type)
Copy-Item source dest           # Copy files (instead of cp/copy)
Remove-Item file.txt            # Delete files (instead of rm/del)

# Process management
Get-Process                     # List processes (instead of ps)
Stop-Process -Name name         # Kill process (instead of kill)

# Package management
npm run dev                     # Frontend development server
npm run build                   # Production build
npm run test:unit               # Run unit tests
```

## Service Management
Use the provided batch files for backend services:
- `start-wen-core.bat`
- `start-wen-gateway.bat` 
- `start-wen-authentication.bat`

## Development Workflow Commands
```powershell
# Frontend (web/ directory)
cd web; npm run dev            # Start development server
cd web; npm run code-check     # Run all linting and type checks
cd web; npm run format         # Format code with Prettier

# Testing
cd web; npm run test:unit      # Unit tests with Vitest
cd web; npm run test:e2e       # E2E tests with Playwright
```