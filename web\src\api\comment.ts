import type { RequestParams } from '@/types/request.types'
import { type ResponseData } from '@/types/response_data.types'
import api from '@/utils/api'

import type { AxiosError, AxiosRequestConfig } from 'axios'

const commentApi = {
  URL: '/core/comments',
  // 搜索
  search: async <T>(params: RequestParams, signal?: AbortSignal): Promise<ResponseData<T>> => {
    const config: AxiosRequestConfig = { signal }
    const res = await api
      .get<T>(commentApi.URL + '/search', params, config)
      .catch((err: AxiosError) => {
        return api.handleError(err)
      })
    return res.data as ResponseData<T>
  },

  // 定位
  location: async <T>(id: string): Promise<ResponseData<T>> => {
    const res = await api.get<T>(commentApi.URL + '/location/' + id).catch((err: AxiosError) => {
      return api.handleError(err) as ResponseData<T>
    })
    return res.data as ResponseData<T>
  },

  // 根据id加载
  loadById: async <T>(id: string): Promise<ResponseData<T>> => {
    const res = await api.get<T>(commentApi.URL + '/' + id).catch((err: AxiosError) => {
      return api.handleError(err) as ResponseData<T>
    })
    return res.data as ResponseData<T>
  },

  // 加载
  load: async <T>(params: string): Promise<ResponseData<T>> => {
    const res = await api.get<T>(commentApi.URL + params).catch((err: AxiosError) => {
      return api.handleError(err) as ResponseData<T>
    })
    return res.data as ResponseData<T>
  },

  // 保存
  save: async <T>(params: RequestParams): Promise<ResponseData<T>> => {
    const res = await api.post<T>(commentApi.URL, params).catch((err: AxiosError) => {
      return api.handleError(err)
    })
    return res.data as ResponseData<T>
  },
}

export default commentApi
