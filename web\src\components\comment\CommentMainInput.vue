<template>
  <NAffix :bottom="0" style="z-index: 1500" class="comment-input-affix">
    <div class="comment-input-row" ref="commentInputWrapperRef">
      <TiptapEditor
        ref="sendTiptapEditorRef"
        :model-value="commentReply"
        @update:model-value="
          (val: unknown) => emit('update:commentReply', val as JSONContent | undefined)
        "
        class="comment-tiptap-editor"
        :editor-props="{
          attributes: {
            class: 'ProseMirrorInput',
            'data-main-editor': 'true',
          },
        }"
        :file-bucket="COMMENT"
        :placeholder="'说是你的自由，但是...'"
        :show-character-count="true"
        :extensions="editorExtensions"
        :toolbar="true"
        @keydown.alt.enter.prevent="$emit('sendComment')"
      />
      <NButton
        text
        type="info"
        :loading="sendCommentLoading"
        @click="$emit('sendComment')"
        class="comment-reply-send-btn"
        size="small"
        :disabled="disabled"
      >
        <SendAltFilled :size="28" />
      </NButton>
    </div>
  </NAffix>
</template>

<script lang="ts" setup>
import { SendAltFilled } from '@/icons'
import { NAffix, NButton } from 'naive-ui'
import { ref } from 'vue'

import TiptapEditor from '@/components/tiptap/core/TipTapEditor.vue'
import { COMMENT } from '@/constants/bucket.constants'
import { COMMENT_EXTENSIONS } from '@/constants/tiptap.constants'

import type { JSONContent } from '@tiptap/vue-3'

const props = defineProps<{
  commentReply: JSONContent | undefined
  sendCommentLoading: boolean
  disabled?: boolean
}>()

const emit = defineEmits<{
  (e: 'sendComment'): void
  (e: 'update:commentReply', value: JSONContent | undefined): void
}>()

const commentInputWrapperRef = ref()
const sendTiptapEditorRef = ref()

const editorExtensions = [...COMMENT_EXTENSIONS, 'characterCount']

defineExpose({
  commentInputWrapperRef,
  sendTiptapEditorRef,
})
</script>

<style scoped lang="scss">
.comment-input-affix {
  width: 100%;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 5%);
  position: absolute;
  bottom: 0;
  left: 0;
  background-color: var(--comment-input-bg, var(--creamy-white-1));
  z-index: 1500;
  transition: transform 0.3s ease;

  @media (width <= 768px) {
    position: fixed;
    width: 100vw;
  }
}

.comment-input-row {
  display: flex;
  align-items: end;
  justify-content: center;
  background-color: var(--comment-input-bg, var(--creamy-white-1));
  border-top: var(--border-1);
  padding: 0.5rem 1.25rem;
  width: 100%;
  box-sizing: border-box;

  @media (width <= 768px) {
    padding: 0.5rem;
  }

  .comment-tiptap-editor {
    max-width: 75%;
    margin-right: 1.25rem;

    @media (width <= 768px) {
      max-width: 70%;
    }

    :deep(.tiptap-editor-wrapper),
    :deep(.editor-content),
    :deep(.ProseMirrorInput) {
      background-color: var(--comment-input-prosemirror-bg, var(--creamy-white-1));
    }
  }
}

.comment-reply-send-btn {
  margin-bottom: 1.7rem;

  @media (width <= 768px) {
    margin-bottom: 1.7rem;
  }
}
</style>
