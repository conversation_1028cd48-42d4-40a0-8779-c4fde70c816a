package com.shenmo.wen.common.exception.enumeration;
import com.shenmo.wen.common.exception.BaseException;
import org.springframework.http.HttpStatus;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface ExceptionEnum {


    /**
     * 获取HttpStatus
     *
     * @return {@link HttpStatus}
     * <AUTHOR>
     */
    default HttpStatus getStatus() {

        return getOption().getHttpStatus();
    }

    /**
     * 获取异常的状态码
     *
     * @return 状态码
     * <AUTHOR>
     */
    default Integer getCode() {

        int statusValue = getOption().getStatusValue();
        final Class<? extends ExceptionEnum> cls = this.getClass();
        int ordinal = -1;
        if (Enum.class.isAssignableFrom(cls)) {
            ordinal = ((Enum<?>) this).ordinal();
        }
        return ExpEnumCodeFactory.getExceptionModule(cls, statusValue, ordinal);
    }

    /**
     * 获取异常的提示信息
     *
     * @return 提示信息
     * <AUTHOR>
     */
    default String getMessage() {

        return getOption().getMessage();
    }

    /**
     * 获取异常枚举选项
     *
     * @return 异常枚举选项
     * <AUTHOR>
     */
    ExceptionEnumOption getOption();
}
