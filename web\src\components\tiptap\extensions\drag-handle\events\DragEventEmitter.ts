/**
 * Observer pattern implementation for drag events
 */
export type DragEventType = 'dragStart' | 'dragEnd' | 'dragMove' | 'dropSuccess' | 'dropFail'

export interface DragEventData {
  type: DragEventType
  sourceElement: HTMLElement
  targetElement?: HTMLElement
  sourcePos: number
  targetPos?: number
  nodeType: string
  timestamp: number
}

export interface DragEventListener {
  onDragEvent(event: DragEventData): void
}

export class DragEventEmitter {
  private listeners: Map<DragEventType, Set<DragEventListener>> = new Map()

  subscribe(eventType: DragEventType, listener: DragEventListener): void {
    if (!this.listeners.has(eventType)) {
      this.listeners.set(eventType, new Set())
    }
    this.listeners.get(eventType)!.add(listener)
  }

  unsubscribe(eventType: DragEventType, listener: DragEventListener): void {
    const listeners = this.listeners.get(eventType)
    if (listeners) {
      listeners.delete(listener)
    }
  }

  emit(eventData: DragEventData): void {
    const listeners = this.listeners.get(eventData.type)
    if (listeners) {
      listeners.forEach(listener => {
        try {
          listener.onDragEvent(eventData)
        } catch (error) {
          console.error(`Error in drag event listener:`, error)
        }
      })
    }
  }

  clear(): void {
    this.listeners.clear()
  }
}