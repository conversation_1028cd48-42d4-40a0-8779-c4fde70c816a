import { Editor } from '@tiptap/core'
import { ClickMenuExtension } from '../click-menu/ClickMenuExtension'
import { BlockMenuExtension, BlockMenuItem } from '../block-menu'

// 定义块菜单项
export const blockMenuItems: Record<string, BlockMenuItem> = {
  heading1: {
    id: 'heading1',
    name: 'Heading 1',
    icon: '<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M3.75 2a.75.75 0 0 1 .75.75V7h7V2.75a.75.75 0 0 1 1.5 0v10.5a.75.75 0 0 1-1.5 0V8.5h-7v4.75a.75.75 0 0 1-1.5 0V2.75A.75.75 0 0 1 3.75 2Z"/></svg>',
    keywords: 'heading title h1 large',
    shortcut: 'Mod+Alt+1',
    action: (editor: Editor) => editor.chain().focus().toggleHeading({ level: 1 }).run(),
  },
  heading2: {
    id: 'heading2',
    name: 'Heading 2',
    icon: '<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M3.75 2a.75.75 0 0 1 .75.75V7h7V2.75a.75.75 0 0 1 1.5 0v10.5a.75.75 0 0 1-1.5 0V8.5h-7v4.75a.75.75 0 0 1-1.5 0V2.75A.75.75 0 0 1 3.75 2Z"/></svg>',
    keywords: 'heading title h2 medium',
    shortcut: 'Mod+Alt+2',
    action: (editor: Editor) => editor.chain().focus().toggleHeading({ level: 2 }).run(),
  },
  heading3: {
    id: 'heading3',
    name: 'Heading 3',
    icon: '<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M3.75 2a.75.75 0 0 1 .75.75V7h7V2.75a.75.75 0 0 1 1.5 0v10.5a.75.75 0 0 1-1.5 0V8.5h-7v4.75a.75.75 0 0 1-1.5 0V2.75A.75.75 0 0 1 3.75 2Z"/></svg>',
    keywords: 'heading title h3 small',
    shortcut: 'Mod+Alt+3',
    action: (editor: Editor) => editor.chain().focus().toggleHeading({ level: 3 }).run(),
  },
  paragraph: {
    id: 'paragraph',
    name: 'Paragraph',
    icon: '<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M2 4.75A.75.75 0 0 1 2.75 4h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 4.75ZM2 8a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 8Zm0 3.25a.75.75 0 0 1 .75-.75h6.5a.75.75 0 0 1 0 1.5h-6.5a.75.75 0 0 1-.75-.75Z"/></svg>',
    keywords: 'paragraph text normal',
    shortcut: 'Mod+Alt+0',
    action: (editor: Editor) => editor.chain().focus().setParagraph().run(),
  },
  bulletList: {
    id: 'bulletList',
    name: 'Bullet List',
    icon: '<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M2 4a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm3.75-1.5a.75.75 0 0 0 0 1.5h8.5a.75.75 0 0 0 0-1.5h-8.5ZM2 8a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm3.75-1.5a.75.75 0 0 0 0 1.5h8.5a.75.75 0 0 0 0-1.5h-8.5ZM2 12a1 1 0 1 1 0-2 1 1 0 0 1 0 2Zm3.75-1.5a.75.75 0 0 0 0 1.5h8.5a.75.75 0 0 0 0-1.5h-8.5Z"/></svg>',
    keywords: 'bullet list unordered ul',
    shortcut: 'Mod+Shift+8',
    action: (editor: Editor) => editor.chain().focus().toggleBulletList().run(),
  },
  orderedList: {
    id: 'orderedList',
    name: 'Numbered List',
    icon: '<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M2.003 2.5a.5.5 0 0 0-.723-.447l-1.003.5a.5.5 0 0 0 .446.895l.28-.14V6H.5a.5.5 0 0 0 0 1h2.006a.5.5 0 1 0 0-1h-.503V2.5ZM5 3.25a.75.75 0 0 1 .75-.75h8.5a.75.75 0 0 1 0 1.5h-8.5A.75.75 0 0 1 5 3.25Zm0 5a.75.75 0 0 1 .75-.75h8.5a.75.75 0 0 1 0 1.5h-8.5A.75.75 0 0 1 5 8.25Zm0 5a.75.75 0 0 1 .75-.75h8.5a.75.75 0 0 1 0 1.5h-8.5a.75.75 0 0 1-.75-.75ZM.924 10.32l.003-.004a.851.851 0 0 1 .144-.153A.66.66 0 0 1 1.5 10c.195 0 .306.068.374.146a.57.57 0 0 1 .128.376c0 .453-.269.682-.8 1.078l-.035.025C.692 11.98 0 12.495 0 13.5a.5.5 0 0 0 .5.5h2.003a.5.5 0 0 0 0-1H1.146c.132-.197.351-.372.654-.597l.047-.035c.47-.35 1.156-.858 1.156-1.845 0-.365-.118-.744-.377-1.038-.268-.303-.658-.484-1.126-.484-.48 0-.84.202-1.068.392a1.858 1.858 0 0 0-.348.384l-.007.011-.002.004-.001.002-.001.001a.5.5 0 0 0 .851.525ZM.5 10.055l-.427-.26.427.26Z"/></svg>',
    keywords: 'numbered list ordered ol',
    shortcut: 'Mod+Shift+7',
    action: (editor: Editor) => editor.chain().focus().toggleOrderedList().run(),
  },
  taskList: {
    id: 'taskList',
    name: 'Task List',
    icon: '<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M2.5 1.75a.25.25 0 0 1 .25-.25h8.5a.25.25 0 0 1 .25.25v7.736a.75.75 0 0 0 1.5 0V1.75A1.75 1.75 0 0 0 11.25 0h-8.5A1.75 1.75 0 0 0 1 1.75v11.5c0 .966.784 1.75 1.75 1.75h3.17a.75.75 0 0 0 0-1.5H2.75a.25.25 0 0 1-.25-.25V1.75ZM4.75 4a.75.75 0 0 0 0 1.5h4.5a.75.75 0 0 0 0-1.5h-4.5ZM4 7.75A.75.75 0 0 1 4.75 7h2a.75.75 0 0 1 0 1.5h-2A.75.75 0 0 1 4 7.75Zm11.774 3.537a.75.75 0 0 0-1.048-1.074L10.7 14.145 9.281 12.72a.75.75 0 0 0-1.062 1.058l1.943 1.95a.75.75 0 0 0 1.055.008l4.557-4.45Z"/></svg>',
    keywords: 'task list todo checkbox',
    action: (editor: Editor) => editor.chain().focus().toggleTaskList().run(),
  },
  blockquote: {
    id: 'blockquote',
    name: 'Quote',
    icon: '<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M2.5 3a.5.5 0 0 0-.5.5v2.5a.5.5 0 0 0 .5.5h5a.5.5 0 0 0 .5-.5V5.5a.5.5 0 0 0-.5-.5H5.25v-.75C5.25 3.56 5.81 3 6.5 3s1.25.56 1.25 1.25V5a.5.5 0 0 0 1 0v-.75C8.75 2.784 7.966 2 6.5 2S4.25 2.784 4.25 4.25V5H2.5v-.5ZM9.5 3a.5.5 0 0 0-.5.5v2.5a.5.5 0 0 0 .5.5h5a.5.5 0 0 0 .5-.5V5.5a.5.5 0 0 0-.5-.5H12.25v-.75c0-.69.56-1.25 1.25-1.25s1.25.56 1.25 1.25V5a.5.5 0 0 0 1 0v-.75C15.75 2.784 14.966 2 13.5 2S11.25 2.784 11.25 4.25V5H9.5v-.5Z"/></svg>',
    keywords: 'quote blockquote citation',
    shortcut: 'Mod+Shift+B',
    action: (editor: Editor) => editor.chain().focus().toggleBlockquote().run(),
  },
  codeBlock: {
    id: 'codeBlock',
    name: 'Code Block',
    icon: '<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M0 3.75C0 2.784.784 2 1.75 2h12.5c.966 0 1.75.784 1.75 1.75v8.5A1.75 1.75 0 0 1 14.25 14H1.75A1.75 1.75 0 0 1 0 12.25v-8.5Zm1.75-.25a.25.25 0 0 0-.25.25v8.5c0 .138.112.25.25.25h12.5a.25.25 0 0 0 .25-.25v-8.5a.25.25 0 0 0-.25-.25H1.75ZM7.25 8a.75.75 0 0 1-.22.53l-2.25 2.25a.75.75 0 0 1-1.06-1.06L5.44 8 3.72 6.28a.75.75 0 0 1 1.06-1.06l2.25 2.25c.141.14.22.331.22.53Zm1.5 1.5a.75.75 0 0 1 0-1.5h3a.75.75 0 0 1 0 1.5h-3Z"/></svg>',
    keywords: 'code block programming',
    shortcut: 'Mod+Alt+C',
    action: (editor: Editor) => editor.chain().focus().toggleCodeBlock().run(),
  },
  horizontalRule: {
    id: 'horizontalRule',
    name: 'Divider',
    icon: '<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M2 8a.75.75 0 0 1 .75-.75h10.5a.75.75 0 0 1 0 1.5H2.75A.75.75 0 0 1 2 8Z"/></svg>',
    keywords: 'divider horizontal rule separator line',
    action: (editor: Editor) => editor.chain().focus().setHorizontalRule().run(),
  },
  image: {
    id: 'image',
    name: 'Image',
    icon: '<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M1.75 2.5a.25.25 0 0 0-.25.25v10.5c0 .138.112.25.25.25h.94l.03-.03 6.077-6.078a1.75 1.75 0 0 1 2.412-.06L14.5 10.31V2.75a.25.25 0 0 0-.25-.25H1.75Zm12.5 11H4.81l5.048-5.047a.25.25 0 0 1 .344-.009l4.298 3.889v.917a.25.25 0 0 1-.25.25ZM1.75 1A1.75 1.75 0 0 0 0 2.75v10.5C0 14.216.784 15 1.75 15h12.5A1.75 1.75 0 0 0 16 13.25V2.75A1.75 1.75 0 0 0 14.25 1H1.75ZM5 5a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z"/></svg>',
    keywords: 'image picture photo',
    action: (editor: Editor) => {
      // 这里可以触发图片上传对话框
      const url = prompt('Enter image URL:')
      if (url) {
        editor.chain().focus().setImage({ src: url }).run()
      }
    },
  },
  table: {
    id: 'table',
    name: 'Table',
    icon: '<svg width="16" height="16" viewBox="0 0 16 16" fill="currentColor"><path d="M0 1.75C0 .784.784 0 1.75 0h12.5C15.216 0 16 .784 16 1.75v12.5A1.75 1.75 0 0 1 14.25 16H1.75A1.75 1.75 0 0 1 0 14.25V1.75Zm1.75-.25a.25.25 0 0 0-.25.25V6h4V1.5H1.75Zm4.5 0V6h4V1.5H6.25Zm4.5 0V6h4V1.75a.25.25 0 0 0-.25-.25H10.75ZM14.5 7.5h-4v3h4v-3Zm0 4.5h-4v2.25c0 .138.112.25.25.25H14.25a.25.25 0 0 0 .25-.25V12Zm-5.5 0h-4v2.25c0 .138.112.25.25.25h3.5V12Zm-4.5 0h-4v2.25c0 .138.112.25.25.25H4.5V12ZM1.5 10.5v-3h4v3h-4Zm4.5 0v-3h4v3H6Z"/></svg>',
    keywords: 'table grid data',
    action: (editor: Editor) => editor.chain().focus().insertTable({ rows: 3, cols: 3, withHeaderRow: true }).run(),
  },
}

// 配置点击菜单
export function createEnhancedClickMenu() {
  return ClickMenuExtension.configure({
    enabled: true,
    onMenu: ({ root, editor, active, selection }) => {
      // 创建菜单项
      const menuItems = [
        {
          label: 'Convert to Heading 1',
          action: () => editor.chain().focus().toggleHeading({ level: 1 }).run(),
        },
        {
          label: 'Convert to Heading 2',
          action: () => editor.chain().focus().toggleHeading({ level: 2 }).run(),
        },
        {
          label: 'Convert to Paragraph',
          action: () => editor.chain().focus().setParagraph().run(),
        },
        {
          label: 'Convert to Quote',
          action: () => editor.chain().focus().toggleBlockquote().run(),
        },
        {
          label: 'Convert to Code Block',
          action: () => editor.chain().focus().toggleCodeBlock().run(),
        },
        { label: '---', action: () => {} }, // 分隔符
        {
          label: 'Duplicate Block',
          action: () => {
            const { pos, node } = active
            const content = node.content.toJSON()
            editor.chain()
              .insertContentAt(pos.pos + node.nodeSize, content)
              .focus()
              .run()
          },
        },
        {
          label: 'Delete Block',
          action: () => {
            const { pos, node } = active
            const from = pos.pos - (node.isLeaf ? 0 : 1)
            const to = from + node.nodeSize
            editor.chain().deleteRange({ from, to }).focus().run()
          },
        },
      ]

      // 渲染菜单
      menuItems.forEach((item, index) => {
        if (item.label === '---') {
          const divider = document.createElement('div')
          divider.style.height = '1px'
          divider.style.background = '#e5e7eb'
          divider.style.margin = '8px 0'
          root.appendChild(divider)
        } else {
          const button = document.createElement('button')
          button.textContent = item.label
          button.style.cssText = `
            display: block;
            width: 100%;
            padding: 8px 12px;
            border: none;
            background: none;
            text-align: left;
            cursor: pointer;
            border-radius: 4px;
            font-size: 14px;
            transition: background-color 0.15s ease;
          `
          
          button.addEventListener('mouseenter', () => {
            button.style.background = '#f3f4f6'
          })
          
          button.addEventListener('mouseleave', () => {
            button.style.background = 'none'
          })
          
          button.addEventListener('click', () => {
            item.action()
          })
          
          root.appendChild(button)
        }
      })
    },
  })
}

// 配置块菜单
export function createEnhancedBlockMenu() {
  return BlockMenuExtension.configure({
    items: Object.keys(blockMenuItems),
    dictionary: {
      lineEmpty: "Type '/' for commands...",
      lineSlash: 'Continue typing to filter...',
      queryEmpty: 'No matching commands',
    },
  })
}

// 注册块菜单项到编辑器存储
export function registerBlockMenuItems(editor: Editor) {
  Object.entries(blockMenuItems).forEach(([key, item]) => {
    if (!editor.storage[key]) {
      editor.storage[key] = {}
    }
    editor.storage[key].blockMenu = {
      items: item,
    }
  })
}
