import { Plugin, PluginKey } from '@tiptap/pm/state'
import type { EditorView } from '@tiptap/pm/view'
import type { Editor } from '@tiptap/core'
import type { ClickMenuOptions, MenuState } from './types'

export const ClickMenuPluginKey = new PluginKey('clickMenu')

export interface ClickMenuPluginOptions extends ClickMenuOptions {
  editor: Editor
}

export class ClickMenuPlugin extends Plugin {
  private view: EditorView | null = null
  private menuElement: HTMLElement | null = null
  private showTimeout: ReturnType<typeof setTimeout> | null = null
  private hideTimeout: ReturnType<typeof setTimeout> | null = null

  constructor(private options: ClickMenuPluginOptions) {
    super({
      key: ClickMenuPluginKey,
      view: (editorView) => new ClickMenuPluginView(editorView, options),
      state: {
        init() {
          return {
            menuState: null as MenuState | null,
            isVisible: false,
            selectedIndex: 0
          }
        },
        apply(tr, oldState) {
          // 保持状态不变，除非有特定的事务
          return oldState
        }
      }
    })
  }
}

export class ClickMenuPluginView {
  private menuElement: HTMLElement | null = null
  private showTimeout: number | null = null
  private hideTimeout: number | null = null

  constructor(
    private view: EditorView,
    private options: ClickMenuPluginOptions
  ) {
    this.init()
  }

  private init() {
    if (!this.options.enabled) return

    // 创建菜单元素
    this.createMenuElement()
    
    // 绑定事件监听器
    this.bindEventListeners()
  }

  private createMenuElement() {
    this.menuElement = document.createElement('div')
    this.menuElement.className = 'click-menu'
    this.menuElement.style.cssText = `
      position: fixed;
      z-index: 1000;
      background: white;
      border: 1px solid #e0e0e0;
      border-radius: 8px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      padding: 8px;
      display: none;
      min-width: 200px;
    `
    
    document.body.appendChild(this.menuElement)
  }

  private bindEventListeners() {
    // 监听点击事件
    this.view.dom.addEventListener('click', this.handleClick.bind(this))
    
    // 监听鼠标移动事件
    this.view.dom.addEventListener('mousemove', this.handleMouseMove.bind(this))
    
    // 监听全局点击事件以隐藏菜单
    document.addEventListener('click', this.handleGlobalClick.bind(this))
    
    // 监听键盘事件
    document.addEventListener('keydown', this.handleKeyDown.bind(this))
  }

  private handleClick(event: MouseEvent) {
    if (!this.options.enabled) return

    const target = event.target as HTMLElement
    const clickableElement = this.findClickableElement(target)
    
    if (clickableElement) {
      // 再次验证位置有效性
      const pos = this.view.posAtDOM(clickableElement, 0)
      if (pos !== null && pos !== undefined && pos >= 0) {
        event.preventDefault()
        this.showMenuForElement(clickableElement, event)
      } else {
        console.warn('Clickable element found but position is invalid:', pos)
      }
    }
  }

  private handleMouseMove(event: MouseEvent) {
    // 清除隐藏定时器
    if (this.hideTimeout) {
      clearTimeout(this.hideTimeout)
      this.hideTimeout = null
    }
  }

  private handleGlobalClick(event: MouseEvent) {
    const target = event.target as HTMLElement
    
    // 如果点击在菜单外部，隐藏菜单
    if (this.menuElement && !this.menuElement.contains(target) && !this.view.dom.contains(target)) {
      this.hideMenu()
    }
  }

  private handleKeyDown(event: KeyboardEvent) {
    if (!this.menuElement || this.menuElement.style.display === 'none') return

    switch (event.key) {
      case 'Escape':
        event.preventDefault()
        this.hideMenu()
        break
      case 'ArrowUp':
        event.preventDefault()
        this.navigateMenu(-1)
        break
      case 'ArrowDown':
        event.preventDefault()
        this.navigateMenu(1)
        break
      case 'Enter':
        event.preventDefault()
        this.executeSelectedItem()
        break
    }
  }

  private findClickableElement(target: HTMLElement): HTMLElement | null {
    // 查找可点击的元素（段落、标题等）
    let element = target
    while (element && element !== this.view.dom) {
      if (this.isSupportedElement(element)) {
        // 验证元素是否在 ProseMirror 文档中有对应的位置
        const pos = this.view.posAtDOM(element, 0)
        if (pos !== null && pos !== undefined && pos >= 0) {
          return element
        }
      }
      element = element.parentElement as HTMLElement
    }
    return null
  }

  private isSupportedElement(element: HTMLElement): boolean {
    const tagName = element.tagName.toLowerCase()
    const supportedTags = ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'li', 'blockquote', 'pre']
    
    // 检查标签名
    if (!supportedTags.includes(tagName)) {
      return false
    }
    
    // 检查是否是 ProseMirror 内容节点
    if (element.closest('.ProseMirror')) {
      return true
    }
    
    return false
  }

  private showMenuForElement(element: HTMLElement, event: MouseEvent) {
    if (!this.menuElement) return

    // 清除之前的定时器
    if (this.showTimeout) clearTimeout(this.showTimeout)
    if (this.hideTimeout) clearTimeout(this.hideTimeout)

    // 延迟显示菜单
    this.showTimeout = setTimeout(() => {
      this.displayMenu(element, event)
    }, this.options.showDelay)
  }

  private displayMenu(element: HTMLElement, event: MouseEvent) {
    if (!this.menuElement) return

    // 验证元素位置
    const pos = this.view.posAtDOM(element, 0)
    if (pos === null || pos === undefined || pos < 0) {
      console.warn('Cannot display menu: invalid element position')
      return
    }

    // 获取元素位置
    const rect = element.getBoundingClientRect()
    const menuItems = this.getMenuItemsForElement(element)

    if (menuItems.length === 0) return

    // 清空菜单内容
    this.menuElement.innerHTML = ''

    // 添加菜单项
    menuItems.forEach((item, index) => {
      const menuItem = document.createElement('div')
      menuItem.className = 'click-menu-item'
      menuItem.style.cssText = `
        padding: 8px 12px;
        cursor: pointer;
        border-radius: 4px;
        display: flex;
        align-items: center;
        gap: 8px;
        transition: background-color 0.2s;
      `
      
      menuItem.innerHTML = `
        <span class="menu-icon">${item.icon}</span>
        <span class="menu-label">${item.label}</span>
        ${item.shortcut ? `<span class="menu-shortcut" style="margin-left: auto; opacity: 0.6; font-size: 12px;">${item.shortcut}</span>` : ''}
      `

      menuItem.addEventListener('mouseenter', () => {
        this.selectMenuItem(index)
      })

      menuItem.addEventListener('click', () => {
        this.executeMenuItem(item, element)
      })

      this.menuElement!.appendChild(menuItem)
    })

    // 计算菜单位置
    const position = this.calculateMenuPosition(rect)
    
    // 显示菜单
    this.menuElement.style.left = `${position.x}px`
    this.menuElement.style.top = `${position.y}px`
    this.menuElement.style.display = 'block'

    // 选中第一个菜单项
    this.selectMenuItem(0)
  }

  private getMenuItemsForElement(element: HTMLElement) {
    // 根据元素类型返回相应的菜单项
    const tagName = element.tagName.toLowerCase()
    const menuItems = []

    // 基础菜单项
    menuItems.push(
      {
        id: 'duplicate',
        label: '复制段落',
        icon: '📋',
        action: () => this.duplicateElement(element),
        shortcut: 'Ctrl+D'
      },
      {
        id: 'delete',
        label: '删除段落',
        icon: '🗑️',
        action: () => this.deleteElement(element),
        shortcut: 'Del'
      }
    )

    // 根据元素类型添加转换选项
    if (tagName === 'p') {
      menuItems.unshift(
        {
          id: 'to-h1',
          label: '转为标题 1',
          icon: 'H1',
          action: () => this.transformElement(element, 'heading', { level: 1 })
        },
        {
          id: 'to-h2',
          label: '转为标题 2',
          icon: 'H2',
          action: () => this.transformElement(element, 'heading', { level: 2 })
        },
        {
          id: 'to-h3',
          label: '转为标题 3',
          icon: 'H3',
          action: () => this.transformElement(element, 'heading', { level: 3 })
        }
      )
    } else if (tagName.startsWith('h')) {
      menuItems.unshift(
        {
          id: 'to-paragraph',
          label: '转为段落',
          icon: 'P',
          action: () => this.transformElement(element, 'paragraph')
        }
      )
    }

    return menuItems
  }

  private calculateMenuPosition(targetRect: DOMRect) {
    const menuWidth = 200 // 估算菜单宽度
    const menuHeight = 200 // 估算菜单高度
    const padding = 8

    let x = targetRect.left
    let y = targetRect.bottom + padding

    // 确保菜单不超出视窗
    if (x + menuWidth > window.innerWidth) {
      x = window.innerWidth - menuWidth - padding
    }

    if (y + menuHeight > window.innerHeight) {
      y = targetRect.top - menuHeight - padding
    }

    return { x, y }
  }

  private selectMenuItem(index: number) {
    if (!this.menuElement) return

    const items = this.menuElement.querySelectorAll('.click-menu-item')
    items.forEach((item, i) => {
      if (i === index) {
        (item as HTMLElement).style.backgroundColor = '#f0f0f0'
      } else {
        (item as HTMLElement).style.backgroundColor = 'transparent'
      }
    })

    // 更新选中索引
    const state = ClickMenuPluginKey.getState(this.view.state)
    if (state) {
      state.selectedIndex = index
    }
  }

  private navigateMenu(direction: number) {
    if (!this.menuElement) return

    const items = this.menuElement.querySelectorAll('.click-menu-item')
    const state = ClickMenuPluginKey.getState(this.view.state)
    
    if (!state || items.length === 0) return

    let newIndex = state.selectedIndex + direction
    if (newIndex < 0) newIndex = items.length - 1
    if (newIndex >= items.length) newIndex = 0

    this.selectMenuItem(newIndex)
  }

  private executeSelectedItem() {
    if (!this.menuElement) return

    const state = ClickMenuPluginKey.getState(this.view.state)
    if (!state) return

    const selectedItem = this.menuElement.querySelector('.click-menu-item[style*="background-color: rgb(240, 240, 240)"]') as HTMLElement
    if (selectedItem) {
      selectedItem.click()
    }
  }

  private executeMenuItem(item: any, element: HTMLElement) {
    try {
      // 验证元素位置
      const pos = this.view.posAtDOM(element, 0)
      if (pos === null || pos === undefined || pos < 0) {
        console.warn('Cannot execute menu item: invalid element position', {
          element,
          position: pos,
          item: item.label
        })
        return
      }
      
      item.action()
      this.hideMenu()
    } catch (error) {
      console.error('Error executing menu item:', error, {
        item: item.label,
        element
      })
      this.hideMenu()
    }
  }

  private duplicateElement(element: HTMLElement) {
    // 实现复制元素的逻辑
    const pos = this.view.posAtDOM(element, 0)
    
    // 检查位置是否有效
    if (pos === null || pos === undefined || pos < 0) {
      console.warn('Invalid position for element duplication:', pos)
      return
    }
    
    const node = this.view.state.doc.nodeAt(pos)
    
    if (node) {
      try {
        const tr = this.view.state.tr
        tr.insert(pos + node.nodeSize, node)
        this.view.dispatch(tr)
      } catch (error) {
        console.error('Error duplicating element:', error)
      }
    }
  }

  private deleteElement(element: HTMLElement) {
    // 实现删除元素的逻辑
    const pos = this.view.posAtDOM(element, 0)
    
    // 检查位置是否有效
    if (pos === null || pos === undefined || pos < 0) {
      console.warn('Invalid position for element deletion:', pos)
      return
    }
    
    const node = this.view.state.doc.nodeAt(pos)
    
    if (node) {
      try {
        const tr = this.view.state.tr
        tr.delete(pos, pos + node.nodeSize)
        this.view.dispatch(tr)
      } catch (error) {
        console.error('Error deleting element:', error)
      }
    }
  }

  private transformElement(element: HTMLElement, nodeType: string, attrs?: any) {
    // 实现转换元素的逻辑
    const pos = this.view.posAtDOM(element, 0)
    
    // 检查位置是否有效
    if (pos === null || pos === undefined || pos < 0) {
      console.warn('Invalid position for element transformation:', pos)
      return
    }
    
    const node = this.view.state.doc.nodeAt(pos)
    
    if (node) {
      const schema = this.view.state.schema
      const newNodeType = schema.nodes[nodeType]
      
      if (newNodeType) {
        try {
          const newNode = newNodeType.create(attrs, node.content, node.marks)
          const tr = this.view.state.tr
          tr.replaceWith(pos, pos + node.nodeSize, newNode)
          this.view.dispatch(tr)
        } catch (error) {
          console.error('Error transforming element:', error)
        }
      }
    }
  }

  private hideMenu() {
    if (!this.menuElement) return

    // 清除定时器
    if (this.showTimeout) {
      clearTimeout(this.showTimeout)
      this.showTimeout = null
    }

    // 延迟隐藏菜单
    this.hideTimeout = setTimeout(() => {
      if (this.menuElement) {
        this.menuElement.style.display = 'none'
      }
    }, this.options.hideDelay)
  }

  destroy() {
    // 清理资源
    if (this.showTimeout) clearTimeout(this.showTimeout)
    if (this.hideTimeout) clearTimeout(this.hideTimeout)
    
    if (this.menuElement) {
      document.body.removeChild(this.menuElement)
      this.menuElement = null
    }

    // 移除事件监听器
    this.view.dom.removeEventListener('click', this.handleClick.bind(this))
    this.view.dom.removeEventListener('mousemove', this.handleMouseMove.bind(this))
    document.removeEventListener('click', this.handleGlobalClick.bind(this))
    document.removeEventListener('keydown', this.handleKeyDown.bind(this))
  }
}

export function createClickMenuPlugin(options: ClickMenuPluginOptions) {
  return new ClickMenuPlugin(options)
}