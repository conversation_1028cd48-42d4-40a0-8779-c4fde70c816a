package com.shenmo.wen.app.core.comment.controller;

import com.shenmo.wen.app.core.comment.pojo.domain.WenCommonLocation;
import com.shenmo.wen.app.core.comment.pojo.param.WenCommentLoadParam;
import com.shenmo.wen.app.core.comment.pojo.param.WenCommentSaveParam;
import com.shenmo.wen.app.core.comment.pojo.vo.WenCommentVo;
import com.shenmo.wen.app.core.comment.service.WenCommentService;
import com.shenmo.wen.app.core.pojo.param.WenSearchParam;
import com.shenmo.wen.common.pojo.response.ResponseData;

import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/comments")
@RequiredArgsConstructor
public class WenCommentController {

    private final WenCommentService service;

    @PostMapping
    public ResponseData<Long> save(@Validated @RequestBody WenCommentSaveParam param) {
        return ResponseData.success(service.save(param));
    }

    @GetMapping
    public ResponseData<List<WenCommentVo>> load(@Validated WenCommentLoadParam param) {
        return ResponseData.success(service.load(param));
    }

    @GetMapping("/{id}")
    public ResponseData<WenCommentVo> loadById(@PathVariable("id") Long id) {
        return ResponseData.success(service.loadById(id));
    }

    @GetMapping("/parents/{id}")
    public ResponseData<List<WenCommentVo>> parents(@PathVariable("id") Long id) {
        return ResponseData.success(service.parents(id));
    }

    @GetMapping("/location/{id}")
    public ResponseData<WenCommonLocation> location(@PathVariable("id") Long id) {
        return ResponseData.success(service.location(id));
    }


    @GetMapping("/search")
    public ResponseData<List<WenCommentVo>> search(WenSearchParam param) {
        return ResponseData.success(service.search(param));
    }
}
