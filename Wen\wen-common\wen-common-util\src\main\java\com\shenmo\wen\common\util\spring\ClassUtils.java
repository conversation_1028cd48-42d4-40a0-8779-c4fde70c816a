package com.shenmo.wen.common.util.spring;

import com.shenmo.wen.common.util.ThrowUtils;
import org.springframework.lang.NonNull;

import java.util.Optional;

/**
 * 类对象工具
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public abstract class ClassUtils extends org.springframework.util.ClassUtils {

    /**
     * 获取当前类加载器
     *
     * @return 当前类加载器
     * <AUTHOR>
     */
    @NonNull
    public static ClassLoader currentClassLoader() {

        return Optional.ofNullable(getDefaultClassLoader()).orElseThrow(ThrowUtils.getThrowSupplier().internalServerError("Get current class loader fail"));
    }
}
