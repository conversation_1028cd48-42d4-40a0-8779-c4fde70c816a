/**
 * Performance monitoring utilities for TipTap extensions
 * Provides real-time performance tracking and optimization suggestions
 */

import { PerformanceConfigManager } from '../config/performanceConfig'

export interface PerformanceMetrics {
  dragOperations: {
    count: number
    averageTime: number
    maxTime: number
    minTime: number
    failureRate: number
  }
  menuOperations: {
    count: number
    averageTime: number
    maxTime: number
    minTime: number
    showTime: number
    hideTime: number
  }
  memoryUsage: {
    domNodes: number
    eventListeners: number
    timers: number
    estimatedMemory: number
  }
  renderingPerformance: {
    fps: number
    frameDrops: number
    longFrames: number
  }
}

/**
 * Performance metrics collector
 */
export class PerformanceMetricsCollector {
  private static instance: PerformanceMetricsCollector
  private metrics: PerformanceMetrics
  private measurements: Map<string, number[]> = new Map()
  private observers: PerformanceObserver[] = []
  private frameCount = 0
  private lastFrameTime = 0
  private frameDrops = 0
  private longFrames = 0

  private constructor() {
    this.metrics = this.initializeMetrics()
    this.setupPerformanceObservers()
    this.startFrameMonitoring()
  }

  static getInstance(): PerformanceMetricsCollector {
    if (!this.instance) {
      this.instance = new PerformanceMetricsCollector()
    }
    return this.instance
  }

  private initializeMetrics(): PerformanceMetrics {
    return {
      dragOperations: {
        count: 0,
        averageTime: 0,
        maxTime: 0,
        minTime: Infinity,
        failureRate: 0,
      },
      menuOperations: {
        count: 0,
        averageTime: 0,
        maxTime: 0,
        minTime: Infinity,
        showTime: 0,
        hideTime: 0,
      },
      memoryUsage: {
        domNodes: 0,
        eventListeners: 0,
        timers: 0,
        estimatedMemory: 0,
      },
      renderingPerformance: {
        fps: 0,
        frameDrops: 0,
        longFrames: 0,
      },
    }
  }

  private setupPerformanceObservers(): void {
    if (!('PerformanceObserver' in window)) return

    // Observe long tasks with better error handling
    this.setupLongTaskObserver()
    
    // Observe memory usage with better error handling
    this.setupMemoryObserver()
  }

  private setupLongTaskObserver(): void {
    try {
      const longTaskObserver = new PerformanceObserver((list) => {
        try {
          for (const entry of list.getEntries()) {
            if (entry.duration > 50) { // Tasks longer than 50ms
              this.longFrames++
            }
          }
        } catch (error) {
          console.warn('Error processing long task entries:', error)
        }
      })
      
      // Check if longtask is supported before observing
      if (PerformanceObserver.supportedEntryTypes?.includes('longtask')) {
        longTaskObserver.observe({ entryTypes: ['longtask'] })
        this.observers.push(longTaskObserver)
      }
    } catch (error) {
      console.warn('Long task observer not supported:', error)
    }
  }

  private setupMemoryObserver(): void {
    try {
      const memoryObserver = new PerformanceObserver((list) => {
        try {
          for (const entry of list.getEntries()) {
            if (entry.name === 'memory') {
              this.updateMemoryMetrics()
            }
          }
        } catch (error) {
          console.warn('Error processing memory entries:', error)
        }
      })
      
      // Check if measure is supported before observing
      if (PerformanceObserver.supportedEntryTypes?.includes('measure')) {
        memoryObserver.observe({ entryTypes: ['measure'] })
        this.observers.push(memoryObserver)
      }
    } catch (error) {
      console.warn('Memory observer not supported:', error)
    }
  }

  private startFrameMonitoring(): void {
    const measureFrame = () => {
      const now = performance.now()
      if (this.lastFrameTime > 0) {
        const frameDuration = now - this.lastFrameTime
        const expectedFrameDuration = 1000 / 60 // 60fps

        if (frameDuration > expectedFrameDuration * 1.5) {
          this.frameDrops++
        }

        this.frameCount++
        
        // Calculate FPS every second
        if (this.frameCount % 60 === 0) {
          this.metrics.renderingPerformance.fps = 1000 / frameDuration
          this.metrics.renderingPerformance.frameDrops = this.frameDrops
          this.metrics.renderingPerformance.longFrames = this.longFrames
        }
      }
      this.lastFrameTime = now
      requestAnimationFrame(measureFrame)
    }
    requestAnimationFrame(measureFrame)
  }

  /**
   * Record drag operation performance
   */
  recordDragOperation(duration: number, success: boolean): void {
    const config = PerformanceConfigManager.getConfig()
    if (!config.monitoring.enabled) return

    this.metrics.dragOperations.count++
    
    if (success) {
      this.addMeasurement('dragOperations', duration)
      this.updateOperationMetrics('dragOperations', duration)
    } else {
      this.metrics.dragOperations.failureRate = 
        (this.metrics.dragOperations.failureRate * (this.metrics.dragOperations.count - 1) + 1) / 
        this.metrics.dragOperations.count
    }
  }

  /**
   * Record menu operation performance
   */
  recordMenuOperation(type: 'show' | 'hide' | 'navigate', duration: number): void {
    const config = PerformanceConfigManager.getConfig()
    if (!config.monitoring.enabled) return

    this.metrics.menuOperations.count++
    this.addMeasurement('menuOperations', duration)
    this.updateOperationMetrics('menuOperations', duration)

    if (type === 'show') {
      this.metrics.menuOperations.showTime = duration
    } else if (type === 'hide') {
      this.metrics.menuOperations.hideTime = duration
    }
  }

  private addMeasurement(category: string, value: number): void {
    if (!this.measurements.has(category)) {
      this.measurements.set(category, [])
    }
    
    const measurements = this.measurements.get(category)!
    measurements.push(value)
    
    // Keep only recent measurements with efficient cleanup
    const config = PerformanceConfigManager.getConfig()
    if (measurements.length > config.monitoring.maxMeasurements) {
      // Remove multiple items at once for better performance
      const removeCount = Math.floor(config.monitoring.maxMeasurements * 0.2) // Remove 20%
      measurements.splice(0, removeCount)
    }
  }

  private updateOperationMetrics(category: keyof Pick<PerformanceMetrics, 'dragOperations' | 'menuOperations'>, duration: number): void {
    const metrics = this.metrics[category]
    
    metrics.maxTime = Math.max(metrics.maxTime, duration)
    metrics.minTime = Math.min(metrics.minTime, duration)
    
    // Calculate running average
    metrics.averageTime = (metrics.averageTime * (metrics.count - 1) + duration) / metrics.count
  }

  private updateMemoryMetrics(): void {
    // Estimate memory usage
    const domNodes = document.querySelectorAll('*').length
    const estimatedMemory = domNodes * 100 // Rough estimate: 100 bytes per DOM node
    
    this.metrics.memoryUsage.domNodes = domNodes
    this.metrics.memoryUsage.estimatedMemory = estimatedMemory
    
    // Get actual memory usage if available
    if ('memory' in performance) {
      const memory = (performance as any).memory
      this.metrics.memoryUsage.estimatedMemory = memory.usedJSHeapSize
    }
  }

  /**
   * Get current performance metrics
   */
  getMetrics(): PerformanceMetrics {
    this.updateMemoryMetrics()
    return { ...this.metrics }
  }

  /**
   * Get performance recommendations
   */
  getRecommendations(): string[] {
    const recommendations: string[] = []
    const metrics = this.getMetrics()

    // Check drag operation performance
    if (metrics.dragOperations.averageTime > 100) {
      recommendations.push('Drag operations are slow. Consider reducing DOM manipulations.')
    }
    
    if (metrics.dragOperations.failureRate > 0.1) {
      recommendations.push('High drag operation failure rate. Check error handling.')
    }

    // Check menu performance
    if (metrics.menuOperations.showTime > 50) {
      recommendations.push('Menu show time is slow. Consider lazy loading menu items.')
    }

    // Check memory usage
    if (metrics.memoryUsage.domNodes > 10000) {
      recommendations.push('High DOM node count. Consider virtual scrolling.')
    }

    // Check rendering performance
    if (metrics.renderingPerformance.fps < 30) {
      recommendations.push('Low FPS detected. Consider reducing animation complexity.')
    }

    if (metrics.renderingPerformance.frameDrops > 10) {
      recommendations.push('Frame drops detected. Consider throttling event handlers.')
    }

    return recommendations
  }

  /**
   * Reset all metrics
   */
  reset(): void {
    this.metrics = this.initializeMetrics()
    this.measurements.clear()
    this.frameCount = 0
    this.frameDrops = 0
    this.longFrames = 0
  }

  /**
   * Cleanup observers
   */
  cleanup(): void {
    this.observers.forEach(observer => observer.disconnect())
    this.observers = []
  }
}

/**
 * Performance profiler for specific operations
 */
export class PerformanceProfiler {
  private static activeProfiles = new Map<string, number>()

  /**
   * Start profiling an operation
   * @param operationName Unique name for the operation being profiled
   * @returns Function to end profiling, or no-op if monitoring disabled/not sampled
   */
  static start(operationName: string): (success?: boolean) => void {
    const config = PerformanceConfigManager.getConfig()
    
    // Early returns for disabled monitoring or sampling
    if (!config.monitoring.enabled || Math.random() > config.monitoring.sampleRate) {
      return () => {} // Return no-op function
    }

    const startTime = performance.now()
    this.activeProfiles.set(operationName, startTime)
    
    // Return end function with closure over start time for validation
    return (success: boolean = true) => {
      const profileStartTime = this.activeProfiles.get(operationName)
      if (profileStartTime === startTime) {
        this.end(operationName, success)
      }
      // Silently ignore if profile was already ended or doesn't match
    }
  }

  /**
   * End profiling and record results
   */
  static end(operationName: string, success: boolean = true): void {
    const startTime = this.activeProfiles.get(operationName)
    if (!startTime) return

    const duration = performance.now() - startTime
    this.activeProfiles.delete(operationName)

    const collector = PerformanceMetricsCollector.getInstance()
    
    if (operationName.includes('drag')) {
      collector.recordDragOperation(duration, success)
    } else if (operationName.includes('menu')) {
      const type = operationName.includes('show') ? 'show' : 
                   operationName.includes('hide') ? 'hide' : 'navigate'
      collector.recordMenuOperation(type, duration)
    }
  }

  /**
   * Profile a function execution
   */
  static profile<T>(operationName: string, fn: () => T): T {
    const endMeasure = this.start(operationName)
    try {
      const result = fn()
      endMeasure(true)
      return result
    } catch (error) {
      endMeasure(false)
      throw error
    }
  }

  /**
   * Profile an async function execution
   */
  static async profileAsync<T>(operationName: string, fn: () => Promise<T>): Promise<T> {
    const endMeasure = this.start(operationName)
    try {
      const result = await fn()
      endMeasure(true)
      return result
    } catch (error) {
      endMeasure(false)
      throw error
    }
  }
}

/**
 * Performance warning system
 */
export class PerformanceWarningSystem {
  private static warnings = new Set<string>()
  private static warningCallbacks: ((warning: string) => void)[] = []

  /**
   * Add warning callback
   */
  static onWarning(callback: (warning: string) => void): void {
    this.warningCallbacks.push(callback)
  }

  /**
   * Check performance and emit warnings
   */
  static checkPerformance(): void {
    const collector = PerformanceMetricsCollector.getInstance()
    const metrics = collector.getMetrics()
    const recommendations = collector.getRecommendations()

    recommendations.forEach(recommendation => {
      if (!this.warnings.has(recommendation)) {
        this.warnings.add(recommendation)
        this.warningCallbacks.forEach(callback => callback(recommendation))
      }
    })

    // Clear old warnings
    if (this.warnings.size > 10) {
      this.warnings.clear()
    }
  }

  /**
   * Start periodic performance checking
   */
  static startMonitoring(interval: number = 30000): void {
    setInterval(() => {
      this.checkPerformance()
    }, interval)
  }
}

/**
 * Export utilities
 */
export const PerformanceMonitoring = {
  MetricsCollector: PerformanceMetricsCollector,
  Profiler: PerformanceProfiler,
  WarningSystem: PerformanceWarningSystem,
}

export default PerformanceMonitoring