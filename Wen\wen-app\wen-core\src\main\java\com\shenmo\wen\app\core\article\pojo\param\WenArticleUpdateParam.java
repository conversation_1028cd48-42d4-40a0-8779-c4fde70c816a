package com.shenmo.wen.app.core.article.pojo.param;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class WenArticleUpdateParam {

    /**
     * 文章ID
     */
    @NotNull(message = "文章ID不可为空")
    private Long id;

    /**
     * 文章标题
     */
    @NotBlank(message = "文章标题不可为空")
    private String title;

    /**
     * 文章标签
     */
    private String tag;

    /**
     * 文章内容
     */
    @NotBlank(message = "文章内容不可为空")
    private String content;

    /**
     * 操作等级，默认值为0
     */
    private Integer operationLevel;

    /**
     * 发布范围：0-公开，1-个人<br>
     * 默认值为1
     */
    private Integer publishedScope;

    /**
     * 分享用户ID列表
     */
    private List<Long> shareUserIds;
}
