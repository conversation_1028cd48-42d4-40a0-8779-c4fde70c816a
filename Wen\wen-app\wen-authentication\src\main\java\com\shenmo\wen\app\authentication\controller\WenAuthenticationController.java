package com.shenmo.wen.app.authentication.controller;

import com.shenmo.wen.app.authentication.pojo.param.LoginParam;
import com.shenmo.wen.app.authentication.pojo.param.RegisterParam;
import com.shenmo.wen.app.authentication.service.WenAuthenticationService;
import com.shenmo.wen.common.pojo.response.ResponseData;
import com.shenmo.wen.modules.user.pojo.vo.WenUserVo;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

/**
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class WenAuthenticationController {
    private final WenAuthenticationService service;

    @PostMapping("/login")
    public ResponseData<WenUserVo> login(@Validated @RequestBody LoginParam param) {
        return ResponseData.success(service.login(param));
    }

    @PostMapping("/register")
    public ResponseData<WenUserVo> register(@Validated @RequestBody RegisterParam param) {
        return ResponseData.success(service.register(param));
    }

    @DeleteMapping("/logout")
    public ResponseData<Void> logout() {
        service.logout();
        return ResponseData.success();
    }
}
