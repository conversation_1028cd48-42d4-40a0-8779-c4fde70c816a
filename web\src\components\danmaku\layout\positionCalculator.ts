import logger from '@/utils/log'

/**
 * 获取弹幕右侧到屏幕右侧的距离
 * 用于计算弹幕在屏幕中的相对位置
 */
export function getDanmuRightPosition(el: HTMLDivElement, dmContainer: HTMLDivElement) {
  const eleWidth = el.offsetWidth || parseInt(el.style.width)
  const eleRight =
    el.getBoundingClientRect().right || dmContainer.getBoundingClientRect().right + eleWidth
  return dmContainer.getBoundingClientRect().right - eleRight
}
