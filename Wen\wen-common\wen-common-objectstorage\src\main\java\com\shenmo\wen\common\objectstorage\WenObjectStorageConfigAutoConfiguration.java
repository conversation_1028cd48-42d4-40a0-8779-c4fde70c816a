package com.shenmo.wen.common.objectstorage;

import com.aliyun.oss.OSSClientBuilder;
import com.shenmo.wen.common.objectstorage.properties.ObjectStorageProperties;
import com.shenmo.wen.common.objectstorage.template.AliObjectStorageTemplate;
import com.shenmo.wen.common.objectstorage.template.MinioObjectStorageTemplate;
import com.shenmo.wen.common.objectstorage.template.ObjectStorageTemplate;
import io.minio.MinioClient;
import lombok.RequiredArgsConstructor;
import org.springframework.boot.autoconfigure.AutoConfiguration;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnExpression;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;

/**
 * 对象存储 配置
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@AutoConfiguration
@RequiredArgsConstructor
@EnableConfigurationProperties(ObjectStorageProperties.class)
public class WenObjectStorageConfigAutoConfiguration {


    /**
     * 对象存储属性配置
     */
    private final ObjectStorageProperties objectStorageProps;


    /**
     * minio对象存储模板
     *
     * <AUTHOR>
     */
    @Bean
    @ConditionalOnClass(MinioClient.class)
    @ConditionalOnMissingBean
    @ConditionalOnExpression("'${server.object-storage.server-type}'.toString().equalsIgnoreCase('minio')")
    public ObjectStorageTemplate minioObjectStorageTemplate() {
        return new MinioObjectStorageTemplate(MinioClient.builder()
                .endpoint(objectStorageProps.getEndpoint())
                .credentials(objectStorageProps.getAccessKey(), objectStorageProps.getSecretKey())
                .build());
    }

    /**
     * ali对象存储模板
     *
     * <AUTHOR>
     */
    @Bean
    @ConditionalOnClass(OSSClientBuilder.class)
    @ConditionalOnMissingBean
    @ConditionalOnExpression("'${server.object-storage.server-type}'.toString().equalsIgnoreCase('ali_oss')")
    public ObjectStorageTemplate aliObjectStorageTemplate() {
        return new AliObjectStorageTemplate(new OSSClientBuilder()
                .build(objectStorageProps.getEndpoint(),
                        objectStorageProps.getAccessKey(),
                        objectStorageProps.getSecretKey()));
    }
}
