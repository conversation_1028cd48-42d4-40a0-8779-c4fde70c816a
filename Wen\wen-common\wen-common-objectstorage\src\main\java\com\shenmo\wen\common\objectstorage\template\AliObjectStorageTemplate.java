package com.shenmo.wen.common.objectstorage.template;

import com.aliyun.oss.HttpMethod;
import com.aliyun.oss.OSS;
import com.aliyun.oss.model.*;
import com.shenmo.wen.common.constant.StringConstant;
import com.shenmo.wen.common.objectstorage.response.*;
import com.shenmo.wen.common.util.AssertUtils;
import com.shenmo.wen.common.util.DateUtils;
import com.shenmo.wen.common.util.ThrowUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;
import org.springframework.util.CollectionUtils;

import java.io.File;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@RequiredArgsConstructor
public class AliObjectStorageTemplate extends AbstractObjectStorageTemplate implements ObjectStorageTemplate {

    /**
     * ali oss客户端
     */
    private final OSS ossClient;


    @Override
    public boolean bucketExists(String bucket) {

        try {
            return ossClient.doesBucketExist(bucket);
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public void makeBucket(String... bucket) {

        for (String b : bucket) {
            try {
                if (!bucketExists(b)) {

                    ossClient.createBucket(b);
                }
            } catch (Exception e) {
                log.warn(String.format("创建桶时失败: %s", b));
            }
        }
    }

    @NonNull
    @Override
    public List<AliBucketResponse> listBuckets() {
        try {
            return ossClient.listBuckets().stream().map(AliBucketResponse::new).collect(Collectors.toList());
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError("获取所有桶时失败", e);
        }
    }

    @NonNull
    @Override
    public List<String> listBucketNames() {
        try {
            return ossClient.listBuckets().stream()
                    .map(Bucket::getName)
                    .collect(Collectors.toList());
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError("获取所有桶名称时失败", e);
        }
    }

    @Override
    public String getPublicReadableBucketPolicy(String bucket) {

        return "{\n" +
                "    \"Version\": \"1\",\n" +
                "    \"Statement\": [\n" +
                "    {\n" +
                "        \"Action\": [\n" +
                "            \"oss:Get*\"\n" +
                "            \"oss:ListObjects\",\n" +
                "            \"oss:ListObjectVersions\"\n" +
                "        ],\n" +
                "        \"Effect\": \"Allow\",\n" +
                "        \"Principal\": [\n" +
                "            \"*\"\n" +
                "        ],\n" +
                "        \"Resource\": [\n" +
                "            \"acs:oss:*:*:" + bucket + "\"\n" +
                "        ]\n" +
                "    }\n" +
                "  ]\n" +
                "}";
    }

    @Override
    public void removeBucket(String... bucket) {
        for (String b : bucket) {
            if (bucketExists(b)) {
                removeObjects(b);
                try {
                    ossClient.deleteBucket(b);
                } catch (Exception e) {
                    throw ThrowUtils.getThrow().internalServerError(String.format("移除桶时失败: %s", b), e);
                }
            }
        }
    }

    @Override
    public void setBucketPolicy(String bucket, String policyConfig) {
        try {
            if (bucketExists(bucket)) {
                ossClient.setBucketPolicy(bucket, policyConfig);
            }
        } catch (Exception e) {
            log.warn(String.format("设置桶策略时失败: %s -> %s", bucket, policyConfig));
        }
    }

    @Override
    public boolean existObject(String bucket, String object) {
        try {
            return ossClient.doesObjectExist(bucket, object);
        } catch (Exception e) {
            return false;
        }
    }

    @NonNull
    @Override
    public PutObjectResponse putObject(String bucket, String object, InputStream inputStream, long size, String contentType) {

        AssertUtils.isTrue(bucketExists(bucket), String.format("新增对象时桶 %s 不存在", bucket));
        try {
            final ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentType(contentType);
            objectMetadata.setContentLength(size);
            return new PutObjectResponse(ossClient.putObject(bucket, object, inputStream, objectMetadata));
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError(String.format("新增对象时失败: %s -> %s:%s", bucket, object, contentType), e);
        }
    }

    @NonNull
    @Override
    public UploadObjectResponse uploadObject(String bucket, String object, String filePath) {

        AssertUtils.isTrue(bucketExists(bucket), String.format("上传对象时桶 %s 不存在", bucket));
        try {
            final ObjectMetadata objectMetadata = new ObjectMetadata();
            objectMetadata.setContentType(tika.detect(object));
            final File file = new File(filePath);
            objectMetadata.setContentLength(file.length());
            return new UploadObjectResponse(ossClient.putObject(bucket, object, file, objectMetadata), bucket, object);
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError(String.format("上传对象时失败: %s -> %s:%s", bucket, object, filePath), e);
        }
    }

    @NonNull
    @Override
    public CopyObjectResponse copyObject(String originBucket, String originObject, String targetBucket, String targetObject) {

        AssertUtils.isTrue(bucketExists(originBucket), String.format("复制对象时源桶 %s 不存在", originBucket));
        AssertUtils.isTrue(bucketExists(targetBucket), String.format("复制对象时目标桶 %s 不存在", targetBucket));
        try {
            return new CopyObjectResponse(ossClient.copyObject(originObject, originObject, targetBucket, targetObject));
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError(String.format("复制对象时失败: %s:%s -> %s:%s", originBucket, originObject, targetBucket, targetObject), e);
        }
    }

    @NonNull
    @Override
    public AliGetObjectResponse getObject(String bucket, String object) {
        if (!bucketExists(bucket)) {
            return new AliGetObjectResponse(new OSSObject());
        }
        try {
            return new AliGetObjectResponse(ossClient.getObject(bucket, object));
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError(String.format("获取对象时失败: %s -> %s", bucket, object), e);
        }
    }

    /**
     * 递归获取对象列表
     *
     * @param resultList 结果列表
     * @param bucket     同名
     * @param prefix     对象前缀
     * <AUTHOR>
     */
    private void listObjects(List<OSSObjectSummary> resultList, String bucket, @Nullable String prefix) {

        final ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucket,
                prefix,
                resultList.get(resultList.size() - 1).getKey(),
                null,
                1000);
        final ObjectListing objectListing = ossClient.listObjects(listObjectsRequest);
        final List<OSSObjectSummary> objectSummaries = objectListing.getObjectSummaries();
        if (!objectSummaries.isEmpty()) {
            resultList.addAll(objectSummaries);
            listObjects(resultList, bucket, prefix);
        }
    }

    @NonNull
    @Override
    public AliListObjectResponse listObjects(String bucket, @Nullable String prefix, int maxKeys, boolean recursive) {
        prefix = prefixFormatting(prefix);
        if (!bucketExists(bucket)) {
            return new AliListObjectResponse(List.of(), bucket, prefix);
        }
        final List<OSSObjectSummary> resultList = new ArrayList<>();
        if (maxKeys < 1 || maxKeys > 1000) {
            final ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucket,
                    prefix,
                    null,
                    null,
                    1000);
            final ObjectListing objectListing = ossClient.listObjects(listObjectsRequest);
            resultList.addAll(objectListing.getObjectSummaries());
            if (CollectionUtils.isEmpty(resultList)) {
                return new AliListObjectResponse(resultList, bucket, prefix);
            }
            try {
                listObjects(resultList, bucket, prefix);
            } catch (Exception e) {
                throw ThrowUtils.getThrow().internalServerError(String.format("获取某个桶下指定前缀下所有对象时失败: %s -> %s:%s:%s", bucket, prefix, maxKeys, recursive), e);
            }
            return new AliListObjectResponse(resultList, bucket, prefix);
        } else {
            final ListObjectsRequest listObjectsRequest = new ListObjectsRequest(bucket,
                    prefix,
                    null,
                    null,
                    maxKeys);
            final ObjectListing objectListing = ossClient.listObjects(listObjectsRequest);
            resultList.addAll(objectListing.getObjectSummaries());
            return new AliListObjectResponse(resultList, bucket, prefix);
        }
    }

    @Override
    public void removeObject(String bucket, String... object) {
        for (String obj : object) {
            try {
                while (existObject(bucket, obj)) {
                    ossClient.deleteObject(bucket, obj);
                }
            } catch (Exception e) {
                throw ThrowUtils.getThrow().internalServerError(String.format("移除对象时失败: %s -> %s", bucket, obj), e);
            }
        }
    }

    @NonNull
    @Override
    public String getPresignedObjectUrl(String bucket, String object, int expiry) {

        if (!bucketExists(bucket)) {
            return StringConstant.EMPTY;
        }
        try {
            return ossClient.generatePresignedUrl(bucket, object, DateUtils.offsetDay(DateUtils.nowDate(), expiry), HttpMethod.GET).toString();
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError(String.format("获取指定对象url时失败: %s -> %s:%s", bucket, object, expiry), e);
        }
    }

    @Override
    public void downloadObject(String bucket, String object, String filePath) {
        try {
            if (localFilePrepare(bucket, object, filePath)) {
                return;
            }
            ossClient.getObject(new GetObjectRequest(bucket, object), new File(filePath));
        } catch (Exception e) {
            throw ThrowUtils.getThrow().internalServerError(String.format("下载对象到本地时失败: %s -> %s:%s", bucket, object, filePath), e);
        }
    }
}
