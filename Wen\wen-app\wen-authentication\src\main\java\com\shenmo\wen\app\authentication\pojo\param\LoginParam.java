package com.shenmo.wen.app.authentication.pojo.param;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;


/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class LoginParam {

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式错误")
    private String phone;

    @NotBlank(message = "密码不能为空")
    @Length(min = 6, message = "密码长度至少为 6 个字符")
    private String password;

    @NotBlank(message = "验证码不能为空")
    private String cftt;
}
