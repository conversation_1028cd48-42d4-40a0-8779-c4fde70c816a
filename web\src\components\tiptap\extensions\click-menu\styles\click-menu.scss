// Simplified Click Menu Styles
.click-menu {
  position: absolute;
  z-index: 1000;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 4px;
  display: flex;
  flex-direction: column;
  gap: 2px;

  &__button {
    width: 24px;
    height: 24px;
    border: none;
    background: none;
    cursor: pointer;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #666;
    transition: background-color 0.2s ease;

    &:hover {
      background: #f0f0f0;
      color: #333;
    }

    svg {
      width: 14px;
      height: 14px;
    }
  }

  &__context {
    min-width: 150px;

    button {
      font-size: 13px;
      white-space: nowrap;
    }
  }

  &__item {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px 14px;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    border: none;
    background: none;
    width: 100%;
    text-align: left;
    font-size: 14px;
    font-weight: 500;
    color: var(--color-text);
    position: relative;
    overflow: hidden;

    // Modern hover and selection states
    &:hover {
      background: var(--color-background-hover);
      transform: translateX(2px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
    }

    &--selected,
    &:focus {
      background: var(--color-primary-light);
      color: var(--color-primary-dark);
      outline: 2px solid var(--color-primary);
      outline-offset: -2px;
      
      .click-menu__item__icon {
        color: var(--color-primary);
        transform: scale(1.1);
      }
    }

    &:active {
      transform: translateX(1px) scale(0.98);
    }

    &:disabled {
      opacity: 0.4;
      cursor: not-allowed;
      transform: none;
      
      &:hover {
        background: none;
        transform: none;
        box-shadow: none;
      }
    }

    // Ripple effect on click
    &::after {
      content: '';
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      background: rgba(var(--color-primary-rgb), 0.2);
      border-radius: 50%;
      transform: translate(-50%, -50%);
      transition: width 0.3s ease, height 0.3s ease;
      pointer-events: none;
    }

    &:active::after {
      width: 200px;
      height: 200px;
    }

    &__icon {
      width: 18px;
      height: 18px;
      display: flex;
      align-items: center;
      justify-content: center;
      flex-shrink: 0;
      transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
      border-radius: 4px;
      padding: 2px;
      
      svg {
        width: 14px;
        height: 14px;
      }
    }

    &__label {
      flex: 1;
      font-weight: 500;
      letter-spacing: -0.01em;
      line-height: 1.4;
    }

    &__shortcut {
      font-size: 11px;
      font-weight: 600;
      color: var(--color-text-secondary);
      background: var(--color-background-secondary);
      padding: 2px 6px;
      border-radius: 4px;
      border: 1px solid var(--color-border-light);
      opacity: 0.8;
      transition: all 0.2s ease;
      font-family: 'SF Mono', 'Monaco', 'Inconsolata', monospace;
      
      &:hover {
        opacity: 1;
        background: var(--color-background-tertiary);
      }
    }
  }

  &__divider {
    height: 1px;
    background: linear-gradient(90deg, 
      transparent 0%, 
      var(--color-border) 20%, 
      var(--color-border) 80%, 
      transparent 100%);
    margin: 6px 8px;
    opacity: 0.6;
  }

  &__group {
    &:not(:last-child) {
      position: relative;
      padding-bottom: 6px;
      margin-bottom: 6px;
      
      &::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 8px;
        right: 8px;
        height: 1px;
        background: linear-gradient(90deg, 
          transparent 0%, 
          var(--color-border) 20%, 
          var(--color-border) 80%, 
          transparent 100%);
        opacity: 0.4;
      }
    }
    
    &__title {
      font-size: 11px;
      font-weight: 600;
      color: var(--color-text-tertiary);
      text-transform: uppercase;
      letter-spacing: 0.05em;
      padding: 4px 14px 2px;
      margin-bottom: 2px;
    }
  }
}

// Enhanced animations for menu appearance
.click-menu-enter-active {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.click-menu-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0, 1, 1);
}

.click-menu-enter-from {
  opacity: 0;
  transform: scale(0.9) translateY(-12px);
  filter: blur(4px);
}

.click-menu-leave-to {
  opacity: 0;
  transform: scale(0.95) translateY(-4px);
  filter: blur(2px);
}

// Staggered animation for menu items
.click-menu__item {
  animation: menu-item-enter 0.3s cubic-bezier(0.4, 0, 0.2, 1) backwards;
  
  @for $i from 1 through 10 {
    &:nth-child(#{$i}) {
      animation-delay: #{$i * 0.03}s;
    }
  }
}

@keyframes menu-item-enter {
  0% {
    opacity: 0;
    transform: translateX(-8px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

// Enhanced responsive design for mobile devices
@media (max-width: 768px) {
  .click-menu {
    min-width: 280px;
    max-width: calc(100vw - 24px);
    border-radius: 16px;
    padding: 8px;
    
    // Enhanced touch targets
    &__item {
      padding: 14px 18px;
      font-size: 16px;
      border-radius: 12px;
      gap: 12px;
      
      &:hover {
        transform: none; // Disable hover transforms on touch devices
      }
      
      &:active {
        transform: scale(0.96);
        background: var(--color-background-hover);
      }
      
      &__icon {
        width: 20px;
        height: 20px;
        
        svg {
          width: 16px;
          height: 16px;
        }
      }
      
      &__shortcut {
        font-size: 12px;
        padding: 4px 8px;
      }
    }
    
    &__group__title {
      padding: 6px 18px 4px;
      font-size: 12px;
    }
  }
}

// Tablet optimizations
@media (min-width: 769px) and (max-width: 1024px) {
  .click-menu {
    min-width: 240px;
    max-width: 350px;
    
    &__item {
      padding: 11px 15px;
      font-size: 15px;
    }
  }
}

// Large screen enhancements
@media (min-width: 1200px) {
  .click-menu {
    min-width: 260px;
    max-width: 380px;
    
    &__item {
      &:hover {
        transform: translateX(3px);
      }
    }
  }
}

// High DPI display optimizations
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .click-menu {
    border-width: 0.5px;
    
    &__item__shortcut {
      border-width: 0.5px;
    }
  }
}

// Dark mode enhancements
@media (prefers-color-scheme: dark) {
  .click-menu {
    box-shadow: 
      0 12px 40px rgba(0, 0, 0, 0.3),
      0 4px 16px rgba(0, 0, 0, 0.2),
      0 0 0 1px rgba(255, 255, 255, 0.1);
    
    &::before {
      background: linear-gradient(135deg, 
        rgba(255, 255, 255, 0.05) 0%, 
        rgba(255, 255, 255, 0.02) 100%);
    }
    
    &__item {
      &:hover {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
      }
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .click-menu-enter-active,
  .click-menu-leave-active {
    transition: opacity 0.2s ease !important;
  }
  
  .click-menu-enter-from,
  .click-menu-leave-to {
    transform: none !important;
    filter: none !important;
  }
  
  .click-menu__item {
    animation: none !important;
    
    &:hover,
    &:active {
      transform: none !important;
    }
    
    &::after {
      display: none;
    }
  }
  
  @keyframes menu-item-enter {
    0%, 100% {
      opacity: 1;
      transform: none;
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .click-menu {
    border: 2px solid var(--color-border);
    
    &__item {
      border: 1px solid transparent;
      
      &:hover,
      &--selected,
      &:focus {
        border-color: var(--color-primary);
        background: var(--color-background-hover);
      }
      
      &__shortcut {
        border: 2px solid var(--color-border);
      }
    }
    
    &__divider,
    &__group::after {
      background: var(--color-border);
      opacity: 1;
    }
  }
}

// Print styles
@media print {
  .click-menu {
    display: none !important;
  }
}