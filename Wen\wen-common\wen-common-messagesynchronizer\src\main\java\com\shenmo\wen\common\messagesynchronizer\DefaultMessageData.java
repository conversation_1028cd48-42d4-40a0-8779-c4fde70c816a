package com.shenmo.wen.common.messagesynchronizer;

import com.fasterxml.jackson.annotation.JsonIgnore;
import lombok.EqualsAndHashCode;
import lombok.Getter;
import lombok.ToString;

/**
 * 默认的消息数据
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Getter
@ToString
@EqualsAndHashCode
public class DefaultMessageData<T> implements MessageData<T> {

    /**
     * 空的消息数据
     */
    private static final MessageData<Object> EMPTY = new EmptyDefaultMessageData();
    /**
     * 消息唯一id
     */
    private String id;
    /**
     * 消息类型
     */
    private T type;
    /**
     * 消息具体数据
     */
    private Object data;

    /**
     * 构造方法
     *
     * @param id   消息唯一id
     * @param type 消息类型
     * @param data 消息具体数据
     * <AUTHOR>
     */
    private DefaultMessageData(String id, T type, Object data) {
        this.id = id;
        this.type = type;
        this.data = data;
    }

    /**
     * 无参构造,用于序列化操作
     *
     * <AUTHOR>
     */
    private DefaultMessageData() {
    }

    /**
     * 静态工厂方法构建对象
     *
     * @param id   消息唯一id
     * @param type 消息类型
     * @param data 消息具体数据
     * @return {@link DefaultMessageData}
     * <AUTHOR>
     */
    public static <T> MessageData<T> of(String id, T type, Object data) {

        return new DefaultMessageData<>(id, type, data);
    }

    /**
     * 获取{@link EmptyDefaultMessageData}
     *
     * @return {@link MessageData}
     * <AUTHOR>
     */
    public static MessageData<Object> empty() {

        return EMPTY;
    }

    @Override
    @JsonIgnore
    public boolean isEmpty() {
        return MessageData.super.isEmpty();
    }

    /**
     * 空的消息数据
     *
     * <AUTHOR>
     * @version 0.0.19
     */
    static class EmptyDefaultMessageData implements MessageData<Object> {

        /**
         * 构造方法
         *
         * <AUTHOR>
         */
        EmptyDefaultMessageData() {

        }

        @Override
        public String getId() {
            return null;
        }

        @Override
        public Object getType() {
            return null;
        }

        @Override
        public Object getData() {
            return null;
        }

        @Override
        @JsonIgnore
        public boolean isEmpty() {
            return true;
        }
    }
}
