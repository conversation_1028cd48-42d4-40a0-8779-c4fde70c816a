import { ref } from 'vue'

import { handleImageUpload } from '@/components/tiptap/extensions/image/ImageResourceManager'
import logger from '@/utils/log'

import { useImageEventHandler } from './composables/useImageEventHandler'
import { useImageUploadSuccess } from './composables/useImageUploadSuccess'

import type { Editor } from '@tiptap/core'
import type { Slice } from '@tiptap/pm/model'

/**
 * 定义事件回调参数类型
 */
export interface BaseEventParams {
  editor: EditorWithStorage
  event: ClipboardEvent | DragEvent
  slice: Slice
}

export interface PasteEventParams extends BaseEventParams {
  event: ClipboardEvent
}

export interface DropEventParams extends BaseEventParams {
  event: DragEvent
  moved: boolean
}

/**
 * 编辑器存储接口定义
 * 使用类型别名而不是接口继承，避免类型冲突
 */
export type EditorWithStorage = Editor & {
  storage: {
    image?: {
      transformSrc?: (src: string) => string
      getFullUrl?: (src: string) => string
    }
    markdown?: {
      getMarkdown: () => string
    }
  }
}

/**
 * 图片上传逻辑封装
 * 处理图片上传、粘贴、拖放等操作
 */
export const useImageUpload = () => {
  const imageInputRef = ref()

  /**
   * 处理图片选择后的上传逻辑
   * @param e 事件对象
   * @param editor 编辑器实例
   * @param fileBucket 文件存储桶
   * @param useThumbnail 是否使用缩略图
   */
  const handleImageChange = async (
    e: Event,
    editor: EditorWithStorage,
    fileBucket: string,
    useThumbnail: boolean,
  ): Promise<void> => {
    const target = e.target as HTMLInputElement
    if (target.files && target.files.length > 0) {
      const files = Array.from(target.files)
      handleImageChangeCallback(files, editor, fileBucket, useThumbnail)
    }
  }

  /**
   * 图片文件处理逻辑
   * @param files 文件数组
   * @param editor 编辑器实例
   * @param fileBucket 存储桶
   * @param useThumbnail 是否使用缩略图
   */
  const handleImageChangeCallback = (
    files: File[],
    editor: EditorWithStorage,
    fileBucket: string,
    useThumbnail: boolean,
  ) => {
    logger.debug('files: ', files)

    // 没有文件时直接返回
    if (!files.length) {
      return
    }

    // 图片文件处理
    files.forEach((file) => {
      // 检查是否为图片文件
      if (!file.type.startsWith('image/')) {
        return
      }

      // 处理图片上传
      handleImageUpload(file, editor, fileBucket, useThumbnail).then((imageUrl) => {
        logger.debug('Image uploaded, relative path:', imageUrl)

        // 使用图片上传后处理组合式函数
        const { handleImageUploadSuccess } = useImageUploadSuccess()
        handleImageUploadSuccess(editor, imageUrl)
      })
    })
  }

  // 使用图片事件处理组合式函数
  const { imageHandleCallback } = useImageEventHandler()

  return {
    imageInputRef,
    handleImageChange,
    handleImageChangeCallback,
    imageHandleCallback,
  }
}
