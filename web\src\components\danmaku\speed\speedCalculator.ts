/**
 * 根据屏幕宽度自适应计算弹幕速度
 * 此函数确保在不同屏幕大小下弹幕速度适当调整
 * @param baseSpeed 基础速度（像素/秒）
 * @param screenWidth 屏幕宽度
 * @returns 经过屏幕宽度调整后的速度值
 */
export function calculateDanmuSpeed(baseSpeed: number, screenWidth: number) {
  if (screenWidth < 320) {
    return baseSpeed * 0.25 // 窄屏，速度减少 4 倍
  } else if (screenWidth < 960) {
    return baseSpeed * 0.5 // 中等屏幕，速度减少 2 倍
  } else if (screenWidth >= 1920) {
    return baseSpeed // 宽屏，速度保持不变
  } else {
    // 介于 960px 和 1920px 之间，线性插值
    const scaleFactor = 0.5 + ((screenWidth - 960) / (1920 - 960)) * 0.5
    return baseSpeed * scaleFactor
  }
}
