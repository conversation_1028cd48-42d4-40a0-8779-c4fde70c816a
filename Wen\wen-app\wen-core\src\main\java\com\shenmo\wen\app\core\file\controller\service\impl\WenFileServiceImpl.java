package com.shenmo.wen.app.core.file.controller.service.impl;

import cn.dev33.satoken.stp.StpUtil;

import com.shenmo.wen.app.core.file.controller.service.WenFileService;
import com.shenmo.wen.app.core.file.exception.FileExceptionEnum;
import com.shenmo.wen.app.core.user.exception.UserExceptionEnum;
import com.shenmo.wen.common.constant.BucketConstant;
import com.shenmo.wen.common.objectstorage.properties.ObjectStorageProperties;
import com.shenmo.wen.common.objectstorage.template.ObjectStorageTemplate;
import com.shenmo.wen.common.util.AssertUtils;
import com.shenmo.wen.common.util.AsyncUtils;
import com.shenmo.wen.common.util.EnCodingUtils;
import com.shenmo.wen.common.util.ThumbnailUtils;
import com.shenmo.wen.modules.user.mapper.WenUserMapper;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.util.List;
import java.util.function.Consumer;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
public class WenFileServiceImpl implements WenFileService {
    private final ObjectStorageProperties objectStorageProperties;
    private final ObjectStorageTemplate objectStorageTemplate;
    private final WenUserMapper userMapper;

    @Override
    public String save(String bucket, MultipartFile file) throws IOException {
        return save(bucket, file, fileUri -> {
        });
    }

    @Override
    public String save(String bucket, MultipartFile file, Consumer<String> fileUriConsumer) throws IOException {
        final long loginId = StpUtil.getLoginIdAsLong();
        AssertUtils.isTrue(userMapper.existsById(loginId), UserExceptionEnum.USER_NOT_EXISTS);
        final List<String> allowAccessBuckets = objectStorageProperties.getAllowAccessBuckets();
        AssertUtils.isTrue(allowAccessBuckets.contains(bucket), FileExceptionEnum.BUCKET_NOT_ALLOW_ACCESS);
        final byte[] fileBytes = file.getBytes();
        final int size = ThumbnailUtils.generateThumbnailSize(bucket);
        final byte[] thumbnailBytes = ThumbnailUtils.generateThumbnailFromInputStreamAsBytes(file.getInputStream(), size, size);
        final String hash = EnCodingUtils.calculateHash(new ByteArrayInputStream(thumbnailBytes));
        final String thumbnailObj = bucket + "/" + hash;
        final String thumbnailFileUri = String.format("/%s/%s", BucketConstant.THUMBNAIL, thumbnailObj);
        if (objectStorageTemplate.existObject(BucketConstant.THUMBNAIL, thumbnailObj)) {
            if (objectStorageTemplate.existObject(bucket, hash)) {
                fileUriConsumer.accept(thumbnailFileUri);
                return thumbnailFileUri;
            }
        }
        objectStorageTemplate.putObject(BucketConstant.THUMBNAIL,
                thumbnailObj,
                thumbnailBytes,
                thumbnailBytes.length,
                objectStorageTemplate.tika().detect(thumbnailBytes));
        AsyncUtils.asyncExecutor(() -> {
            if (objectStorageTemplate.existObject(bucket, hash)) {
                fileUriConsumer.accept(thumbnailFileUri);
                return;
            }
            objectStorageTemplate.putObject(bucket,
                    hash,
                    fileBytes,
                    fileBytes.length,
                    objectStorageTemplate.tika().detect(fileBytes));
            fileUriConsumer.accept(thumbnailFileUri);
        });
        return thumbnailFileUri;
    }
}
