import { Client } from '@stomp/stompjs'
import SockJS from 'sockjs-client'

import config from '@/config'
import logger from '@/utils/log'

import type { Message, StompSubscription } from '@stomp/stompjs'
// 定义 WebSocket 连接类
class WebSocket {
  static instance: WebSocket = new WebSocket()
  private stompClient: Client | null = null
  private socketUrl: string = config.backend.wsURL
  private subscriptions: { [destination: string]: StompSubscription } = {}

  // 连接到 WebSocket 服务
  connect(): void {
    const socket = new SockJS(this.socketUrl, null, { transports: ['websocket', 'xhr-streaming'] })
    this.stompClient = new Client({
      webSocketFactory: () => socket,
      debug: (str) => {
        if (config.logLevel === logger.levels.DEBUG) {
          logger.debug(str)
        }
      },
    })

    this.stompClient.onConnect = (frame) => {
      logger.info('ws connected: ' + frame)
    }

    this.stompClient.activate()
  }

  // 订阅指定目标地址并指定回调函数
  subscribe(destination: string, onMessageReceived: (message: string) => void): void {
    if (this.stompClient === null) {
      logger.warn('WebSocket client is not connected')
      return
    }
    if (this.stompClient.connected) {
      // 订阅指定目标地址
      const subscription = this.stompClient.subscribe(destination, (message: Message) => {
        if (message.body) {
          // 根据目标地址调用对应的回调函数
          onMessageReceived(message.body)
        }
      })

      // 保存订阅信息到 subscriptions 对象
      this.subscriptions[destination] = subscription
      logger.info(`Subscribed to: ${destination}`)
    } else {
      logger.info(`WebSocket is not connected, retrying to subscribe to: ${destination}`)
      setTimeout(() => {
        this.subscribe(destination, onMessageReceived)
      }, 1000) // 延迟 1 秒后重试订阅
    }
  }

  // 取消对指定目标地址的订阅
  unsubscribe(destination: string): void {
    const subscription = this.subscriptions[destination]
    if (subscription) {
      subscription.unsubscribe()
      delete this.subscriptions[destination] // 从订阅记录中移除
      logger.info(`Unsubscribed from: ${destination}`)
    } else {
      logger.warn(`No subscription found for: ${destination}`)
    }
  }

  // 关闭 WebSocket 连接
  disconnect(): void {
    if (this.stompClient !== null) {
      this.stompClient.deactivate().then(() => {
        logger.info('ws disconnected')
      })
    }
  }
}

export default WebSocket.instance
