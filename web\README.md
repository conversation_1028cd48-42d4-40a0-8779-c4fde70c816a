# Wen Web Frontend

A modern Vue 3 rich text editing platform with advanced drag-and-drop functionality and comprehensive keyboard accessibility support.

## Features

- **Rich Text Editor**: Advanced TipTap-based editor with drag-and-drop paragraph reordering
- **Accessibility First**: Comprehensive keyboard navigation and screen reader support
- **Click Menu System**: Context-aware menus for content transformation and actions
- **Real-time Collaboration**: WebSocket-based collaborative editing
- **Modern UI**: Naive UI components with responsive design

## Recommended IDE Setup

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (and disable Vetur).

## Type Support for `.vue` Imports in TS

TypeScript cannot handle type information for `.vue` imports by default, so we replace the `tsc` CLI with `vue-tsc` for type checking. In editors, we need [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) to make the TypeScript language service aware of `.vue` types.

## Customize configuration

See [Vite Configuration Reference](https://vite.dev/config/).

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```

### Run Unit Tests with [Vitest](https://vitest.dev/)

```sh
npm run test:unit
```

### Lint with [ESLint](https://eslint.org/)

```sh
npm run lint
```

## TipTap Editor Features

### Drag and Drop Functionality

The editor supports intuitive drag-and-drop paragraph reordering with visual feedback and keyboard accessibility.

#### Keyboard Shortcuts for Drag Operations

- **Alt + ↑/↓**: Move content up/down
- **Alt + Shift + ↑/↓**: Move to top/bottom
- **Alt + 1-6**: Convert to heading levels 1-6
- **Alt + h**: Convert to heading
- **Alt + p**: Convert to paragraph
- **Alt + l**: Convert to list
- **Alt + c**: Convert to code block
- **Alt + q**: Convert to blockquote
- **Alt + d**: Duplicate content
- **Alt + Delete**: Delete content
- **Ctrl + Space** or **Alt + m**: Open context menu

### Click Menu System

Context-aware menus provide quick access to content transformation and formatting options.

#### Menu Navigation Shortcuts

- **↑/↓**: Navigate menu items
- **Home/End**: Jump to first/last item
- **Tab/Shift+Tab**: Navigate menu items
- **Enter/Space**: Execute selected item
- **Escape**: Close menu

#### Quick Transformation Shortcuts (in menu)

- **1-6**: Convert to heading levels
- **p**: Convert to paragraph
- **c**: Convert to code block
- **q**: Convert to blockquote
- **l**: Convert to list
- **u**: Convert to bullet list
- **o**: Convert to ordered list
- **t**: Insert table
- **i**: Insert image
- **d**: Duplicate content
- **Delete**: Delete content

### Accessibility Features

- **Screen Reader Support**: Comprehensive ARIA labels and announcements
- **Keyboard Navigation**: Full keyboard accessibility for all features
- **Focus Management**: Proper focus handling during drag operations
- **Context Announcements**: Detailed status updates for screen readers
- **Reduced Motion**: Respects user's motion preferences
