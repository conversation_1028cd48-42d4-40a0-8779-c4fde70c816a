import { ref } from 'vue'

/**
 * 星星样式生成逻辑
 * 抽取自BackgroundAnimation组件
 */
export function useStarStyles() {
  // 星星样式生成
  const getStarStyle = (index: number) => {
    const size = Math.floor(Math.random() * 2) + 1 // 改为1-3px，减小最大尺寸
    const left = Math.floor(Math.random() * 100)
    const top = Math.floor(Math.random() * 100)
    const opacity = Math.random() * 0.5 + 0.3
    const animation = index % 5 === 0 ? 'twinkle 3s infinite' : 'none'

    return {
      width: `${size}px`,
      height: `${size}px`,
      left: `${left}%`,
      top: `${top}%`,
      opacity: opacity,
      animation: animation,
    }
  }

  return {
    getStarStyle,
  }
}
