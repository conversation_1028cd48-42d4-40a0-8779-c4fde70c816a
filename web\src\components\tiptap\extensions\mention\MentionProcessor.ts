/**
 * Mention元素处理器
 * 用于处理mention元素的初始化和修复
 */

import fileApi from '@/api/file'

/**
 * 修复mention元素，确保样式和头像正确显示
 * 使用Vue响应式方式而非直接DOM操作
 * @param container 容器元素，如果不提供则处理整个文档
 */
export function repairMentionElements(container?: HTMLElement) {
  const targetElement = container || document
  const mentionElements = targetElement.querySelectorAll('span[data-type="mention"]')

  mentionElements.forEach((element) => {
    const span = element as HTMLElement
    const label = span.getAttribute('data-label')
    const avatar = span.getAttribute('data-avatar')

    // 确保基本属性
    if (!span.classList.contains('mention')) {
      span.classList.add('mention')
    }

    // 设置contenteditable为false
    span.setAttribute('contenteditable', 'false')

    // 检查是否需要修复内容结构
    const hasNameSpan = span.querySelector('.mention-name')
    const hasAvatar = span.querySelector('.mention-avatar')

    // 只在必要时进行最小化的DOM修复
    if (!hasNameSpan && label) {
      const nameSpan = document.createElement('span')
      nameSpan.className = 'mention-name'
      nameSpan.textContent = `@${label}`
      span.insertBefore(nameSpan, span.firstChild)
    }

    if (!hasAvatar && avatar) {
      const avatarImg = document.createElement('img')
      avatarImg.className = 'mention-avatar'
      avatarImg.src = fileApi.getResourceURL(avatar)
      avatarImg.alt = label || ''
      avatarImg.loading = 'lazy'
      span.appendChild(avatarImg)
    }
  })
}

/**
 * 检查mention元素是否需要修复
 * @param element mention元素
 * @returns 是否需要修复
 */
export function needsMentionRepair(element: HTMLElement): boolean {
  if (!element.matches('span[data-type="mention"]')) {
    return false
  }

  const hasNameSpan = !!element.querySelector('.mention-name')
  const hasAvatar = !!element.querySelector('.mention-avatar')
  const avatar = element.getAttribute('data-avatar')

  return !hasNameSpan || (!!avatar && !hasAvatar)
}
