import type { Editor } from '@tiptap/core'
import type { Node as ProseMirrorNode } from '@tiptap/pm/model'
import type { Transaction } from '@tiptap/pm/state'

/**
 * Content transformation utilities for different node types
 */
export class ContentTransformationUtils {
  /**
   * Transform paragraph to heading with specified level
   */
  static transformParagraphToHeading(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number, 
    level: 1 | 2 | 3 | 4 | 5 | 6
  ): boolean {
    try {
      if (node.type.name !== 'paragraph') return false
      
      const headingType = editor.schema.nodes.heading
      if (!headingType) return false
      
      const tr = editor.state.tr
      const attrs = { level, ...node.attrs }
      tr.setNodeMarkup(pos, headingType, attrs)
      editor.view.dispatch(tr)
      
      return true
    } catch (error) {
      console.error('Failed to transform paragraph to heading:', error)
      return false
    }
  }

  /**
   * Transform heading to paragraph
   */
  static transformHeadingToParagraph(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      if (node.type.name !== 'heading') return false
      
      const paragraphType = editor.schema.nodes.paragraph
      if (!paragraphType) return false
      
      const tr = editor.state.tr
      const attrs = { ...node.attrs }
      delete attrs.level // Remove heading-specific attributes
      tr.setNodeMarkup(pos, paragraphType, attrs)
      editor.view.dispatch(tr)
      
      return true
    } catch (error) {
      console.error('Failed to transform heading to paragraph:', error)
      return false
    }
  }

  /**
   * Transform paragraph/heading to list item
   */
  static transformToListItem(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number, 
    listType: 'bulletList' | 'orderedList'
  ): boolean {
    try {
      if (!['paragraph', 'heading'].includes(node.type.name)) return false
      
      // Use TipTap's built-in list commands
      editor.chain()
        .focus()
        .setTextSelection(pos)
        .toggleList(listType, 'listItem')
        .run()
      
      return true
    } catch (error) {
      console.error('Failed to transform to list item:', error)
      return false
    }
  }

  /**
   * Transform content to code block
   */
  static transformToCodeBlock(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number, 
    language?: string
  ): boolean {
    try {
      if (node.type.name === 'codeBlock') return false
      
      const codeBlockType = editor.schema.nodes.codeBlock
      if (!codeBlockType) return false
      
      const tr = editor.state.tr
      const attrs = { language: language || 'javascript' }
      tr.setNodeMarkup(pos, codeBlockType, attrs)
      editor.view.dispatch(tr)
      
      return true
    } catch (error) {
      console.error('Failed to transform to code block:', error)
      return false
    }
  }

  /**
   * Transform code block to paragraph
   */
  static transformCodeBlockToParagraph(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      if (node.type.name !== 'codeBlock') return false
      
      const paragraphType = editor.schema.nodes.paragraph
      if (!paragraphType) return false
      
      const tr = editor.state.tr
      tr.setNodeMarkup(pos, paragraphType, {})
      editor.view.dispatch(tr)
      
      return true
    } catch (error) {
      console.error('Failed to transform code block to paragraph:', error)
      return false
    }
  }

  /**
   * Duplicate node at position
   */
  static duplicateNode(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      const tr = editor.state.tr
      const nodeSize = node.nodeSize
      
      // Insert duplicate node after current node
      tr.insert(pos + nodeSize, node)
      editor.view.dispatch(tr)
      
      return true
    } catch (error) {
      console.error('Failed to duplicate node:', error)
      return false
    }
  }

  /**
   * Delete node at position
   */
  static deleteNode(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      const tr = editor.state.tr
      const nodeSize = node.nodeSize
      
      // Delete the node
      tr.delete(pos, pos + nodeSize)
      editor.view.dispatch(tr)
      
      return true
    } catch (error) {
      console.error('Failed to delete node:', error)
      return false
    }
  }

  /**
   * Move node up (swap with previous sibling)
   */
  static moveNodeUp(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      const { state } = editor
      const $pos = state.doc.resolve(pos)
      
      // Find parent and current index
      const parent = $pos.parent
      const currentIndex = $pos.index()
      
      if (currentIndex === 0) return false // Already at top
      
      const prevNode = parent.child(currentIndex - 1)
      const prevPos = $pos.start() - prevNode.nodeSize
      
      const tr = state.tr
      // Remove current node
      tr.delete(pos, pos + node.nodeSize)
      // Insert at previous position
      tr.insert(prevPos, node)
      
      editor.view.dispatch(tr)
      return true
    } catch (error) {
      console.error('Failed to move node up:', error)
      return false
    }
  }

  /**
   * Move node down (swap with next sibling)
   */
  static moveNodeDown(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      const { state } = editor
      const $pos = state.doc.resolve(pos)
      
      // Find parent and current index
      const parent = $pos.parent
      const currentIndex = $pos.index()
      
      if (currentIndex >= parent.childCount - 1) return false // Already at bottom
      
      const nextNode = parent.child(currentIndex + 1)
      const nextPos = pos + node.nodeSize
      
      const tr = state.tr
      // Remove next node first
      tr.delete(nextPos, nextPos + nextNode.nodeSize)
      // Insert next node before current
      tr.insert(pos, nextNode)
      
      editor.view.dispatch(tr)
      return true
    } catch (error) {
      console.error('Failed to move node down:', error)
      return false
    }
  }

  /**
   * Apply formatting to node content
   */
  static applyFormatting(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number, 
    markType: string
  ): boolean {
    try {
      const nodeSize = node.nodeSize
      const from = pos + 1 // Skip opening tag
      const to = pos + nodeSize - 1 // Skip closing tag
      
      // Select the content and apply formatting
      editor.chain()
        .focus()
        .setTextSelection({ from, to })
        .toggleMark(markType)
        .run()
      
      return true
    } catch (error) {
      console.error('Failed to apply formatting:', error)
      return false
    }
  }

  /**
   * Transform blockquote to paragraph
   */
  static transformBlockquoteToParagraph(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      if (node.type.name !== 'blockquote') return false
      
      const paragraphType = editor.schema.nodes.paragraph
      if (!paragraphType) return false
      
      const tr = editor.state.tr
      const attrs = { ...node.attrs }
      delete attrs.citation // Remove blockquote-specific attributes
      delete attrs.style
      tr.setNodeMarkup(pos, paragraphType, attrs)
      editor.view.dispatch(tr)
      
      return true
    } catch (error) {
      console.error('Failed to transform blockquote to paragraph:', error)
      return false
    }
  }

  /**
   * Transform paragraph to blockquote
   */
  static transformParagraphToBlockquote(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      if (node.type.name !== 'paragraph') return false
      
      const blockquoteType = editor.schema.nodes.blockquote
      if (!blockquoteType) return false
      
      const tr = editor.state.tr
      const attrs = { ...node.attrs, style: 'default' }
      tr.setNodeMarkup(pos, blockquoteType, attrs)
      editor.view.dispatch(tr)
      
      return true
    } catch (error) {
      console.error('Failed to transform paragraph to blockquote:', error)
      return false
    }
  }

  /**
   * Get available transformations for a node type
   */
  static getAvailableTransformations(nodeType: string): string[] {
    const transformations: Record<string, string[]> = {
      paragraph: ['heading1', 'heading2', 'heading3', 'bulletList', 'orderedList', 'codeBlock', 'blockquote'],
      heading: ['paragraph', 'bulletList', 'orderedList', 'codeBlock', 'blockquote'],
      listItem: ['paragraph', 'heading1', 'heading2', 'codeBlock'],
      codeBlock: ['paragraph', 'heading1', 'heading2', 'blockquote'],
      blockquote: ['paragraph', 'heading1', 'heading2', 'bulletList', 'orderedList'],
      bulletList: ['orderedList'],
      orderedList: ['bulletList']
    }
    
    return transformations[nodeType] || []
  }

  /**
   * Get available formatting options for a node type
   */
  static getAvailableFormatting(nodeType: string): string[] {
    const formatting: Record<string, string[]> = {
      paragraph: ['bold', 'italic', 'underline', 'strike', 'code'],
      heading: ['bold', 'italic', 'underline', 'strike'],
      listItem: ['bold', 'italic', 'underline', 'strike', 'code'],
      blockquote: ['bold', 'italic', 'underline', 'strike', 'code'],
      codeBlock: [], // Code blocks typically don't support inline formatting
      bulletList: [], // List containers don't support direct formatting
      orderedList: [] // List containers don't support direct formatting
    }
    
    return formatting[nodeType] || []
  }

  /**
   * Check if transformation is valid for current context
   */
  static canTransform(
    editor: Editor, 
    fromType: string, 
    toType: string, 
    pos: number
  ): boolean {
    try {
      const { state } = editor
      const $pos = state.doc.resolve(pos)
      
      // Check if target node type exists in schema
      const targetNodeType = editor.schema.nodes[toType]
      if (!targetNodeType) return false
      
      // Check if transformation is allowed in current context
      const parent = $pos.parent
      return parent.canReplaceWith($pos.index(), $pos.index() + 1, targetNodeType)
    } catch {
      return false
    }
  }

  /**
   * Batch transform multiple nodes
   */
  static batchTransform(
    editor: Editor, 
    transformations: Array<{
      pos: number
      node: ProseMirrorNode
      targetType: string
      attrs?: Record<string, any>
    }>
  ): boolean {
    try {
      const tr = editor.state.tr
      
      // Sort by position (descending) to avoid position shifts
      const sortedTransformations = transformations.sort((a, b) => b.pos - a.pos)
      
      for (const { pos, targetType, attrs } of sortedTransformations) {
        const targetNodeType = editor.schema.nodes[targetType]
        if (targetNodeType) {
          tr.setNodeMarkup(pos, targetNodeType, attrs || {})
        }
      }
      
      editor.view.dispatch(tr)
      return true
    } catch (error) {
      console.error('Failed to batch transform nodes:', error)
      return false
    }
  }

  /**
   * Transform list item to paragraph
   */
  static transformListItemToParagraph(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      if (node.type.name !== 'listItem') return false
      
      // Use TipTap's built-in command to lift list item
      editor.chain()
        .focus()
        .setTextSelection(pos)
        .liftListItem('listItem')
        .run()
      
      return true
    } catch (error) {
      console.error('Failed to transform list item to paragraph:', error)
      return false
    }
  }

  /**
   * Split node at cursor position
   */
  static splitNode(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      const { state } = editor
      const $pos = state.doc.resolve(pos + 1) // Position inside the node
      
      const tr = state.tr
      tr.split($pos.pos)
      editor.view.dispatch(tr)
      
      return true
    } catch (error) {
      console.error('Failed to split node:', error)
      return false
    }
  }

  /**
   * Merge node with next sibling
   */
  static mergeWithNext(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      const { state } = editor
      const $pos = state.doc.resolve(pos)
      const parent = $pos.parent
      const currentIndex = $pos.index()
      
      if (currentIndex >= parent.childCount - 1) return false // No next sibling
      
      const nextNode = parent.child(currentIndex + 1)
      if (nextNode.type !== node.type) return false // Different types can't merge
      
      const tr = state.tr
      const nextPos = pos + node.nodeSize
      
      // Join the nodes
      tr.join(nextPos)
      editor.view.dispatch(tr)
      
      return true
    } catch (error) {
      console.error('Failed to merge with next node:', error)
      return false
    }
  }

  /**
   * Wrap node in another node type
   */
  static wrapNode(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number, 
    wrapperType: string, 
    attrs?: Record<string, any>
  ): boolean {
    try {
      const wrapperNodeType = editor.schema.nodes[wrapperType]
      if (!wrapperNodeType) return false
      
      const tr = editor.state.tr
      const from = pos
      const to = pos + node.nodeSize
      
      const range = tr.doc.resolve(from).blockRange(tr.doc.resolve(to))
      if (range) {
        tr.wrap(range, [
          { type: wrapperNodeType, attrs: attrs || {} }
        ])
      }
      
      editor.view.dispatch(tr)
      return true
    } catch (error) {
      console.error('Failed to wrap node:', error)
      return false
    }
  }

  /**
   * Unwrap node from its parent wrapper
   */
  static unwrapNode(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      const { state } = editor
      const $pos = state.doc.resolve(pos)
      
      // Find the parent that can be unwrapped
      for (let depth = $pos.depth; depth > 0; depth--) {
        const parent = $pos.node(depth)
        if (parent.type.name !== 'doc' && parent.type.name !== 'paragraph') {
          const range = $pos.blockRange($pos.doc.resolve(pos + node.nodeSize))
          if (range) {
            const tr = state.tr
            tr.lift(range, depth - 1)
            editor.view.dispatch(tr)
            return true
          }
        }
      }
      
      return false
    } catch (error) {
      console.error('Failed to unwrap node:', error)
      return false
    }
  }

  /**
   * Change text alignment for block nodes
   */
  static setTextAlignment(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number, 
    alignment: 'left' | 'center' | 'right' | 'justify'
  ): boolean {
    try {
      const tr = editor.state.tr
      const attrs = { ...node.attrs, textAlign: alignment }
      tr.setNodeMarkup(pos, null, attrs)
      editor.view.dispatch(tr)
      
      return true
    } catch (error) {
      console.error('Failed to set text alignment:', error)
      return false
    }
  }

  /**
   * Toggle node attribute
   */
  static toggleNodeAttribute(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number, 
    attributeName: string, 
    defaultValue: any = true
  ): boolean {
    try {
      const tr = editor.state.tr
      const currentValue = node.attrs[attributeName]
      const newValue = currentValue ? !currentValue : defaultValue
      const attrs = { ...node.attrs, [attributeName]: newValue }
      tr.setNodeMarkup(pos, null, attrs)
      editor.view.dispatch(tr)
      
      return true
    } catch (error) {
      console.error('Failed to toggle node attribute:', error)
      return false
    }
  }

  /**
   * Set node attribute to specific value
   */
  static setNodeAttribute(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number, 
    attributeName: string, 
    value: any
  ): boolean {
    try {
      const tr = editor.state.tr
      const attrs = { ...node.attrs, [attributeName]: value }
      tr.setNodeMarkup(pos, null, attrs)
      editor.view.dispatch(tr)
      
      return true
    } catch (error) {
      console.error('Failed to set node attribute:', error)
      return false
    }
  }

  /**
   * Remove node attribute
   */
  static removeNodeAttribute(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number, 
    attributeName: string
  ): boolean {
    try {
      const tr = editor.state.tr
      const attrs = { ...node.attrs }
      delete attrs[attributeName]
      tr.setNodeMarkup(pos, null, attrs)
      editor.view.dispatch(tr)
      
      return true
    } catch (error) {
      console.error('Failed to remove node attribute:', error)
      return false
    }
  }

  /**
   * Get node type specific formatting options
   */
  static getNodeSpecificOptions(nodeType: string): Record<string, any[]> {
    const options: Record<string, Record<string, any[]>> = {
      paragraph: {
        textAlign: ['left', 'center', 'right', 'justify'],
        indent: [0, 1, 2, 3, 4, 5, 6]
      },
      heading: {
        level: [1, 2, 3, 4, 5, 6],
        textAlign: ['left', 'center', 'right']
      },
      blockquote: {
        style: ['default', 'warning', 'info', 'success', 'error'],
        textAlign: ['left', 'center', 'right']
      },
      codeBlock: {
        language: ['javascript', 'typescript', 'python', 'java', 'css', 'html', 'json', 'markdown', 'sql', 'bash'],
        showLineNumbers: [true, false],
        wrap: [true, false]
      },
      image: {
        align: ['left', 'center', 'right'],
        width: ['auto', '25%', '50%', '75%', '100%']
      },
      horizontalRule: {
        style: ['solid', 'dashed', 'dotted', 'double'],
        thickness: ['thin', 'medium', 'thick']
      }
    }
    
    return options[nodeType] || {}
  }

  /**
   * Check if node supports specific transformation
   */
  static supportsTransformation(nodeType: string, transformation: string): boolean {
    const supportMatrix: Record<string, string[]> = {
      paragraph: ['heading', 'list', 'codeBlock', 'blockquote', 'split', 'align'],
      heading: ['paragraph', 'list', 'codeBlock', 'blockquote', 'level', 'align', 'anchor'],
      listItem: ['paragraph', 'heading', 'codeBlock', 'indent', 'checkbox'],
      codeBlock: ['paragraph', 'heading', 'blockquote', 'language', 'format'],
      blockquote: ['paragraph', 'heading', 'list', 'codeBlock', 'style', 'citation'],
      image: ['align', 'resize', 'caption', 'border', 'shadow'],
      table: ['row', 'column', 'header', 'cell'],
      horizontalRule: ['style', 'thickness']
    }
    
    return supportMatrix[nodeType]?.includes(transformation) || false
  }

  /**
   * Get contextual actions for node type
   */
  static getContextualActions(nodeType: string): string[] {
    const actions: Record<string, string[]> = {
      paragraph: ['indent', 'outdent', 'align', 'split', 'transform'],
      heading: ['level', 'align', 'anchor', 'collapsible', 'transform'],
      listItem: ['indent', 'outdent', 'checkbox', 'type', 'split', 'transform'],
      codeBlock: ['language', 'copy', 'format', 'lineNumbers', 'wrap', 'title', 'highlight'],
      blockquote: ['style', 'citation', 'align', 'transform'],
      image: ['align', 'resize', 'caption', 'border', 'shadow'],
      table: ['addRow', 'addColumn', 'deleteRow', 'deleteColumn', 'header'],
      horizontalRule: ['style', 'thickness']
    }
    
    return actions[nodeType] || ['duplicate', 'delete', 'move']
  }
}