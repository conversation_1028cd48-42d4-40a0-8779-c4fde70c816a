import { type ResponseData } from '@/types/response_data.types'
import api from '@/utils/api'
import tiptap from '@/utils/tiptap'

import type { Editor } from '@tiptap/core'
import type { AxiosError } from 'axios'

// 定义文章相关的请求参数类型
interface ArticleParams {
  id?: string
  title?: string
  tag?: string
  operationLevel?: number
  publishedScope?: number
  content?: string
  shareUserIds?: string[]
}

// 定义搜索参数类型
type SearchParams = {
  searchKey?: string
  owner?: boolean
  interaction?: boolean
  favorite?: boolean
  tag?: string
  id?: string | number
  loadSize?: number
  [key: string]: string | number | boolean | null | undefined
}

// 定义下载响应类型
interface DownloadResponse extends ResponseData<string> {
  headers: {
    'content-disposition'?: string
  }
}

const articleApi = {
  URL: '/core/articles',
  // 搜索
  search: async <T>(params?: SearchParams, signal?: AbortSignal): Promise<ResponseData<T>> => {
    const res = await api.get<T>(articleApi.URL, params, { signal }).catch((err) => {
      return api.handleError(err) as ResponseData<T>
    })
    return res.data as ResponseData<T>
  },
  // 详情
  title: async <T>(id: string): Promise<ResponseData<T>> => {
    const res = await api.get<T>(articleApi.URL + '/title/' + id).catch((err) => {
      return api.handleError(err) as ResponseData<T>
    })
    return res.data as ResponseData<T>
  },
  // 详情
  detail: async <T>(id: string): Promise<ResponseData<T>> => {
    const res = await api.get<T>(articleApi.URL + '/detail/' + id).catch((err) => {
      return api.handleError(err) as ResponseData<T>
    })
    return res.data as ResponseData<T>
  },

  // 保存
  save: async <T>(params?: ArticleParams): Promise<ResponseData<T>> => {
    const res = await api
      .post<T>(
        articleApi.URL,
        params as Record<string, string | number | boolean | null | undefined>,
      )
      .catch((err) => {
        return api.handleError(err) as ResponseData<T>
      })
    return res.data as ResponseData<T>
  },

  // 编辑
  edit: async <T>(params?: ArticleParams): Promise<ResponseData<T>> => {
    const res = await api
      .put<T>(
        articleApi.URL,
        params as Record<string, string | number | boolean | null | undefined>,
      )
      .catch((err) => {
        return api.handleError(err) as ResponseData<T>
      })
    return res.data as ResponseData<T>
  },

  // 切换发布范围
  togglePublishedScope: async <T>(id: string): Promise<ResponseData<T>> => {
    const res = await api.put<T>(`${articleApi.URL}/toggle-scope/${id}`).catch((err) => {
      return api.handleError(err) as ResponseData<T>
    })
    return res.data as ResponseData<T>
  },

  // 获取热门标签
  getHotTags: async <T>(limit: number = 5): Promise<ResponseData<T>> => {
    const res = await api.get<T>(`${articleApi.URL}/hot-tags`, { limit }).catch((err) => {
      return api.handleError(err) as ResponseData<T>
    })
    return res.data as ResponseData<T>
  },

  // 下载
  md: async (
    id: string,
    editor: Editor & { setContent: (content: unknown) => void; getMarkdown: () => string },
  ) => {
    try {
      const response = await api.get<DownloadResponse>(articleApi.URL + '/md/' + id)
      if (response && 'data' in response) {
        const data = response.data.data
        const dataList: string[] = data.split(/\r?\n/)
        const json = dataList.pop() || ''
        editor.setContent(tiptap.toJsonObject(json))
        const md = editor.getMarkdown()
        const blob = new Blob([dataList.join('\n') + '\n' + md], { type: 'text/markdown' })
        const url = window.URL.createObjectURL(blob)
        const link = document.createElement('a')
        link.href = url
        let filename = decodeURIComponent(
          response.data.headers['content-disposition']?.split('filename=')[1] || `article-${id}`,
        )
        filename = filename + '.md'
        document.body.appendChild(link)
        link.download = filename
        link.click()
        document.body.removeChild(link)
        window.URL.revokeObjectURL(url)
      }
    } catch (err) {
      api.handleError(err as AxiosError)
    }
  },

  // 删除文章
  delete: async <T>(id: string): Promise<ResponseData<T>> => {
    const res = await api.del<T>(`${articleApi.URL}/${id}`).catch((err) => {
      return api.handleError(err) as ResponseData<T>
    })
    return res.data as ResponseData<T>
  },
}

export default articleApi
