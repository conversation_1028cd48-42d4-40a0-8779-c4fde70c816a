package com.shenmo.wen.app.authentication.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.google.common.base.Supplier;
import com.shenmo.wen.app.authentication.config.properties.CloudflareTurnstileProperties;
import com.shenmo.wen.app.authentication.config.properties.UserConfigProperties;
import com.shenmo.wen.app.authentication.exception.AuthenticationException;
import com.shenmo.wen.app.authentication.exception.AuthenticationExceptionEnum;
import com.shenmo.wen.app.authentication.pojo.cloudflare.CloudflareTurnstileResponse;
import com.shenmo.wen.app.authentication.pojo.param.LoginParam;
import com.shenmo.wen.app.authentication.pojo.param.RegisterParam;
import com.shenmo.wen.app.authentication.service.WenAuthenticationService;
import com.shenmo.wen.common.util.AssertUtils;
import com.shenmo.wen.common.util.IpUtils;
import com.shenmo.wen.modules.user.mapper.WenUserMapper;
import com.shenmo.wen.modules.user.pojo.entity.WenUser;
import com.shenmo.wen.modules.user.pojo.vo.WenUserVo;

import lombok.extern.slf4j.Slf4j;
import lombok.RequiredArgsConstructor;

import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.client.RestTemplate;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;

import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WenAuthenticationServiceImpl implements WenAuthenticationService {
    private final WenUserMapper mapper;
    private final PasswordEncoder passwordEncoder;
    private final UserConfigProperties userConfigProperties;
    private final CloudflareTurnstileProperties cloudflareTurnstileProperties;
    private final RestTemplate restTemplate;

    @Override
    public WenUserVo login(LoginParam param) {
        verifyTurnstile(param.getCftt());
        final String phone = param.getPhone();
        final String password = param.getPassword();
        final Supplier<AuthenticationException> phoneOrPasswordMistakeExceptionSupplier = () -> new AuthenticationException(
                AuthenticationExceptionEnum.PHONE_OR_PASSWORD_MISTAKE);
        final WenUser user = Optional.ofNullable(mapper.byPhone(phone))
                .orElseThrow(phoneOrPasswordMistakeExceptionSupplier);
        if (!passwordEncoder.matches(password, user.getPassword())) {
            throw phoneOrPasswordMistakeExceptionSupplier.get();
        }
        final String ip = IpUtils.getIp();
        final Long id = user.getId();
        user.setIp(ip);
        mapper.updateIpById(id, ip);
        final WenUserVo userVo = new WenUserVo();
        BeanUtils.copyProperties(user, userVo);
        userVo.setIpLocation(IpUtils.getIpLocation(ip));
        StpUtil.login(id);
        return userVo;
    }

    @Override
    public WenUserVo register(RegisterParam param) {
        verifyTurnstile(param.getCftt());
        final String phone = param.getPhone();
        AssertUtils.isTrue(mapper.countByPhone(phone) == 0, AuthenticationExceptionEnum.PHONE_EXISTS);
        final String username = param.getUsername();
        AssertUtils.isTrue(mapper.countByUsername(username) == 0, AuthenticationExceptionEnum.USERNAME_EXISTS);
        final WenUser user = new WenUser();
        user.setUsername(username);
        user.setPhone(phone);
        final String password = param.getPassword();
        user.setPassword(passwordEncoder.encode(password));
        user.setAvatar(userConfigProperties.getAvatar());
        final String ip = IpUtils.getIp();
        user.setIp(ip);
        mapper.insert(user);
        final WenUserVo userVo = new WenUserVo();
        BeanUtils.copyProperties(user, userVo);
        userVo.setIpLocation(IpUtils.getIpLocation(ip));
        StpUtil.login(user.getId());
        return userVo;
    }

    @Override
    public void logout() {
        StpUtil.logout();
    }

    private void verifyTurnstile(String cftt) {
        if (!cloudflareTurnstileProperties.getEnabled()) {
            return;
        }
        AssertUtils.isTrue(StringUtils.isNotBlank(cftt), AuthenticationExceptionEnum.CF_TURNSTILE_NOT_BLANK);
        final String url = cloudflareTurnstileProperties.getUrl();
        final String secret = cloudflareTurnstileProperties.getSecret();

        // 构造参数
        MultiValueMap<String, String> param = new LinkedMultiValueMap<>();
        param.add("secret", secret);
        param.add("response", cftt);

        log.info("cloudflare turnstile param: {}", param);
        // 设置Content-Type
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_FORM_URLENCODED);

        HttpEntity<MultiValueMap<String, String>> request = new HttpEntity<>(param, headers);

        // 发送请求
        CloudflareTurnstileResponse response = restTemplate.postForObject(url, request,
                CloudflareTurnstileResponse.class);

        // 判断success字段
        boolean success = response != null && response.getSuccess();
        AssertUtils.isTrue(success, AuthenticationExceptionEnum.CF_TURNSTILE_MISTAKE);
    }
}
