// Final test script - paste this in browser console
console.log('=== Final Extension Test ===');

async function runFinalTest() {
  // Wait for editor
  const editor = await new Promise(resolve => {
    const check = () => {
      const ed = document.querySelector('.ProseMirror');
      if (ed) resolve(ed);
      else setTimeout(check, 100);
    };
    check();
  });

  console.log('✅ Editor found');

  // Check initial state
  console.log('Initial drag handles:', document.querySelectorAll('.drag-handle').length);
  console.log('Initial click menus:', document.querySelectorAll('.click-menu').length);

  // Get first paragraph
  const firstP = editor.querySelector('p');
  if (!firstP) {
    console.log('❌ No paragraphs found');
    return;
  }

  console.log('✅ First paragraph found');

  // Get positions
  const pRect = firstP.getBoundingClientRect();
  const editorRect = editor.getBoundingClientRect();

  console.log('Paragraph position:', pRect);
  console.log('Editor position:', editorRect);

  // Test mousemove event
  console.log('🧪 Testing mousemove event...');
  
  const mouseMoveEvent = new MouseEvent('mousemove', {
    bubbles: true,
    cancelable: true,
    view: window,
    clientX: pRect.left - 30,
    clientY: pRect.top + 10
  });

  // Dispatch on editor view
  editor.dispatchEvent(mouseMoveEvent);

  // Wait and check results
  await new Promise(resolve => setTimeout(resolve, 200));

  const dragHandles = document.querySelectorAll('.drag-handle');
  const clickMenus = document.querySelectorAll('.click-menu');

  console.log('After mousemove:');
  console.log('- Drag handles:', dragHandles.length);
  console.log('- Click menus:', clickMenus.length);

  // Check visibility
  let visibleDragHandles = 0;
  let visibleClickMenus = 0;

  dragHandles.forEach((handle, i) => {
    const style = getComputedStyle(handle);
    const isVisible = handle.style.display !== 'none' && 
                     style.display !== 'none' && 
                     style.visibility !== 'hidden';
    
    console.log(`Drag handle ${i}:`, {
      display: handle.style.display,
      computedDisplay: style.display,
      visibility: style.visibility,
      position: handle.style.position,
      left: handle.style.left,
      top: handle.style.top,
      isVisible
    });
    
    if (isVisible) visibleDragHandles++;
  });

  clickMenus.forEach((menu, i) => {
    const style = getComputedStyle(menu);
    const isVisible = menu.style.display !== 'none' && 
                     style.display !== 'none' && 
                     style.visibility !== 'hidden';
    
    console.log(`Click menu ${i}:`, {
      display: menu.style.display,
      computedDisplay: style.display,
      visibility: style.visibility,
      position: menu.style.position,
      left: menu.style.left,
      top: menu.style.top,
      isVisible
    });
    
    if (isVisible) visibleClickMenus++;
  });

  // Results
  console.log('\n=== RESULTS ===');
  if (visibleDragHandles > 0) {
    console.log('✅ Drag handles are working!', `(${visibleDragHandles} visible)`);
  } else {
    console.log('❌ Drag handles not working');
  }

  if (visibleClickMenus > 0) {
    console.log('✅ Click menus are working!', `(${visibleClickMenus} visible)`);
  } else {
    console.log('❌ Click menus not working');
  }

  // Test interaction
  if (visibleDragHandles > 0) {
    console.log('\n🧪 Testing drag handle interaction...');
    const firstHandle = Array.from(dragHandles).find(h => 
      h.style.display !== 'none' && getComputedStyle(h).display !== 'none'
    );
    
    if (firstHandle) {
      console.log('Found visible drag handle, testing click...');
      firstHandle.click();
      console.log('✅ Drag handle click test completed');
    }
  }

  if (visibleClickMenus > 0) {
    console.log('\n🧪 Testing click menu interaction...');
    const firstMenu = Array.from(clickMenus).find(m => 
      m.style.display !== 'none' && getComputedStyle(m).display !== 'none'
    );
    
    if (firstMenu) {
      console.log('Found visible click menu');
      const buttons = firstMenu.querySelectorAll('button');
      console.log('Click menu buttons:', buttons.length);
      
      if (buttons.length > 0) {
        console.log('✅ Click menu has interactive buttons');
      }
    }
  }

  console.log('\n=== Test Complete ===');
}

// Run the test
runFinalTest().catch(console.error);
