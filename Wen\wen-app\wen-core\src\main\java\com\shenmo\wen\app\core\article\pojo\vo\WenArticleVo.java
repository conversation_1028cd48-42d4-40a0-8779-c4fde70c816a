package com.shenmo.wen.app.core.article.pojo.vo;

import lombok.Data;

import java.util.List;

import com.shenmo.wen.modules.user.pojo.domain.WenSearchUser;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class WenArticleVo {
    /**
     * 文章ID
     */
    private Long id;

    /**
     * 文章标题
     */
    private String title;

    /**
     * 文章标签
     */
    private String tag;

    /**
     * 文章内容
     */
    private String content;

    /**
     * 文章发布人
     */
    private String publisher;

    /**
     * 文章发布人头像
     */
    private String publisherAvatar;

    /**
     * 是否文章拥有着
     */
    private Boolean isOwner;

    /**
     * 文章发布时的IP归属地
     */
    private String ipLocation;

    /**
     * 发布时间
     */
    private Long publishedAt;

    /**
     * 点赞数，默认值为0
     */
    private Integer likeCount;

    private Boolean isLike = false;

    /**
     * 点踩数，默认值为0
     */
    private Integer dislikeCount;
    private Boolean isDislike = false;

    /**
     * 评论数，默认值为0
     */
    private Integer commentCount;

    /**
     * 收藏数，默认值为0
     */
    private Integer favoriteCount;
    private Boolean isFavorite = false;

    /**
     * 操作等级，默认值为0
     */
    private Integer operationLevel;
    /**
     * 发布范围：0-公开，1-个人<br>
     * 默认值为1
     */
    private Integer publishedScope;
    private Long lastModified;
    private List<WenSearchUser> shareUsers;
}
