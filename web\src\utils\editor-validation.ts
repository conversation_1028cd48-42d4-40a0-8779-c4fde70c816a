import type { TiptapEditorRef } from '@/types/component-refs.types'
import message from '@/utils/message'

/**
 * 编辑器验证结果
 */
export interface EditorValidationResult {
  isValid: boolean
  content?: any
  message?: string
}

/**
 * 验证编辑器内容是否为空
 * @param editor Tiptap编辑器引用或编辑器对象
 * @param contentRef 内容引用对象
 * @param emptyMessage 内容为空时的提示信息
 * @returns 验证结果
 */
export function validateEditorContent(
  editor: TiptapEditorRef | { isEmpty: boolean; getJSON?: () => unknown } | null | undefined,
  contentRef: { value: any },
  emptyMessage: string = '内容不能为空哦~',
): EditorValidationResult {
  // 检查编辑器是否存在
  if (!editor) {
    const errorMessage = '编辑器初始化失败，请刷新后重试'
    message.warning(errorMessage)
    return {
      isValid: false,
      message: errorMessage,
    }
  }

  // 获取编辑器实例（可能是直接的编辑器对象或者包装的引用）
  const editorInstance = 'editor' in editor ? editor.editor : editor

  // 检查编辑器是否为空
  const editorIsEmpty = editorInstance.isEmpty
  const noContent = !contentRef.value

  // 如果编辑器为空，直接返回
  if (editorIsEmpty) {
    message.warning(emptyMessage)
    return {
      isValid: false,
      message: emptyMessage,
    }
  }

  // 如果编辑器不为空但内容引用为空，可能是数据同步问题，尝试获取编辑器内容
  if (noContent && !editorIsEmpty) {
    const editorContent = editorInstance.getJSON?.()
    if (editorContent) {
      contentRef.value = editorContent
      return {
        isValid: true,
        content: editorContent,
      }
    } else {
      message.warning(emptyMessage)
      return {
        isValid: false,
        message: emptyMessage,
      }
    }
  }

  // 内容有效
  return {
    isValid: true,
    content: contentRef.value,
  }
}

/**
 * 检查是否正在加载中，防止重复提交
 * @param loadingRef 加载状态引用
 * @param loadingMessage 正在加载时的提示信息
 * @returns 是否可以继续执行
 */
export function checkLoadingState(
  loadingRef: { value: boolean } | Map<string, boolean>,
  key?: string,
  loadingMessage: string = '正在处理中，请稍候...',
): boolean {
  let isLoading: boolean

  if (loadingRef instanceof Map && key) {
    isLoading = loadingRef.get(key) || false
  } else if ('value' in loadingRef) {
    isLoading = loadingRef.value
  } else {
    return true // 如果无法判断加载状态，允许继续执行
  }

  if (isLoading) {
    message.warning(loadingMessage)
    return false
  }

  return true
}

/**
 * 设置加载状态
 * @param loadingRef 加载状态引用
 * @param loading 加载状态
 * @param key 如果是Map类型，需要提供key
 */
export function setLoadingState(
  loadingRef: { value: boolean } | Map<string, boolean>,
  loading: boolean,
  key?: string,
): void {
  if (loadingRef instanceof Map && key) {
    loadingRef.set(key, loading)
  } else if ('value' in loadingRef) {
    loadingRef.value = loading
  }
}
