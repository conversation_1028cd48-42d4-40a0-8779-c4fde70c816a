import config from '@/config'
import { type ResponseData } from '@/types/response_data.types'
import api from '@/utils/api'

const fileApi = {
  URL: '/core/files',
  imageTypes: ['image/png', 'image/jpg', 'image/jpeg', 'image/gif', 'image/webp'],
  upload: async <T>(file: File, bucket: string): Promise<ResponseData<T>> => {
    const formData = new FormData()
    formData.append('bucket', bucket)
    formData.append('file', file)
    const res = await api
      .postFormData<T>(fileApi.URL, formData, {
        headers: { 'Content-Type': 'multipart/form-data' },
      })
      .catch((err) => {
        return api.handleError(err)
      })
    return res.data as ResponseData<T>
  },
  uploadImage: async <T>(file: File, bucket: string): Promise<ResponseData<T>> => {
    if (fileApi.imageTypes.includes(file.type)) {
      return fileApi.upload(file, bucket)
    }
    return Promise.reject(new Error('upload fail'))
  },
  getResourceURL: (uri: string): string => {
    if (!uri) {
      return ''
    }
    if (uri.startsWith('http')) {
      return uri
    }
    return `${config.backend.resourceURL}${uri}`
  },
}

export default fileApi
