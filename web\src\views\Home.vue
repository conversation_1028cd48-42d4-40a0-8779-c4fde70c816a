<template>
  <div class="common-layout">
    <!-- 顶部区域 -->
    <div class="common-layout-top">
      <transition name="fade-slide">
        <div class="left-controls-container" v-show="!homeState.isCardVisible.value">
          <div class="control-item">
            <NGradientText type="info" class="control-label">循环：</NGradientText>
            <NSwitch v-model:value="danmaku.danmakuLoop.value" size="small" />
          </div>
          <div class="control-item">
            <NGradientText type="info" class="control-label">暂停：</NGradientText>
            <NSwitch
              v-model:value="danmaku.danmakuPause.value"
              @update:value="
                (val) => danmaku.handleDanmakuPauseChange(val, homeState.commentDanmakuRef)
              "
              size="small"
            />
          </div>
        </div>
      </transition>
      <div class="middle-controls-container">
        <!-- 文章按钮容器，包含用于切换显示状态的按钮 -->
        <ToggleButton
          v-model:value="homeState.isCardVisible.value"
          @toggle="handleToggleCardVisibility"
        />

        <!-- 搜索容器 -->
        <SearchBar
          v-model="search.searchCondition.value"
          :placeholder="search.getSearchPlaceholder(homeState.isCardVisible.value)"
          @search="handleSearch"
        />

        <!-- 创建按钮容器 -->
        <CreateButton @click="homeState.openCreateArticleDialog" />
      </div>

      <!-- 创建文章弹框 -->
      <ArticleModal
        :ref="(el) => (homeState.articleModalRef.value = el)"
        @success="handleArticleSuccess"
      />
      <UserInfoGroup />
    </div>

    <!-- 标签栏区域 -->
    <div class="tag-bar-wrapper">
      <TagBar
        v-model="search.searchCondition.value.tag"
        @tagSelected="
          (tagName) =>
            search.handleTagSelected(
              tagName,
              homeState.isCardVisible.value,
              homeState.articleListRef,
              homeState.commentDanmakuRef,
            )
        "
      />
    </div>

    <div class="common-layout-content">
      <!-- 过渡卡片区域，极简过渡以提高速度 -->
      <transition
        @before-enter="transition.beforeEnter"
        @enter="transition.enter"
        @leave="transition.leave"
        :duration="{ enter: 200, leave: 200 }"
        mode="out-in"
      >
        <ArticleList
          v-if="homeState.isCardVisible.value"
          :key="'article'"
          :search-condition="search.searchCondition.value"
          :ref="(el) => (homeState.articleListRef.value = el)"
          @reset="homeState.resetArticleList"
        />

        <!-- 过渡评论区域，极简过渡以提高速度 -->
        <CommentDanmaku
          v-else
          :key="'comment'"
          :search-condition="search.searchCondition.value"
          :loop="danmaku.danmakuLoop.value"
          :pause="danmaku.danmakuPause.value"
          :ref="(el) => (homeState.commentDanmakuRef.value = el)"
        />
      </transition>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { NSwitch, NGradientText } from 'naive-ui'
import { onMounted, onUnmounted, nextTick, defineAsyncComponent } from 'vue'

// 导入组件
import ArticleList from '@/components/home/<USER>'
import CommentDanmaku from '@/components/home/<USER>'
import CreateButton from '@/components/home/<USER>'
import SearchBar from '@/components/home/<USER>'
import TagBar from '@/components/home/<USER>'
import ToggleButton from '@/components/home/<USER>'
import UserInfoGroup from '@/components/UserInfoGroup.vue'

// 导入 composables
import { useHomeDanmaku } from '@/composables/home/<USER>'
import { useHomeSearch } from '@/composables/home/<USER>'
import { useHomeState } from '@/composables/home/<USER>'
// 导入工具
import transition from '@/utils/transition'

// 导入异步组件
const ArticleModal = defineAsyncComponent(() => import('@/components/ArticleModal.vue'))

// 使用 composables
const search = useHomeSearch()
const danmaku = useHomeDanmaku()
const homeState = useHomeState()

// 创建窗口大小调整回调
const resizeCallback = homeState.createResizeCallback(danmaku.handleDanmakuResize)

// 生命周期钩子
onMounted(async () => {
  // 初始化状态
  homeState.initializeState()

  // 加载保存的搜索条件
  search.loadSearchCondition()

  // 等待DOM渲染完成后再进行搜索，确保组件refs已经正确绑定
  await nextTick()

  // 添加一个小延迟，确保ArticleList组件完全初始化
  setTimeout(() => {
    handleSearch()
  }, 50)

  // 窗口大小变化监听
  window.addEventListener('resize', resizeCallback)
})

onUnmounted(() => {
  // 清理搜索相关资源
  search.cleanup()

  // 清理状态相关资源
  homeState.cleanup(resizeCallback)
})

// 处理搜索
const handleSearch = (loadMore: boolean = false) => {
  console.log('handleSearch called:', {
    isCardVisible: homeState.isCardVisible.value,
    articleListRef: homeState.articleListRef.value,
    commentDanmakuRef: homeState.commentDanmakuRef.value,
    loadMore,
  })

  search.search(
    homeState.isCardVisible.value,
    homeState.articleListRef,
    homeState.commentDanmakuRef,
    loadMore,
  )
}

// 处理切换卡片显示状态
const handleToggleCardVisibility = () => {
  homeState.toggleCardVisibility(
    homeState.resetArticleList,
    handleSearch,
    danmaku.handleDanmakuSubscription,
  )
}

// 处理文章创建成功
const handleArticleSuccess = () => {
  homeState.resetArticleList()
  handleSearch()
}
</script>

<style scoped lang="scss">
.common-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  height: 100dvh;
  width: 100vw;
  width: 100dvw;

  .common-layout-top {
    padding: 1.25rem;
    background: linear-gradient(to bottom, var(--creamy-white-3), var(--creamy-white-2));
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 7.5rem;
    flex-wrap: wrap;

    .left-controls-container {
      display: flex;
      flex-direction: column;
      justify-content: flex-start;
      align-items: flex-start;
      position: absolute;
      left: 1.25rem;
      top: 7rem;

      .control-item {
        display: flex;
        align-items: center;
        color: var(--black);
        font-size: 0.9rem;

        .control-label {
          margin-right: 0;
        }
      }
    }

    .middle-controls-container {
      display: flex;
      justify-content: center;
      align-items: center;
      margin: auto;
      padding-left: 4.5%;
      max-width: 100%;
    }
  }

  .tag-bar-wrapper {
    min-height: 2.5rem;
    padding-bottom: 0.25rem;
    background-color: var(--creamy-white-2);
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .common-layout-content {
    height: calc(100vh - 12.5rem);
    height: calc(100dvh - 12.5rem);
    background-color: var(--creamy-white-1);
    position: relative;
    overflow: hidden;
    width: 100%;
    display: flex;
    flex-direction: column;
    padding-top: 0.25rem;
  }
}

/* 弹幕控制按钮的过渡动画 */
.fade-slide-enter-active,
.fade-slide-leave-active {
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
}

.fade-slide-enter-from,
.fade-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}
</style>
