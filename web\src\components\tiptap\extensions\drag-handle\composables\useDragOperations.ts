import { ref, computed, watch, onUnmounted, type Ref } from 'vue'
import type { Editor } from '@tiptap/core'
import type { Node as ProseMirrorNode } from '@tiptap/pm/model'
import type { DragOperation, DragState, DropZone, NodePosition } from '../types'
import { DragEventManager } from '../DragEventManager'

export function useDragOperations(editor: Ref<Editor | undefined>) {
  const dragState = ref<DragState>({
    operation: null,
    dropZones: [],
    visualFeedback: {
      ghostImage: null,
      dropIndicators: [],
      dragPreview: null
    }
  })

  const dragEventManager = ref<DragEventManager | null>(null)
  const isInitialized = ref(false)

  // 计算属性
  const isDragging = computed(() => dragState.value.operation?.isActive ?? false)
  const currentDragElement = computed(() => dragState.value.operation?.dragElement ?? null)
  const hasValidDropZones = computed(() => dragState.value.dropZones.length > 0)

  // 初始化拖拽管理器
  const initializeDragManager = () => {
    if (!editor.value || isInitialized.value) return

    try {
      const view = (editor.value as any).view
      if (!view) return

      const options = editor.value.extensionManager.extensions
        .find(ext => ext.name === 'dragHandle')?.options

      if (options) {
        dragEventManager.value = new DragEventManager(editor.value, view, options)
        isInitialized.value = true
      }
    } catch (error) {
      console.error('Error initializing drag manager:', error)
    }
  }

  // 监听编辑器变化
  watch(editor, (newEditor) => {
    if (newEditor && !isInitialized.value) {
      // 延迟初始化以确保编辑器完全加载
      setTimeout(initializeDragManager, 100)
    }
  }, { immediate: true })

  /**
   * 初始化拖拽操作
   */
  const initiateDrag = (sourcePos: number, element: HTMLElement, event?: DragEvent): boolean => {
    if (!editor.value || !dragEventManager.value) {
      console.warn('Editor or drag manager not available')
      return false
    }

    try {
      // 获取源节点信息
      const nodePosition = getNodePosition(sourcePos, element)
      if (!nodePosition) {
        console.warn('Could not determine node position')
        return false
      }

      // 创建拖拽操作
      const operation: DragOperation = {
        sourceNode: nodePosition.node,
        sourcePos: nodePosition.pos,
        targetPos: null,
        dragElement: element,
        ghostImage: null,
        isActive: true,
        nodeType: nodePosition.node.type.name,
        preservedAttributes: {},
        nestingContext: {
          sourceParent: nodePosition.parent,
          sourceDepth: nodePosition.depth
        }
      }

      // 更新状态
      dragState.value.operation = operation
      
      // 如果有事件，使用事件管理器处理
      if (event && dragEventManager.value) {
        return dragEventManager.value.initiateDrag(element, event)
      }

      // 手动初始化（用于键盘操作）
      return initializeManualDrag(operation)
    } catch (error) {
      console.error('Error initiating drag:', error)
      return false
    }
  }

  /**
   * 手动初始化拖拽（键盘操作）
   */
  const initializeManualDrag = (operation: DragOperation): boolean => {
    try {
      // 创建拖放区域
      createDropZones()
      
      // 添加视觉反馈
      operation.dragElement.classList.add('dragging', 'keyboard-drag')
      
      // 触发事件
      editor.value?.emit('dragStart', { operation, manual: true })
      
      return true
    } catch (error) {
      console.error('Error in manual drag initialization:', error)
      return false
    }
  }

  /**
   * 更新拖放区域
   */
  const updateDropZones = (event?: DragEvent): void => {
    if (!isDragging.value || !editor.value) return

    try {
      // 清除现有拖放区域
      clearDropZones()
      
      // 重新创建拖放区域
      createDropZones()
      
      // 如果有事件，更新鼠标位置相关的反馈
      if (event) {
        updateMouseFeedback(event)
      }
    } catch (error) {
      console.error('Error updating drop zones:', error)
    }
  }

  /**
   * 创建拖放区域
   */
  const createDropZones = (): void => {
    if (!editor.value || !dragState.value.operation) return

    const view = (editor.value as any).view
    if (!view) return

    const dropZones: DropZone[] = []
    const { dom } = view
    
    // 获取所有可能的拖放目标
    const supportedSelectors = getSupportedSelectors()
    const elements = dom.querySelectorAll(supportedSelectors)

    elements.forEach((element) => {
      if (element instanceof HTMLElement && 
          element !== dragState.value.operation?.dragElement) {
        
        const dropZone = createDropZone(element, view)
        if (dropZone) {
          dropZones.push(dropZone)
        }
      }
    })

    dragState.value.dropZones = dropZones
  }

  /**
   * 创建单个拖放区域
   */
  const createDropZone = (element: HTMLElement, view: any): DropZone | null => {
    try {
      const pos = view.posAtDOM(element, 0)
      if (pos === null || pos === undefined) return null

      // 验证是否为有效的拖放目标
      const isValid = validateDropTarget(element, pos)
      
      // 创建视觉指示器
      const indicator = createDropIndicator(element)
      
      const targetNode = view.state.doc.nodeAt(pos)
      
      return {
        element,
        position: pos,
        isValid,
        visualIndicator: indicator,
        nodeType: targetNode?.type.name || 'unknown',
        canAcceptNodeTypes: [],
        nestingLevel: 0
      }
    } catch (error) {
      console.error('Error creating drop zone:', error)
      return null
    }
  }

  /**
   * 创建拖放指示器
   */
  const createDropIndicator = (element: HTMLElement): HTMLElement => {
    const indicator = document.createElement('div')
    indicator.className = 'drop-zone-indicator'
    indicator.style.cssText = `
      position: absolute;
      top: -2px;
      left: 0;
      right: 0;
      height: 4px;
      background: var(--color-primary, #3b82f6);
      border-radius: 2px;
      opacity: 0;
      transition: opacity 0.2s ease;
      z-index: 100;
      pointer-events: none;
    `

    element.style.position = 'relative'
    element.appendChild(indicator)
    
    // 添加到视觉反馈数组
    dragState.value.visualFeedback.dropIndicators.push(indicator)
    
    return indicator
  }

  /**
   * 验证拖放目标
   */
  const validateDropTarget = (element: HTMLElement, pos: number): boolean => {
    if (!editor.value || !dragState.value.operation) return false

    try {
      const view = (editor.value as any).view
      const targetNode = view.state.doc.nodeAt(pos)
      
      if (!targetNode) return false
      
      // 使用拖拽管理器验证
      if (dragEventManager.value) {
        return dragEventManager.value.validateDropTarget(element, new DragEvent('dragover'))
      }
      
      // 基本验证
      if (element === dragState.value.operation.dragElement) return false
      
      // 获取节点类型配置
      const dragHandleExt = editor.value.extensionManager.extensions
        .find(ext => ext.name === 'dragHandle')
      
      if (!dragHandleExt?.options?.nodeTypeConfigs) return true
      
      const sourceConfig = dragHandleExt.options.nodeTypeConfigs[dragState.value.operation.nodeType]
      const targetConfig = dragHandleExt.options.nodeTypeConfigs[targetNode.type.name]
      
      if (!sourceConfig || !targetConfig) return false
      
      // 检查节点类型兼容性
      if (!sourceConfig.canDragInto.includes(targetNode.type.name)) return false
      if (!targetConfig.canReceiveFrom.includes(dragState.value.operation.nodeType)) return false
      
      return true
    } catch (error) {
      console.error('Error validating drop target:', error)
      return false
    }
  }

  /**
   * 执行拖放操作
   */
  const executeDrop = (targetPos: number, targetElement?: HTMLElement): boolean => {
    if (!editor.value || !dragState.value.operation) {
      console.warn('No active drag operation')
      return false
    }

    try {
      const operation = dragState.value.operation
      
      // 使用拖拽管理器执行拖放
      if (dragEventManager.value && targetElement) {
        const success = dragEventManager.value.executeDrop(
          targetElement, 
          new DragEvent('drop')
        )
        
        if (success) {
          operation.targetPos = targetPos
          finalizeDrop(true)
        }
        
        return success
      }
      
      // 手动执行拖放
      return executeManualDrop(operation.sourcePos, targetPos)
    } catch (error) {
      console.error('Error executing drop:', error)
      finalizeDrop(false)
      return false
    }
  }

  /**
   * 手动执行拖放
   */
  const executeManualDrop = (sourcePos: number, targetPos: number): boolean => {
    if (!editor.value) return false

    try {
      const view = (editor.value as any).view
      const { state, dispatch } = view
      const { tr } = state

      // 获取源节点
      const sourceNode = state.doc.nodeAt(sourcePos)
      if (!sourceNode) return false

      // 计算调整后的目标位置
      let adjustedTargetPos = targetPos
      if (sourcePos < targetPos) {
        adjustedTargetPos -= sourceNode.nodeSize
      }

      // 创建事务
      const newTr = tr
        .delete(sourcePos, sourcePos + sourceNode.nodeSize)
        .insert(adjustedTargetPos, sourceNode)

      // 应用事务
      dispatch(newTr)
      
      finalizeDrop(true)
      return true
    } catch (error) {
      console.error('Error in manual drop execution:', error)
      finalizeDrop(false)
      return false
    }
  }

  /**
   * 完成拖放操作
   */
  const finalizeDrop = (success: boolean): void => {
    if (!dragState.value.operation) return

    try {
      const operation = dragState.value.operation
      
      // 触发完成事件
      editor.value?.emit(success ? 'dropSuccess' : 'dropError', {
        operation,
        success
      })
      
      // 执行动画
      if (success) {
        animateSuccessfulDrop(operation.dragElement)
      } else {
        animateCancelledDrop(operation.dragElement)
      }
      
      // 清理
      setTimeout(() => {
        cleanup()
      }, 200)
    } catch (error) {
      console.error('Error finalizing drop:', error)
      cleanup()
    }
  }

  /**
   * 取消拖拽操作
   */
  const cancelDrag = (): void => {
    if (!isDragging.value) return

    try {
      // 使用拖拽管理器取消
      if (dragEventManager.value) {
        dragEventManager.value.cancelDrag()
        return
      }
      
      // 手动取消
      if (dragState.value.operation) {
        animateCancelledDrop(dragState.value.operation.dragElement)
        
        editor.value?.emit('dragCancel', {
          operation: dragState.value.operation
        })
      }
      
      cleanup()
    } catch (error) {
      console.error('Error canceling drag:', error)
      cleanup()
    }
  }

  /**
   * 移动到上一个位置（键盘操作）
   */
  const moveUp = (): boolean => {
    return editor.value?.commands.moveParagraphUp() ?? false
  }

  /**
   * 移动到下一个位置（键盘操作）
   */
  const moveDown = (): boolean => {
    return editor.value?.commands.moveParagraphDown() ?? false
  }

  /**
   * 获取节点位置信息
   */
  const getNodePosition = (pos: number, element: HTMLElement): NodePosition | null => {
    if (!editor.value) return null

    try {
      const view = (editor.value as any).view
      const node = view.state.doc.nodeAt(pos)
      if (!node) return null

      const $pos = view.state.doc.resolve(pos)
      
      return {
        node,
        pos,
        depth: $pos.depth,
        parent: $pos.parent,
        index: $pos.index(),
        element
      }
    } catch (error) {
      console.error('Error getting node position:', error)
      return null
    }
  }

  /**
   * 获取支持的选择器
   */
  const getSupportedSelectors = (): string => {
    if (!editor.value) return ''
    
    const dragHandleExt = editor.value.extensionManager.extensions
      .find(ext => ext.name === 'dragHandle')
    
    if (!dragHandleExt?.options?.nodeTypeConfigs) {
      // Fallback to default selectors
      const supportedNodes = ['paragraph', 'heading', 'listItem', 'codeBlock']
      return supportedNodes.map(node => {
        switch (node) {
          case 'paragraph': return 'p'
          case 'heading': return 'h1, h2, h3, h4, h5, h6'
          case 'listItem': return 'li'
          case 'codeBlock': return '.code-block, pre'
          default: return `.${node}`
        }
      }).join(', ')
    }
    
    // Use configured selectors
    return Object.values(dragHandleExt.options.nodeTypeConfigs)
      .map((config: any) => config.selector)
      .join(', ')
  }

  /**
   * 更新鼠标反馈
   */
  const updateMouseFeedback = (event: DragEvent): void => {
    // 更新拖放区域的视觉反馈
    dragState.value.dropZones.forEach(zone => {
      if (zone.visualIndicator) {
        const rect = zone.element.getBoundingClientRect()
        const isOver = event.clientY >= rect.top && event.clientY <= rect.bottom &&
                      event.clientX >= rect.left && event.clientX <= rect.right
        
        zone.visualIndicator.style.opacity = isOver ? '1' : '0'
      }
    })
  }

  /**
   * 成功拖放动画
   */
  const animateSuccessfulDrop = (element: HTMLElement): void => {
    element.classList.add('drop-success')
    setTimeout(() => {
      element.classList.remove('drop-success')
    }, 300)
  }

  /**
   * 取消拖拽动画
   */
  const animateCancelledDrop = (element: HTMLElement): void => {
    element.classList.add('drag-cancelled')
    setTimeout(() => {
      element.classList.remove('drag-cancelled')
    }, 300)
  }

  /**
   * 清除拖放区域
   */
  const clearDropZones = (): void => {
    dragState.value.dropZones.forEach(zone => {
      if (zone.visualIndicator && zone.visualIndicator.parentNode) {
        zone.visualIndicator.parentNode.removeChild(zone.visualIndicator)
      }
      zone.element.classList.remove('drop-zone', 'drop-zone-active')
    })
    
    dragState.value.dropZones = []
    dragState.value.visualFeedback.dropIndicators = []
  }

  /**
   * 清理所有资源
   */
  const cleanup = (): void => {
    try {
      // 清除拖放区域
      clearDropZones()
      
      // 移除视觉反馈
      if (dragState.value.operation) {
        const element = dragState.value.operation.dragElement
        element.classList.remove('dragging', 'keyboard-drag', 'drop-success', 'drag-cancelled')
      }
      
      // 清除幽灵图像
      if (dragState.value.visualFeedback.ghostImage) {
        const ghost = dragState.value.visualFeedback.ghostImage
        if (ghost.parentNode) {
          ghost.parentNode.removeChild(ghost)
        }
      }
      
      // 重置状态
      dragState.value = {
        operation: null,
        dropZones: [],
        visualFeedback: {
          ghostImage: null,
          dropIndicators: [],
          dragPreview: null
        }
      }
    } catch (error) {
      console.error('Error during cleanup:', error)
    }
  }

  // 组件卸载时清理
  onUnmounted(() => {
    cleanup()
  })

  return {
    // 状态
    dragState,
    isDragging,
    currentDragElement,
    hasValidDropZones,
    
    // 操作方法
    initiateDrag,
    updateDropZones,
    executeDrop,
    cancelDrag,
    moveUp,
    moveDown,
    
    // 工具方法
    getNodePosition,
    validateDropTarget,
    cleanup
  }
}