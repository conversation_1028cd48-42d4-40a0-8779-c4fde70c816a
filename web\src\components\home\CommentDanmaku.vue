<template>
  <div class="comment-container">
    <Danmaku
      ref="danmakuRef"
      class="comment-danmaku"
      v-model:danmus="commentList"
      v-bind="danmakuConfig"
    >
      <template #dm="{ danmu }">
        <span class="comment-danmaku-item cursor-pointer" @dblclick="handleDanmuClick(danmu)">
          <span class="comment-danmaku-publisher">
            <NAvatar
              round
              :size="28"
              :src="danmu.publisherAvatar"
              object-fit="cover"
              :lazy="true"
            />
            <span>{{ danmu.publisher }}: </span>
          </span>
          <span class="comment-danmaku-content">
            <DanmakuRenderer
              :content="danmu.contentObj"
              @image-preview-open="handleImagePreviewOpen"
              @image-preview-close="handleImagePreviewClose"
            />
          </span>
        </span>
      </template>
    </Danmaku>
  </div>
</template>

<script lang="ts" setup>
import { NAvatar } from 'naive-ui'
import { ref, reactive, onMounted, onUnmounted, watch } from 'vue'

import fileApi from '@/api/file'
import Danmaku from '@/components/Danmaku.vue'
import DanmakuRenderer from '@/components/DanmakuRenderer.vue'
import { TOPIC_COMMENTS } from '@/constants/destination.constants'
import { COMMENT_RECEIVED } from '@/constants/frequency_key.constants'
import router from '@/router'
import type { Comment } from '@/types/comment.types'
import type { Notification } from '@/types/notification.types'
import frequencyLimit from '@/utils/frequency-limit'
import Json from '@/utils/json'
import logger from '@/utils/log'
import tiptap from '@/utils/tiptap'
import webSocket from '@/utils/web-socket'

const props = defineProps({
  searchCondition: {
    type: Object,
    required: true,
  },
  loop: {
    type: Boolean,
    default: false,
  },
  pause: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['search', 'update:loop', 'update:pause'])

// 评论列表数据
const commentList = ref<Comment[]>([])
const danmakuRef = ref()

// 弹幕配置
const danmakuConfig = reactive({
  isSuspend: true,
  useSlot: true,
  speeds: 160,
  debounce: 200,
  top: 10,
  right: 0,
  channels: 0,
  randomChannel: true,
  fontSize: 14,
  loop: false,
  pause: false,
  autoplay: false,
})

// 监听props变化更新配置
watch(
  () => props.loop,
  (newVal) => {
    danmakuConfig.loop = newVal
  },
)

watch(
  () => props.pause,
  (newVal) => {
    danmakuConfig.pause = newVal
    if (danmakuRef.value) {
      newVal ? danmakuRef.value.pause() : danmakuRef.value.play()
    }
  },
)

// 限制弹幕列表最大长度
const MAX_DANMAKU_COUNT = 200

// 清理弹幕列表
const clearDanmaku = () => {
  commentList.value = []
  if (danmakuRef.value) {
    danmakuRef.value.reset()
  }
}

// 处理弹幕点击
const handleDanmuClick = (danmu: Comment) => {
  // 使用 window.open 在新标签页中打开并自动切换过去
  const route = router.resolve({
    name: 'Article',
    params: { articleId: danmu.articleId, commentId: danmu.id },
  })
  const newWindow = window.open(route.href, '_blank')
  if (newWindow) {
    newWindow.focus()
  }
}

// 获取资源URL
const getResourceURL = (uri: string): string => {
  return fileApi.getResourceURL(uri)
}

// 添加评论到列表
const addCommentList = (list: Comment[]) => {
  // 用Set来跟踪已经显示的评论ID，避免重复
  const existingCommentIds = new Set(commentList.value.map((comment) => comment.id))

  // 过滤掉已经存在的评论
  const newComments = list.filter((comment) => !existingCommentIds.has(comment.id))

  // 如果没有新评论，直接返回
  if (newComments.length === 0) return

  try {
    const processedComments = newComments.map((comment) => ({
      ...comment,
      publisherAvatar: getResourceURL(comment.publisherAvatar || ''),
      // 确保内容字段存在，同时安全地处理JSON转换
      content: comment.content || '',
      contentObj: comment.content
        ? tiptap.toJsonObject(comment.content)
        : {
            type: 'doc',
            content: [{ type: 'paragraph', content: [] }],
          },
    }))

    // 分批添加弹幕，避免一次性添加太多造成卡顿
    const batchSize = 10
    for (let i = 0; i < processedComments.length; i += batchSize) {
      const batch = processedComments.slice(i, i + batchSize)
      setTimeout(() => {
        commentList.value.push(...batch)
      }, i * 50) // 每批次间隔50ms
    }

    // 限制列表长度
    if (commentList.value.length > MAX_DANMAKU_COUNT) {
      commentList.value = commentList.value.slice(-MAX_DANMAKU_COUNT)
    }

    // 确保danmakuRef存在
    if (danmakuRef.value) {
      danmakuRef.value.play()
    }
  } catch (error) {
    logger.error('处理评论列表时出错:', error)
  }
}

// 处理评论消息接收
const handleCommentMessageReceived = (newMessage: string) => {
  const messageObj = Json.parse(newMessage) as Notification
  if (!messageObj) return

  frequencyLimit.debounce(COMMENT_RECEIVED + messageObj.commentId, () => {
    // 检查评论是否已经存在
    const isExisting = commentList.value.some((comment) => comment.id === messageObj.commentId)
    if (isExisting) return

    // 检查必要字段是否存在
    if (!messageObj.commentId || !messageObj.articleId || !messageObj.publisher) {
      logger.warn('消息缺少必要字段:', messageObj)
      return
    }

    // 预处理消息内容
    try {
      // 确保消息内容是有效的JSON或字符串
      const processedMessage = {
        id: messageObj.commentId,
        articleId: messageObj.articleId,
        publisher: messageObj.publisher,
        publisherAvatar: getResourceURL(messageObj.publisherAvatar || ''),
        content: messageObj.content || '',
        // 只有当content存在且不为空时才调用toJsonObject
        contentObj: messageObj.content
          ? tiptap.toJsonObject(messageObj.content)
          : {
              type: 'doc',
              content: [{ type: 'paragraph', content: [] }],
            },
      } as Comment

      // 确保danmakuRef已初始化
      if (danmakuRef.value) {
        danmakuRef.value.insert(processedMessage)
      }
    } catch (error) {
      logger.error('处理弹幕消息时出错:', error)
    }
  })
}

// 订阅评论
const subscribeComment = () => {
  webSocket.subscribe(TOPIC_COMMENTS, handleCommentMessageReceived)
}

// 取消订阅评论
const unsubscribeComment = () => {
  webSocket.unsubscribe(TOPIC_COMMENTS)
}

// 处理图片预览开始事件，暂停弹幕
const handleImagePreviewOpen = () => {
  if (danmakuRef.value) {
    danmakuRef.value.pause()
  }
}

// 处理图片预览结束事件，恢复弹幕
const handleImagePreviewClose = () => {
  if (danmakuRef.value && !danmakuConfig.pause) {
    danmakuRef.value.play()
  }
}

// 调整大小
const resize = () => {
  if (danmakuRef.value) {
    danmakuRef.value.resize()
  }
}

// 弹幕播放控制
const play = () => {
  if (danmakuRef.value) {
    danmakuRef.value.play()
  }
}

const pause = () => {
  if (danmakuRef.value) {
    danmakuRef.value.pause()
  }
}

// 暴露方法给父组件
defineExpose({
  clearDanmaku,
  addCommentList,
  subscribeComment,
  unsubscribeComment,
  resize,
  play,
  pause,
})

onMounted(() => {
  subscribeComment()
})

onUnmounted(() => {
  unsubscribeComment()
})
</script>

<style scoped lang="scss">
.comment-container {
  height: calc(100vh - 10rem);
  height: calc(100dvh - 10rem);
  width: 100%;
  position: absolute;
  left: 0;
  overflow: hidden;

  .comment-danmaku {
    width: 100%;
    height: 100%;

    .comment-danmaku-item {
      display: flex;
      align-items: flex-end;
      width: fit-content;

      /* 发布者信息显示样式 */
      .comment-danmaku-publisher {
        display: flex;
        align-items: center;
        margin-bottom: 0.2rem;
        font-size: 1.2rem;
        color: var(--black);

        .n-avatar {
          margin-right: 4px;

          /* 确保发布者头像保持圆形不变形 */
          ::v-deep(.n-avatar__img) {
            object-fit: cover;
            aspect-ratio: 1 / 1;
          }
        }
      }

      /* 弹幕内容样式 */
      .comment-danmaku-content {
        max-width: 31rem;
        display: inline-block;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        font-size: 1.2rem;
        line-height: 1.5;
        vertical-align: bottom;
        margin-bottom: 0.25rem;
        color: var(--black);
      }
    }
  }
}
</style>
