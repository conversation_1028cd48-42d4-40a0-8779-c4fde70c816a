.bilibili-container {
  position: relative;
  width: 100%;
  margin: 1rem 0;
}

// 全局样式已经排除了 bilibili-container，这里不再需要覆盖

.bilibili-wrapper {
  position: relative;
  padding-top: 56.25%; /* 16:9 宽高比 */
  height: 0;
  overflow: hidden;
  border-radius: 0.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 10%);
  background-color: #f8f8f8;
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 15%);
  }

  iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border: none;
  }
}

// wrapper 样式保持原有的阴影效果

// 加载状态
.bilibili-loading {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgba(255, 255, 255, 80%);
  z-index: 1;

  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(0, 161, 214, 30%);
    border-radius: 50%;
    border-top-color: #00a1d6;
    animation: spin 1s ease-in-out infinite;
  }

  @keyframes spin {
    to {
      transform: rotate(360deg);
    }
  }
}

// 错误状态
.bilibili-error {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #f8f8f8;
  color: #666;
  padding: 1rem;
  text-align: center;

  .error-icon {
    font-size: 2rem;
    color: #ff6b6b;
    margin-bottom: 0.5rem;
  }

  .error-message {
    font-size: 0.9rem;
  }

  .retry-button {
    margin-top: 0.5rem;
    padding: 0.25rem 0.75rem;
    background-color: #00a1d6;
    color: white;
    border: none;
    border-radius: 0.25rem;
    cursor: pointer;
    font-size: 0.8rem;
    transition: background-color 0.2s;

    &:hover {
      background-color: #0091c2;
    }
  }
}

// 暗色模式
.dark-theme {
  .bilibili-wrapper {
    background-color: #2a2a2a;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 30%);

    &:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 40%);
    }
  }

  .bilibili-loading {
    background-color: rgba(42, 42, 42, 80%);
  }

  .bilibili-error {
    background-color: #2a2a2a;
    color: #bbb;
  }
}

// 暗色模式下不再需要选中状态覆盖，全局样式已经排除了 bilibili-container
