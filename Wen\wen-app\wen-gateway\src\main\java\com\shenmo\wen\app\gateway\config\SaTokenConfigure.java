package com.shenmo.wen.app.gateway.config;

import cn.dev33.satoken.config.SaTokenConfig;
import cn.dev33.satoken.context.SaHolder;
import cn.dev33.satoken.reactor.context.SaReactorSyncHolder;
import cn.dev33.satoken.reactor.filter.SaReactorFilter;
import cn.dev33.satoken.router.SaHttpMethod;
import cn.dev33.satoken.router.SaRouter;
import cn.dev33.satoken.stp.StpUtil;
import com.shenmo.wen.common.constant.RedisKeyConstant;
import com.shenmo.wen.common.exception.enumeration.BaseExceptionEnum;
import com.shenmo.wen.common.pojo.response.ResponseData;
import com.shenmo.wen.common.util.JacksonUtils;
import com.shenmo.wen.common.util.spring.SpringRedisUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.config.GlobalCorsProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.web.cors.CorsConfiguration;
import org.springframework.web.cors.reactive.CorsProcessor;
import org.springframework.web.cors.reactive.DefaultCorsProcessor;

/**
 * [Sa-Token 权限认证] 配置类
 *
 * <AUTHOR>
 */
@Slf4j
@Configuration
@RequiredArgsConstructor
public class SaTokenConfigure {

    private final GlobalCorsProperties globalCorsProperties;
    private final CorsProcessor corsProcessor = new DefaultCorsProcessor();
    private final SaTokenConfig saTokenConfig;

    @Bean
    public SaReactorFilter getSaReactorFilter() {
        return new SaReactorFilter()
                // 拦截地址
                .addInclude("/**")
                // 开放地址
                .addExclude("/favicon.ico")
                .setBeforeAuth(obj -> {
                    for (CorsConfiguration cors : globalCorsProperties.getCorsConfigurations().values()) {
                        corsProcessor.process(cors, SaReactorSyncHolder.getExchange());
                    }
                    // 如果是预检请求，则立即返回到前端
                    SaRouter.match(SaHttpMethod.OPTIONS)
                            .back();
                })
                // 鉴权方法：每次访问进入
                .setAuth(obj -> SaRouter.match("/**")
                        .notMatch("/authentication/login", "/authentication/register")
                        .check(r -> {
                            try {
                                StpUtil.checkLogin();
                            } catch (Exception e) {
                                StpUtil.logout();
                                final SetOperations<String, Object> set = SpringRedisUtils.forSet();
                                set.remove(RedisKeyConstant.USER_ONLINE, StpUtil.getLoginIdAsLong());
                            }
                            StpUtil.renewTimeout(saTokenConfig.getTimeout());
                        }))
                // 异常处理方法：每次setAuth函数出现异常时进入
                .setError(e -> {
                    final BaseExceptionEnum tokenNotFound = BaseExceptionEnum.TOKEN_NOT_FOUND;
                    SaHolder.getContext().getResponse().setStatus(tokenNotFound.getStatus().value());
                    return JacksonUtils.toJson(ResponseData.error(tokenNotFound.getCode(), tokenNotFound.getMessage()));
                });
    }
}
