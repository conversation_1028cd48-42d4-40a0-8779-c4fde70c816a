import { ref, computed, nextTick } from 'vue'

import articleApi from '@/api/article'
import { ArticlePublishedScope } from '@/constants/article_published_scope.constants'
import { ARTICLE_SUBMIT, ARTICLE_QUICK_SAVE } from '@/constants/frequency_key.constants'
import { type Article } from '@/types/article.types'
import { type ArticleForm } from '@/types/article_form.types'
import type { ResponseData } from '@/types/response_data.types'
import {
  validateEditorContent,
  checkLoadingState,
  setLoadingState,
} from '@/utils/editor-validation'
import frequencyLimit from '@/utils/frequency-limit'
import localStorage from '@/utils/local-storage'
import logger from '@/utils/log'
import message from '@/utils/message'
import tiptap from '@/utils/tiptap'

export function useArticleForm() {
  // 表单数据和引用
  const articleForm = ref<ArticleForm>(getInitArticleForm())
  const articleFormRef = ref()
  const articleTiptapEditorRef = ref()
  const shareUserSelectRef = ref()

  // 初始化表单数据
  function getInitArticleForm(): ArticleForm {
    return {
      id: '',
      title: '',
      tags: [],
      operationLevel: 0,
      publishedScope: ArticlePublishedScope.PERSONAL,
      shareUsers: [],
      contentObj: {},
    }
  }

  // 生成等级选项
  const generateCommentLevel = computed(() => {
    const optionsArray = []
    for (let i = 0; i <= localStorage.getLoginUser()?.level; i++) {
      optionsArray.push({
        label: 'Lv' + i,
        value: i,
      })
    }
    return optionsArray
  })

  // 重置表单
  const resetArticleForm = () => {
    articleForm.value = getInitArticleForm()
    shareUserSelectRef.value?.reset()
    // 清空 Tiptap 编辑器内容
    const editor = articleTiptapEditorRef.value?.editor
    if (editor) {
      editor.commands.clearContent(true) // true 表示同时触发更新事件
    }
  }

  // 设置表单数据（用于编辑模式）
  const setFormData = (articleData: Article) => {
    articleForm.value = {
      id: articleData.id,
      title: articleData.title,
      tags: articleData.tags,
      operationLevel: articleData.operationLevel,
      publishedScope: articleData.publishedScope,
      contentObj: articleData.contentObj,
      shareUsers: articleData.shareUsers || [],
    }
    logger.debug('edit article form: ', articleForm.value)
  }

  // 提交表单
  const submitArticleForm = (
    isEditingArticle: { value: boolean },
    submitLoading: { value: boolean },
    isArticleDialogVisible: { value: boolean },
    emit: (event: 'success') => void,
  ) => {
    if (submitLoading.value) {
      return false
    }
    frequencyLimit.debounce(
      ARTICLE_SUBMIT,
      () => {
        saveArticle(true, isEditingArticle, submitLoading, isArticleDialogVisible, emit) // 保存并关闭弹窗
      },
      300,
    )
    return false
  }

  // 快速保存方法（不关闭弹框）
  const quickSaveArticleForm = (
    isEditingArticle: { value: boolean },
    quickSaveLoading: { value: boolean },
    isArticleDialogVisible: { value: boolean },
    emit: (event: 'success') => void,
  ) => {
    if (quickSaveLoading.value) {
      return
    }
    frequencyLimit.debounce(
      ARTICLE_QUICK_SAVE,
      () => {
        saveArticle(false, isEditingArticle, quickSaveLoading, isArticleDialogVisible, emit) // 保存但不关闭弹窗
      },
      300,
    )
  }

  // 通用保存文章方法
  const saveArticle = (
    closeDialogAfterSave: boolean,
    isEditingArticle: { value: boolean },
    loadingRef: { value: boolean },
    isArticleDialogVisible: { value: boolean },
    emit: (event: 'success') => void,
  ) => {
    if (!articleFormRef.value?.validate()) {
      return
    }

    // 1. 检查是否正在保存中
    if (!checkLoadingState(loadingRef)) {
      return
    }

    // 2. 验证编辑器内容
    const editor = articleTiptapEditorRef.value?.editor
    const contentRef = { value: articleForm.value.contentObj }
    const validationResult = validateEditorContent(editor, contentRef, '文章内容不能为空哦~')

    if (!validationResult.isValid) {
      return
    }

    // 3. 设置loading状态并获取最新内容
    setLoadingState(loadingRef, true)
    const latestContent = editor.getJSON()

    // 构建保存参数
    const params = {
      title: articleForm.value.title,
      tag: articleForm.value.tags.join(','),
      operationLevel: articleForm.value.operationLevel,
      publishedScope: articleForm.value.publishedScope,
      content: tiptap.toJsonString(latestContent), // 使用编辑器的最新内容
      shareUserIds: articleForm.value.shareUsers.map((user) => user.id),
    }

    // 编辑模式需要添加id
    if (isEditingArticle.value && articleForm.value.id) {
      Object.assign(params, { id: articleForm.value.id })
    }

    // 根据模式选择API
    const apiMethod =
      isEditingArticle.value && articleForm.value.id ? articleApi.edit : articleApi.save

    apiMethod(params)
      .then((res: ResponseData) => {
        if (res?.success) {
          // 如果是新建文章，保存成功后立即更新状态
          if (!isEditingArticle.value && res.data) {
            isEditingArticle.value = true
            articleForm.value.id = res.data
            // 强制更新状态，确保下次保存时使用编辑模式
            nextTick(() => {
              logger.debug('Article state updated:', {
                isEditing: isEditingArticle.value,
                articleId: articleForm.value.id,
              })
            })
          }

          if (closeDialogAfterSave) {
            isArticleDialogVisible.value = false
            message.success(isEditingArticle.value ? '修改成功' : '创建成功')
          } else {
            message.success('保存成功')
          }
          emit('success')
        }
      })
      .catch((error) => {
        // 如果是新建文章失败，重置编辑状态
        if (!isEditingArticle.value) {
          isEditingArticle.value = false
          articleForm.value.id = ''
        }
        message.error(error.message || '保存失败')
      })
      .finally(() => {
        loadingRef.value = false
      })
  }

  return {
    // 状态
    articleForm,
    articleFormRef,
    articleTiptapEditorRef,
    shareUserSelectRef,
    generateCommentLevel,

    // 方法
    resetArticleForm,
    setFormData,
    submitArticleForm,
    quickSaveArticleForm,
  }
}
