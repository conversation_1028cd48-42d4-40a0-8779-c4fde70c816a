package com.shenmo.wen.app.core.interaction.pojo.param;

import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class WenInteractionParam {

    /**
     * 目标类型，用整数表示互动的目标对象类型，0代表评论，1代表文章，可为空（需根据实际业务规则判断赋值情况）。
     */
    @NotNull(message = "互动目标不可为空")
    private Integer targetType;

    /**
     * 目标ID，根据目标类型的不同，对应文章的ID或者评论的ID，用于准确关联到具体的互动目标，可为空（结合业务场景确定其合法性）。
     */
    @NotNull(message = "互动目标ID不可为空")
    private Long targetId;

    /**
     * 互动类型，用整数表示具体的互动行为，0表示点踩，1表示点赞，可为空（依据业务逻辑规范其取值情况）。
     */
    @NotNull(message = "互动类型不可为空")
    private Integer actionType;
}
