import { type Ref } from 'vue'

export function useSuspendEvents(dmContainer: Ref<HTMLDivElement>, emit: any) {
  /**
   * 初始化悬停事件
   */
  function initSuspendEvents() {
    let suspendDanmus: HTMLElement[] = []
    dmContainer.value.addEventListener('mouseover', (e) => {
      let target = e.target as EventTarget & HTMLElement

      if (!target.className.includes('dm')) {
        target = target.closest('.dm') || target
      }

      if (!target.className.includes('dm')) return

      if (suspendDanmus.includes(target)) return

      emit('dm-over', { el: target })
      target.classList.add('pause')

      suspendDanmus.push(target)
    })

    dmContainer.value.addEventListener('mouseout', (e) => {
      let target = e.target as EventTarget & HTMLElement

      if (!target.className.includes('dm')) {
        target = target.closest('.dm') || target
      }

      if (!target.className.includes('dm')) return
      emit('dm-out', { el: target })
      target.classList.remove('pause')

      // 容错处理
      suspendDanmus.forEach((item) => {
        item.classList.remove('pause')
      })
      suspendDanmus = []
    })
  }

  return {
    initSuspendEvents,
  }
}
