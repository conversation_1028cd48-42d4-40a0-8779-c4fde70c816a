package com.shenmo.wen.app.core.interaction.service.impl;

import cn.dev33.satoken.stp.StpUtil;

import com.shenmo.wen.app.core.article.exception.ArticleExceptionEnum;
import com.shenmo.wen.app.core.article.mapper.WenArticleMapper;
import com.shenmo.wen.app.core.article.pojo.entity.WenArticle;
import com.shenmo.wen.app.core.comment.mapper.WenCommentMapper;
import com.shenmo.wen.app.core.comment.pojo.entity.WenComment;
import com.shenmo.wen.app.core.interaction.mapper.WenInteractionMapper;
import com.shenmo.wen.app.core.interaction.pojo.entity.WenInteraction;
import com.shenmo.wen.app.core.interaction.pojo.param.WenInteractionParam;
import com.shenmo.wen.app.core.interaction.pojo.vo.WenInteractionCount;
import com.shenmo.wen.app.core.interaction.service.WenInteractionService;
import com.shenmo.wen.common.enumeration.InteractionActionEnum;
import com.shenmo.wen.common.enumeration.InteractionTargetEnum;
import com.shenmo.wen.common.util.AssertUtils;

import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
public class WenInteractionServiceImpl implements WenInteractionService {
    private final WenInteractionMapper mapper;
    private final WenArticleMapper articleMapper;
    private final WenCommentMapper commentMapper;

    @Override
    public WenInteractionCount save(WenInteractionParam param) {
        // 验证目标id
        final InteractionTargetEnum interactionTargetEnum = InteractionTargetEnum.of(param.getTargetType());
        final Long targetId = param.getTargetId();
        switch (interactionTargetEnum) {
            case ARTICLE ->
                    AssertUtils.isTrue(articleMapper.existsById(targetId), ArticleExceptionEnum.ARTICLE_NOT_EXISTS);
            case COMMENT ->
                    AssertUtils.isTrue(commentMapper.existsById(targetId), ArticleExceptionEnum.ARTICLE_NOT_EXISTS);
        }
        final int targetType = interactionTargetEnum.getCode();
        final Integer actionType = param.getActionType();
        final InteractionActionEnum interactionActionEnum = InteractionActionEnum.of(actionType);
        final long loginId = StpUtil.getLoginIdAsLong();
        final WenInteraction alreadyInteraction = mapper.targetById(loginId, targetType, targetId);
        final boolean notInteraction = Objects.isNull(alreadyInteraction);
        // 已互动并且互动类型一致
        final boolean alreadyAction = !notInteraction && alreadyInteraction.getActionType().equals(actionType);
        if (notInteraction) {
            final WenInteraction interaction = new WenInteraction();
            interaction.setUserId(loginId);
            interaction.setTargetType(targetType);
            interaction.setActionType(actionType);
            interaction.setTargetId(targetId);
            mapper.insert(interaction);
            // 新增互动数
            incrementInteractionCount(interactionTargetEnum, interactionActionEnum, targetId);
        } else {
            if (alreadyAction) {
                mapper.deleteById(alreadyInteraction.getId());
            } else {
                alreadyInteraction.setActionType(actionType);
                mapper.updateById(alreadyInteraction);
            }
            // 更新互动数
            updateInteractionCount(alreadyAction, interactionTargetEnum, interactionActionEnum, targetId);
        }
        return interactionCountById(alreadyAction, interactionTargetEnum, targetId);
    }

    private WenInteractionCount interactionCountById(boolean alreadyAction, InteractionTargetEnum interactionTargetEnum, Long id) {
        final WenInteractionCount interactionCount = new WenInteractionCount();
        switch (interactionTargetEnum) {
            case ARTICLE -> {
                final WenArticle article = articleMapper.likeAndDislikeCountById(id);
                interactionCount.setLikeCount(article.getLikeCount());
                interactionCount.setDislikeCount(article.getDislikeCount());
            }
            case COMMENT -> {
                final WenComment comment = commentMapper.likeAndDislikeCountById(id);
                interactionCount.setLikeCount(comment.getLikeCount());
                interactionCount.setDislikeCount(comment.getDislikeCount());
            }
        }
        interactionCount.setCancel(alreadyAction);
        return interactionCount;
    }

    private void incrementInteractionCount(InteractionTargetEnum interactionTargetEnum, InteractionActionEnum interactionActionEnum, Long targetId) {
        switch (interactionTargetEnum) {
            case ARTICLE -> {
                switch (interactionActionEnum) {
                    case LIKE -> articleMapper.incrementLikeCount(targetId);
                    case DISLIKE -> articleMapper.incrementDislikeCount(targetId);
                }
            }
            case COMMENT -> {
                switch (interactionActionEnum) {
                    case LIKE -> commentMapper.incrementLikeCount(targetId);
                    case DISLIKE -> commentMapper.incrementDislikeCount(targetId);
                }
            }
        }
    }

    private void updateInteractionCount(boolean alreadyActionType, InteractionTargetEnum interactionTargetEnum, InteractionActionEnum interactionActionEnum, Long targetId) {
        switch (interactionTargetEnum) {
            case ARTICLE -> {
                switch (interactionActionEnum) {
                    case LIKE -> {
                        if (alreadyActionType) {
                            articleMapper.decrementLikeCount(targetId);
                        } else {
                            articleMapper.incrementLikeCount(targetId);
                            articleMapper.decrementDislikeCount(targetId);
                        }
                    }
                    case DISLIKE -> {
                        if (alreadyActionType) {
                            articleMapper.decrementDislikeCount(targetId);
                        } else {
                            articleMapper.incrementDislikeCount(targetId);
                            articleMapper.decrementLikeCount(targetId);
                        }
                    }
                }
            }
            case COMMENT -> {
                switch (interactionActionEnum) {
                    case LIKE -> {
                        if (alreadyActionType) {
                            commentMapper.decrementLikeCount(targetId);
                        } else {
                            commentMapper.incrementLikeCount(targetId);
                            commentMapper.decrementDislikeCount(targetId);
                        }
                    }
                    case DISLIKE -> {
                        if (alreadyActionType) {
                            commentMapper.decrementDislikeCount(targetId);
                        } else {
                            commentMapper.incrementDislikeCount(targetId);
                            commentMapper.decrementLikeCount(targetId);
                        }
                    }
                }
            }
        }
    }
}
