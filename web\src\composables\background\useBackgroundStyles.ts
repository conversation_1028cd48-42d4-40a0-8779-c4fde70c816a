import { ref, computed } from 'vue'

import { activeTheme, ThemeType } from '@/utils/theme'

/**
 * 背景样式管理及主题切换逻辑
 * 抽取自BackgroundAnimation组件
 */
export function useBackgroundStyles(customProps?: {
  customLightGradient?: string
  customDarkGradient?: string
  zIndex?: number
}) {
  const isDarkTheme = computed(() => activeTheme.value === ThemeType.DARK)

  // 计算背景样式，支持自定义渐变
  const backgroundStyle = computed(() => {
    if (isDarkTheme.value) {
      return {
        background:
          customProps?.customDarkGradient ||
          'linear-gradient(to top, #0a0a0f, #121218 60%, #1c1c26 100%)',
        zIndex: customProps?.zIndex || 0,
      }
    } else {
      return {
        background:
          customProps?.customLightGradient ||
          'linear-gradient(to top, var(--creamy-white-3), var(--creamy-white-2) 70%, rgba(232, 240, 242, 0.8) 100%)',
        zIndex: customProps?.zIndex || 0,
      }
    }
  })

  return {
    isDarkTheme,
    backgroundStyle,
  }
}
