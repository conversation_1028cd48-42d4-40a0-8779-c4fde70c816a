import { computed } from 'vue'

/**
 * 创建双向绑定数据模型
 * 用于实现类似v-model的Props双向绑定功能
 *
 * @param props 组件属性对象
 * @param emit 事件发射函数
 * @param name 属性名称，默认为modelValue
 * @param translater 可选的数据转换函数
 * @returns 响应式计算属性
 */
export function createDanmuModelBinding<T>(
  props: any,
  emit: Function,
  name = 'modelValue',
  translater?: Function,
) {
  return computed<T>({
    get: () => props[name],
    set: (value) => {
      emit(`update:${name}`, translater ? translater(value) : value)
    },
  })
}
