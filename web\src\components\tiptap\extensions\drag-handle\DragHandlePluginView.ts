/**
 * Drag Handle Plugin View - Handles DOM interactions and visual feedback
 */
import { EditorView } from '@tiptap/pm/view'
import type { Editor } from '@tiptap/core'
import type { DragHandlePluginOptions, DragOperation, DropZone } from './types'
import { DragHandleManager } from './DragHandleManager'
import { DropZoneManager } from './DropZoneManager'
import { DragOperationManager } from './DragOperationManager'

export class DragHandlePluginView {
  private editor: Editor
  private options: DragHandlePluginOptions
  private view: EditorView
  private dragHandleManager: DragHandleManager
  private dropZoneManager: DropZoneManager
  private dragOperationManager: DragOperationManager

  constructor(view: EditorView, options: DragHandlePluginOptions) {
    this.view = view
    this.editor = options.editor
    this.options = options
    
    this.dragHandleManager = new DragHandleManager(view, options)
    this.dropZoneManager = new DropZoneManager(view, options)
    this.dragOperationManager = new DragOperationManager(view, options)
    
    this.init()
  }

  private init() {
    this.dragHandleManager.initialize()
    this.setupEventListeners()
  }

  private setupEventListeners() {
    // Delegate event handling to specialized managers
    this.dragHandleManager.onDragStart((operation) => {
      this.dragOperationManager.start(operation)
      this.dropZoneManager.show()
    })

    this.dragHandleManager.onDragEnd(() => {
      this.dragOperationManager.end()
      this.dropZoneManager.hide()
    })
  }

  destroy() {
    this.dragHandleManager.destroy()
    this.dropZoneManager.destroy()
    this.dragOperationManager.destroy()
  }
}