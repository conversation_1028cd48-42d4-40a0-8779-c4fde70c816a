package com.shenmo.wen.common.objectstorage.response;

import com.aliyun.oss.model.Bucket;
import org.springframework.lang.NonNull;

/**
 * ali桶响应
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class AliBucketResponse extends BucketResponse<Bucket> {

    /**
     * 构造方法
     *
     * @param origin 源对象
     * <AUTHOR>
     */
    public AliBucketResponse(Bucket origin) {
        super(origin);
    }

    /**
     * 获取桶名称
     *
     * @return 桶名称
     * <AUTHOR>
     */
    @NonNull
    @Override
    public String getName() {

        return origin.getName();
    }
}
