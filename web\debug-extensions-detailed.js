// Enhanced debug script for TipTap extensions
console.log('🔍 Starting detailed extension debugging...');

// Function to check if extensions are properly loaded
function checkExtensionLoading() {
    console.log('📦 Checking extension loading...');
    
    // Check if styles are loaded
    const styleSheets = Array.from(document.styleSheets);
    let foundClickMenuStyles = false;
    let foundDragHandleStyles = false;
    let styleDetails = [];
    
    styleSheets.forEach((sheet, index) => {
        try {
            const rules = Array.from(sheet.cssRules || []);
            let clickMenuRules = 0;
            let dragHandleRules = 0;
            
            rules.forEach(rule => {
                if (rule.selectorText) {
                    if (rule.selectorText.includes('.click-menu')) {
                        foundClickMenuStyles = true;
                        clickMenuRules++;
                    }
                    if (rule.selectorText.includes('.drag-handle')) {
                        foundDragHandleStyles = true;
                        dragHandleRules++;
                    }
                }
            });
            
            if (clickMenuRules > 0 || dragHandleRules > 0) {
                styleDetails.push({
                    sheet: index,
                    href: sheet.href || 'inline',
                    clickMenuRules,
                    dragHandleRules
                });
            }
        } catch (e) {
            // Cross-origin stylesheet, skip
        }
    });
    
    console.log(`🎨 Click menu styles loaded: ${foundClickMenuStyles}`);
    console.log(`🎨 Drag handle styles loaded: ${foundDragHandleStyles}`);
    console.log('📋 Style details:', styleDetails);
    
    return { foundClickMenuStyles, foundDragHandleStyles, styleDetails };
}

// Function to check editor state
function checkEditorState() {
    console.log('📝 Checking editor state...');
    
    const editor = document.querySelector('.ProseMirror');
    if (!editor) {
        console.error('❌ No ProseMirror editor found');
        return null;
    }
    
    console.log('✅ Found ProseMirror editor:', editor);
    
    // Check editor parent for positioning
    const parent = editor.parentElement;
    if (parent) {
        const parentStyle = getComputedStyle(parent);
        console.log('📐 Editor parent position:', parentStyle.position);
        console.log('📐 Editor parent dimensions:', {
            width: parentStyle.width,
            height: parentStyle.height
        });
    }
    
    // Check editor content
    const content = editor.innerHTML;
    console.log('📄 Editor content length:', content.length);
    console.log('📄 Editor content preview:', content.substring(0, 200) + '...');
    
    // Add test content if needed
    if (content.trim() === '' || content === '<p></p>' || content === '<p><br></p>') {
        console.log('📝 Adding test content...');
        editor.innerHTML = `
            <p>Test paragraph 1 - hover to see click menu</p>
            <p>Test paragraph 2 - should show drag handle</p>
            <h2>Test heading - should also show controls</h2>
            <p>Test paragraph 3 - try dragging this</p>
            <blockquote><p>Test blockquote - should work too</p></blockquote>
        `;
    }
    
    return editor;
}

// Function to simulate user interactions
function simulateInteractions(editor) {
    console.log('🧪 Simulating user interactions...');
    
    const blockElements = editor.querySelectorAll('p, h1, h2, h3, h4, h5, h6, blockquote');
    console.log(`📊 Found ${blockElements.length} block elements`);
    
    if (blockElements.length === 0) {
        console.warn('⚠️ No block elements found to test');
        return;
    }
    
    blockElements.forEach((element, index) => {
        setTimeout(() => {
            console.log(`🎯 Testing element ${index + 1}: ${element.tagName}`);
            
            // Get element position
            const rect = element.getBoundingClientRect();
            const editorRect = editor.getBoundingClientRect();
            
            console.log(`📍 Element position:`, {
                left: rect.left,
                top: rect.top,
                width: rect.width,
                height: rect.height
            });
            
            // Simulate mousemove event at different positions
            const positions = [
                { x: rect.left - 30, y: rect.top + rect.height / 2, desc: 'left side' },
                { x: rect.left - 10, y: rect.top + rect.height / 2, desc: 'near left' },
                { x: rect.left + 10, y: rect.top + rect.height / 2, desc: 'inside element' }
            ];
            
            positions.forEach((pos, posIndex) => {
                setTimeout(() => {
                    console.log(`🎯 Testing position ${posIndex + 1} (${pos.desc}):`, { x: pos.x, y: pos.y });
                    
                    const mouseMoveEvent = new MouseEvent('mousemove', {
                        bubbles: true,
                        cancelable: true,
                        clientX: pos.x,
                        clientY: pos.y,
                        view: window
                    });
                    
                    // Dispatch on both the element and the editor
                    element.dispatchEvent(mouseMoveEvent);
                    editor.dispatchEvent(mouseMoveEvent);
                    
                    // Check for extension elements after a delay
                    setTimeout(() => {
                        checkExtensionElements(index, posIndex, pos.desc);
                    }, 150);
                    
                }, posIndex * 200);
            });
            
        }, index * 1000);
    });
}

// Function to check for extension elements
function checkExtensionElements(elementIndex, posIndex, posDesc) {
    const clickMenus = document.querySelectorAll('.click-menu');
    const dragHandles = document.querySelectorAll('.drag-handle');
    
    console.log(`📊 Element ${elementIndex + 1}, Position ${posIndex + 1} (${posDesc}):`);
    console.log(`   Click menus: ${clickMenus.length}, Drag handles: ${dragHandles.length}`);
    
    // Check visibility
    const visibleClickMenus = Array.from(clickMenus).filter(el => {
        const style = getComputedStyle(el);
        return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
    });
    
    const visibleDragHandles = Array.from(dragHandles).filter(el => {
        const style = getComputedStyle(el);
        return style.display !== 'none' && style.visibility !== 'hidden' && style.opacity !== '0';
    });
    
    console.log(`👁️ Visible - Click menus: ${visibleClickMenus.length}, Drag handles: ${visibleDragHandles.length}`);
    
    if (visibleClickMenus.length > 0) {
        console.log('🎉 Click menu is working!', visibleClickMenus[0]);
        console.log('📍 Click menu position:', {
            left: visibleClickMenus[0].style.left,
            top: visibleClickMenus[0].style.top,
            display: visibleClickMenus[0].style.display
        });
    }
    
    if (visibleDragHandles.length > 0) {
        console.log('🎉 Drag handle is working!', visibleDragHandles[0]);
        console.log('📍 Drag handle position:', {
            left: visibleDragHandles[0].style.left,
            top: visibleDragHandles[0].style.top,
            display: visibleDragHandles[0].style.display
        });
    }
    
    // Log all extension elements for debugging
    if (clickMenus.length > 0) {
        clickMenus.forEach((menu, i) => {
            console.log(`🔍 Click menu ${i}:`, {
                element: menu,
                style: menu.style.cssText,
                computed: {
                    display: getComputedStyle(menu).display,
                    visibility: getComputedStyle(menu).visibility,
                    opacity: getComputedStyle(menu).opacity
                }
            });
        });
    }
    
    if (dragHandles.length > 0) {
        dragHandles.forEach((handle, i) => {
            console.log(`🔍 Drag handle ${i}:`, {
                element: handle,
                style: handle.style.cssText,
                computed: {
                    display: getComputedStyle(handle).display,
                    visibility: getComputedStyle(handle).visibility,
                    opacity: getComputedStyle(handle).opacity
                }
            });
        });
    }
}

// Function to check for JavaScript errors
function setupErrorTracking() {
    console.log('🔍 Setting up error tracking...');
    
    // Track console errors
    const originalError = console.error;
    console.error = function(...args) {
        console.log('🚨 Console error caught:', ...args);
        originalError.apply(console, args);
    };
    
    // Track unhandled promise rejections
    window.addEventListener('unhandledrejection', event => {
        console.log('🚨 Unhandled promise rejection:', event.reason);
    });
    
    // Track general errors
    window.addEventListener('error', event => {
        console.log('🚨 JavaScript error:', {
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            error: event.error
        });
    });
}

// Main execution
function runDebugTests() {
    console.log('🚀 Starting comprehensive extension debugging...');
    
    setupErrorTracking();
    
    setTimeout(() => {
        const styleCheck = checkExtensionLoading();
        const editor = checkEditorState();
        
        if (editor) {
            simulateInteractions(editor);
            
            // Final summary
            setTimeout(() => {
                console.log('📋 Final Test Summary:');
                console.log('✅ Styles loaded:', styleCheck.foundClickMenuStyles && styleCheck.foundDragHandleStyles);
                console.log('✅ Editor found:', !!editor);
                
                const allExtensionElements = document.querySelectorAll('.click-menu, .drag-handle');
                console.log('📊 Total extension elements:', allExtensionElements.length);
                
                if (!styleCheck.foundClickMenuStyles || !styleCheck.foundDragHandleStyles) {
                    console.log('💡 Issue: Extension styles not loaded properly');
                    console.log('💡 Check: Are the SCSS files being imported in the extension index files?');
                }
                
                if (allExtensionElements.length === 0) {
                    console.log('💡 Issue: No extension DOM elements created');
                    console.log('💡 Check: Are the extensions properly registered and initialized?');
                    console.log('💡 Check: Are there any JavaScript errors preventing extension creation?');
                }
                
                console.log('🏁 Debug testing complete!');
            }, 8000);
        }
    }, 1000);
}

// Auto-run when script loads
runDebugTests();
