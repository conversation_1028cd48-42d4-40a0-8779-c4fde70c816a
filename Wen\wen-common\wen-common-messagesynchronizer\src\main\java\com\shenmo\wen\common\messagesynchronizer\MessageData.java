package com.shenmo.wen.common.messagesynchronizer;

import com.fasterxml.jackson.annotation.JsonIgnore;

import java.util.Objects;

/**
 * 消息数据
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface MessageData<T> {

    /**
     * 获取消息唯一id
     *
     * @return 消息唯一id
     * <AUTHOR>
     */
    String getId();

    /**
     * 获取消息类型
     *
     * @return 消息类型
     * <AUTHOR>
     */
    T getType();

    /**
     * 获取消息具体数据
     *
     * @return 消息具体数据
     * <AUTHOR>
     */
    Object getData();

    /**
     * 判定当前消息是否为空
     *
     * @return 当前消息是否为空
     * <AUTHOR>
     */
    @JsonIgnore
    default boolean isEmpty() {

        return Objects.isNull(getData());
    }
}
