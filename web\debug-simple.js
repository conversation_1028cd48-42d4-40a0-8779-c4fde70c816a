// Simple debug script - paste this in browser console
console.log('=== Simple Extension Debug ===');

// Check if editor exists
const editor = document.querySelector('.ProseMirror');
console.log('Editor found:', !!editor);

// Check for drag handles
const dragHandles = document.querySelectorAll('.drag-handle');
console.log('Drag handles:', dragHandles.length);

// Check for click menus
const clickMenus = document.querySelectorAll('.click-menu');
console.log('Click menus:', clickMenus.length);

// Test mousemove event
if (editor) {
  const firstP = editor.querySelector('p');
  if (firstP) {
    console.log('Testing mousemove on first paragraph...');
    
    const rect = firstP.getBoundingClientRect();
    const event = new MouseEvent('mousemove', {
      bubbles: true,
      clientX: rect.left - 20,
      clientY: rect.top + 10
    });
    
    document.dispatchEvent(event);
    
    setTimeout(() => {
      const newDragHandles = document.querySelectorAll('.drag-handle');
      const newClickMenus = document.querySelectorAll('.click-menu');
      console.log('After mousemove - Drag handles:', newDragHandles.length);
      console.log('After mousemove - Click menus:', newClickMenus.length);
      
      // Check if any are visible
      const visibleDragHandles = Array.from(newDragHandles).filter(el => 
        el.style.display !== 'none' && 
        getComputedStyle(el).display !== 'none'
      );
      const visibleClickMenus = Array.from(newClickMenus).filter(el => 
        el.style.display !== 'none' && 
        getComputedStyle(el).display !== 'none'
      );
      
      console.log('Visible drag handles:', visibleDragHandles.length);
      console.log('Visible click menus:', visibleClickMenus.length);
      
      if (visibleDragHandles.length > 0) {
        console.log('✅ Drag handle is working!');
      } else {
        console.log('❌ Drag handle not visible');
      }
      
      if (visibleClickMenus.length > 0) {
        console.log('✅ Click menu is working!');
      } else {
        console.log('❌ Click menu not visible');
      }
    }, 200);
  }
}

// Check for console errors
const errors = [];
const originalError = console.error;
console.error = function(...args) {
  errors.push(args.join(' '));
  originalError.apply(console, arguments);
};

setTimeout(() => {
  if (errors.length > 0) {
    console.log('Console errors found:');
    errors.forEach(error => console.log('❌', error));
  } else {
    console.log('✅ No console errors');
  }
}, 1000);
