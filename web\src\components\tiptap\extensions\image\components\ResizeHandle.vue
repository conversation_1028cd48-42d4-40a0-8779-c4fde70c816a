<template>
  <div
    :class="handleClasses"
    :style="handleStyles"
    :data-handle-position="position"
    contenteditable="false"
    @mousedown="onMouseDown"
    @touchstart="onTouchStart"
  />
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  position: string
  isMobileDevice: boolean
  visible: boolean
}

interface Emits {
  (e: 'resize-start', event: MouseEvent | TouchEvent, position: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

/**
 * 计算控制点的 CSS 类
 */
const handleClasses = computed(() => {
  const isMiddleHandle = ['top', 'right', 'bottom', 'left'].includes(props.position)
  return [
    'resize-handle',
    `handle-${props.position}`,
    {
      'middle-handle': isMiddleHandle,
      'corner-handle': !isMiddleHandle,
      // 移除移动设备特殊样式，统一使用桌面样式
    },
  ]
})

/**
 * 计算控制点的样式
 */
const handleStyles = computed(() => {
  const isMiddleHandle = ['top', 'right', 'bottom', 'left'].includes(props.position)
  // 统一使用桌面设备的控制点位置，避免不一致问题
  const handleOffset = -4
  const centerHandleOffset = -2

  const baseStyles: Record<string, string> = {
    position: 'absolute',
    zIndex: '9999',
    boxShadow: '0 0 5px rgba(0, 0, 0, 0.5)',
    pointerEvents: 'all',
    touchAction: 'none',
    display: props.visible ? 'block' : 'none',
    visibility: props.visible ? 'visible' : 'hidden',
    opacity: props.visible ? '1' : '0',
  }

  // 设置尺寸和样式
  if (isMiddleHandle) {
    Object.assign(baseStyles, {
      borderRadius: '2px',
      backgroundColor: '#2d8cf0',
      border: '1px solid white',
    })

    if (props.position === 'top' || props.position === 'bottom') {
      Object.assign(baseStyles, {
        width: '12px',
        height: '4px',
      })
    } else {
      Object.assign(baseStyles, {
        width: '4px',
        height: '12px',
      })
    }
  } else {
    Object.assign(baseStyles, {
      width: '8px',
      height: '8px',
      borderRadius: '50%',
      backgroundColor: '#2d8cf0',
      border: '1px solid white',
    })
  }

  // 设置位置
  switch (props.position) {
    case 'top-left':
      Object.assign(baseStyles, {
        top: `${handleOffset}px`,
        left: `${handleOffset}px`,
        cursor: 'nwse-resize',
      })
      break
    case 'top-right':
      Object.assign(baseStyles, {
        top: `${handleOffset}px`,
        right: `${handleOffset}px`,
        cursor: 'nesw-resize',
      })
      break
    case 'bottom-left':
      Object.assign(baseStyles, {
        bottom: `${handleOffset}px`,
        left: `${handleOffset}px`,
        cursor: 'nesw-resize',
      })
      break
    case 'bottom-right':
      Object.assign(baseStyles, {
        bottom: `${handleOffset}px`,
        right: `${handleOffset}px`,
        cursor: 'nwse-resize',
      })
      break
    case 'top':
      Object.assign(baseStyles, {
        top: `${centerHandleOffset}px`,
        left: 'calc(50% - 6px)',
        cursor: 'ns-resize',
      })
      break
    case 'right':
      Object.assign(baseStyles, {
        right: `${centerHandleOffset}px`,
        top: 'calc(50% - 6px)',
        cursor: 'ew-resize',
      })
      break
    case 'bottom':
      Object.assign(baseStyles, {
        bottom: `${centerHandleOffset}px`,
        left: 'calc(50% - 6px)',
        cursor: 'ns-resize',
      })
      break
    case 'left':
      Object.assign(baseStyles, {
        left: `${centerHandleOffset}px`,
        top: 'calc(50% - 6px)',
        cursor: 'ew-resize',
      })
      break
  }

  return baseStyles
})

/**
 * 处理鼠标按下事件
 */
const onMouseDown = (event: MouseEvent) => {
  event.preventDefault()
  event.stopPropagation()
  emit('resize-start', event, props.position)
}

/**
 * 处理触摸开始事件
 */
const onTouchStart = (event: TouchEvent) => {
  event.preventDefault()
  event.stopPropagation()
  emit('resize-start', event, props.position)
}
</script>

<style scoped>
.resize-handle {
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

/* 移动设备相关样式已移除，统一使用桌面样式 */
</style>
