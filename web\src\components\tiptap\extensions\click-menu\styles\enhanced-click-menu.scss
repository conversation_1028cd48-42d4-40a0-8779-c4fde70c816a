/* Enhanced <PERSON>lick Menu Styles - Based on tiptap-starter-kit */

.ProseMirror-cm {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 998;
}

.ProseMirror-cm-plus,
.ProseMirror-cm-drag {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  border-radius: 4px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.15s ease;

  &:hover {
    background: #f3f4f6;
    color: #374151;
  }

  &:active {
    background: #e5e7eb;
  }
}

.ProseMirror-cm-drag {
  cursor: grab;

  &:active {
    cursor: grabbing;
  }

  &[draggable="true"] {
    cursor: grab;
  }
}

.ProseMirror-cm-menu {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 8px;
  min-width: 200px;
  max-width: 300px;
  z-index: 999;
}

.ProseMirror-icon {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
}

.ProseMirror-icon-plus::before {
  content: "➕";
  font-size: 12px;
}

.ProseMirror-icon-drag::before {
  content: "⋮⋮";
  font-size: 12px;
  letter-spacing: -2px;
}

/* Block Menu Styles */
.ProseMirror-bm {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 8px;
  max-height: 400px;
  overflow-y: auto;
  min-width: 280px;
  z-index: 1000;
}

.ProseMirror-bm-button {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: none;
  border-radius: 6px;
  cursor: pointer;
  text-align: left;
  gap: 12px;
  transition: background-color 0.15s ease;

  &:hover,
  &[data-active="true"] {
    background: #f3f4f6;
  }

  &:active {
    background: #e5e7eb;
  }
}

.ProseMirror-bm-button-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  color: #6b7280;
}

.ProseMirror-bm-button-name {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

.ProseMirror-bm-button-shortcut {
  font-size: 12px;
  color: #9ca3af;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

.ProseMirror-bm-divider {
  display: block;
  height: 1px;
  background: #e5e7eb;
  margin: 8px 0;
}

.ProseMirror-bm-empty {
  padding: 16px;
  text-align: center;
  color: #9ca3af;
  font-size: 14px;
}

.ProseMirror-bm-placeholder {
  position: relative;

  &::before {
    content: attr(data-empty);
    position: absolute;
    color: #9ca3af;
    pointer-events: none;
    font-style: italic;
  }
}

/* Tippy.js theme overrides */
.tippy-box[data-theme~="ProseMirror"] {
  background: transparent;
  border: none;
  box-shadow: none;
}

.tippy-box[data-theme~="ProseMirror-none"] {
  background: transparent;
  border: none;
  box-shadow: none;
}

/* Animation improvements */
.tippy-box[data-animation="shift-away"][data-state="hidden"] {
  opacity: 0;
  transform: translateY(-10px);
}

.tippy-box[data-animation="shift-away"][data-state="visible"] {
  opacity: 1;
  transform: translateY(0);
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .ProseMirror-bm {
    min-width: 240px;
    max-height: 300px;
  }

  .ProseMirror-cm-menu {
    min-width: 180px;
    max-width: 250px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .ProseMirror-cm {
    background: #1f2937;
    border-color: #374151;
  }

  .ProseMirror-cm-plus,
  .ProseMirror-cm-drag {
    color: #9ca3af;

    &:hover {
      background: #374151;
      color: #d1d5db;
    }

    &:active {
      background: #4b5563;
    }
  }

  .ProseMirror-cm-menu,
  .ProseMirror-bm {
    background: #1f2937;
    border-color: #374151;
  }

  .ProseMirror-bm-button {
    &:hover,
    &[data-active="true"] {
      background: #374151;
    }

    &:active {
      background: #4b5563;
    }
  }

  .ProseMirror-bm-button-name {
    color: #d1d5db;
  }

  .ProseMirror-bm-button-icon {
    color: #9ca3af;
  }

  .ProseMirror-bm-button-shortcut {
    color: #6b7280;
  }

  .ProseMirror-bm-divider {
    background: #374151;
  }

  .ProseMirror-bm-empty {
    color: #6b7280;
  }

  .ProseMirror-bm-placeholder::before {
    color: #6b7280;
  }
}
