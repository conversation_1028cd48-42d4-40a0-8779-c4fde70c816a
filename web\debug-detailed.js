// Detailed debug script - paste this in browser console
console.log('=== Detailed Extension Debug ===');

// Function to wait for Vue app to be ready
function waitForVueApp() {
  return new Promise((resolve) => {
    const checkApp = () => {
      const app = document.querySelector('#app');
      if (app && app.__vueParentComponent) {
        resolve(app);
      } else {
        setTimeout(checkApp, 100);
      }
    };
    checkApp();
  });
}

// Function to wait for editor to be ready
function waitForEditor() {
  return new Promise((resolve) => {
    const checkEditor = () => {
      const editor = document.querySelector('.ProseMirror');
      if (editor) {
        resolve(editor);
      } else {
        setTimeout(checkEditor, 100);
      }
    };
    checkEditor();
  });
}

// Main debug function
async function debugExtensions() {
  console.log('Waiting for Vue app...');
  await waitForVueApp();
  console.log('✅ Vue app ready');

  console.log('Waiting for editor...');
  const editor = await waitForEditor();
  console.log('✅ Editor ready');

  // Check editor structure
  console.log('Editor classes:', editor.className);
  console.log('Editor parent:', editor.parentElement?.className);

  // Check for extension containers
  const editorContainer = editor.parentElement;
  if (editorContainer) {
    console.log('Editor container position:', getComputedStyle(editorContainer).position);
    
    // Look for drag handles and click menus
    const dragHandles = editorContainer.querySelectorAll('.drag-handle');
    const clickMenus = editorContainer.querySelectorAll('.click-menu');
    
    console.log('Drag handles in container:', dragHandles.length);
    console.log('Click menus in container:', clickMenus.length);
    
    // Check if they're attached
    dragHandles.forEach((handle, i) => {
      console.log(`Drag handle ${i}:`, {
        display: handle.style.display,
        position: handle.style.position,
        parent: handle.parentElement?.className
      });
    });
    
    clickMenus.forEach((menu, i) => {
      console.log(`Click menu ${i}:`, {
        display: menu.style.display,
        position: menu.style.position,
        parent: menu.parentElement?.className
      });
    });
  }

  // Test mousemove event
  const firstP = editor.querySelector('p');
  if (firstP) {
    console.log('Testing mousemove on first paragraph...');
    
    const rect = firstP.getBoundingClientRect();
    const editorRect = editor.getBoundingClientRect();
    
    console.log('Paragraph rect:', rect);
    console.log('Editor rect:', editorRect);
    
    // Create mousemove event
    const event = new MouseEvent('mousemove', {
      bubbles: true,
      clientX: rect.left - 20,
      clientY: rect.top + 10,
      view: window
    });
    
    // Dispatch on document (as our extensions listen to document)
    document.dispatchEvent(event);
    
    // Also try dispatching on editor
    editor.dispatchEvent(event);
    
    setTimeout(() => {
      console.log('Checking after mousemove...');
      
      const newDragHandles = document.querySelectorAll('.drag-handle');
      const newClickMenus = document.querySelectorAll('.click-menu');
      
      console.log('Total drag handles:', newDragHandles.length);
      console.log('Total click menus:', newClickMenus.length);
      
      // Check visibility
      newDragHandles.forEach((handle, i) => {
        const computed = getComputedStyle(handle);
        console.log(`Drag handle ${i} visibility:`, {
          display: handle.style.display,
          computedDisplay: computed.display,
          opacity: computed.opacity,
          visibility: computed.visibility,
          zIndex: computed.zIndex
        });
      });
      
      newClickMenus.forEach((menu, i) => {
        const computed = getComputedStyle(menu);
        console.log(`Click menu ${i} visibility:`, {
          display: menu.style.display,
          computedDisplay: computed.display,
          opacity: computed.opacity,
          visibility: computed.visibility,
          zIndex: computed.zIndex
        });
      });
      
      // Check if any are visible
      const visibleDragHandles = Array.from(newDragHandles).filter(el => {
        const computed = getComputedStyle(el);
        return el.style.display !== 'none' && 
               computed.display !== 'none' &&
               computed.visibility !== 'hidden' &&
               computed.opacity !== '0';
      });
      
      const visibleClickMenus = Array.from(newClickMenus).filter(el => {
        const computed = getComputedStyle(el);
        return el.style.display !== 'none' && 
               computed.display !== 'none' &&
               computed.visibility !== 'hidden' &&
               computed.opacity !== '0';
      });
      
      console.log('Visible drag handles:', visibleDragHandles.length);
      console.log('Visible click menus:', visibleClickMenus.length);
      
      if (visibleDragHandles.length > 0) {
        console.log('✅ Drag handle is working!');
        visibleDragHandles.forEach((handle, i) => {
          console.log(`Visible drag handle ${i} position:`, {
            left: handle.style.left,
            top: handle.style.top,
            rect: handle.getBoundingClientRect()
          });
        });
      } else {
        console.log('❌ Drag handle not visible');
      }
      
      if (visibleClickMenus.length > 0) {
        console.log('✅ Click menu is working!');
        visibleClickMenus.forEach((menu, i) => {
          console.log(`Visible click menu ${i} position:`, {
            left: menu.style.left,
            top: menu.style.top,
            rect: menu.getBoundingClientRect()
          });
        });
      } else {
        console.log('❌ Click menu not visible');
      }
    }, 300);
  } else {
    console.log('❌ No paragraphs found in editor');
  }
}

// Check for console errors
const errors = [];
const originalError = console.error;
console.error = function(...args) {
  errors.push(args.join(' '));
  originalError.apply(console, arguments);
};

// Run the debug
debugExtensions().catch(console.error);

// Check errors after a delay
setTimeout(() => {
  if (errors.length > 0) {
    console.log('Console errors found:');
    errors.forEach(error => console.log('❌', error));
  } else {
    console.log('✅ No console errors');
  }
}, 2000);
