import type { Editor } from '@tiptap/core'
import type { Node as ProseMirrorNode } from '@tiptap/pm/model'

/**
 * Keyboard accessibility utilities for drag and click menu operations
 */
export class KeyboardAccessibilityUtils {
  private static announcements: HTMLElement | null = null

  /**
   * Initialize screen reader announcements
   */
  static initializeAnnouncements(): void {
    if (this.announcements) return

    this.announcements = document.createElement('div')
    this.announcements.setAttribute('aria-live', 'polite')
    this.announcements.setAttribute('aria-atomic', 'true')
    this.announcements.setAttribute('role', 'status')
    this.announcements.style.cssText = `
      position: absolute;
      left: -10000px;
      width: 1px;
      height: 1px;
      overflow: hidden;
    `
    document.body.appendChild(this.announcements)
  }

  /**
   * Announce message to screen readers
   */
  static announce(message: string): void {
    this.initializeAnnouncements()
    if (this.announcements) {
      this.announcements.textContent = message
      
      // Clear after announcement
      setTimeout(() => {
        if (this.announcements) {
          this.announcements.textContent = ''
        }
      }, 1000)
    }
  }

  /**
   * Get keyboard shortcuts for drag operations
   */
  static getDragKeyboardShortcuts(): Record<string, string> {
    return {
      'Alt-ArrowUp': 'moveParagraphUp',
      'Alt-ArrowDown': 'moveParagraphDown',
      'Alt-Shift-ArrowUp': 'moveNodeUp',
      'Alt-Shift-ArrowDown': 'moveNodeDown',
      'Ctrl-Alt-ArrowUp': 'moveToTop',
      'Ctrl-Alt-ArrowDown': 'moveToBottom',
      'Alt-h': 'convertToHeading',
      'Alt-p': 'convertToParagraph',
      'Alt-l': 'convertToList',
      'Alt-c': 'convertToCodeBlock',
      'Alt-q': 'convertToBlockquote'
    }
  }

  /**
   * Get keyboard shortcuts for click menu operations
   */
  static getClickMenuKeyboardShortcuts(): Record<string, string> {
    return {
      'Ctrl-Space': 'showClickMenu',
      'Escape': 'hideClickMenu',
      'ArrowUp': 'navigateMenuUp',
      'ArrowDown': 'navigateMenuDown',
      'Enter': 'selectMenuItem',
      'Tab': 'navigateMenuNext',
      'Shift-Tab': 'navigateMenuPrevious'
    }
  }

  /**
   * Handle keyboard navigation for drag operations
   */
  static handleDragKeyboard(
    editor: Editor,
    event: KeyboardEvent,
    currentNode: ProseMirrorNode,
    currentPos: number
  ): boolean {
    const { key, altKey, shiftKey, ctrlKey } = event
    
    // Alt + Arrow keys for basic movement
    if (altKey && !shiftKey && !ctrlKey) {
      switch (key) {
        case 'ArrowUp':
          event.preventDefault()
          const moveUpResult = editor.commands.moveNodeUp(currentNode.type.name)
          this.announce(moveUpResult ? 
            `已将${this.getNodeTypeLabel(currentNode.type.name)}向上移动` : 
            '无法向上移动，已在顶部'
          )
          return moveUpResult

        case 'ArrowDown':
          event.preventDefault()
          const moveDownResult = editor.commands.moveNodeDown(currentNode.type.name)
          this.announce(moveDownResult ? 
            `已将${this.getNodeTypeLabel(currentNode.type.name)}向下移动` : 
            '无法向下移动，已在底部'
          )
          return moveDownResult

        // Quick transformation shortcuts
        case 'h':
          event.preventDefault()
          const headingResult = this.transformToHeading(editor, currentNode, currentPos)
          this.announce(headingResult ? '已转换为标题' : '无法转换为标题')
          return headingResult

        case 'p':
          event.preventDefault()
          const paragraphResult = this.transformToParagraph(editor, currentNode, currentPos)
          this.announce(paragraphResult ? '已转换为段落' : '无法转换为段落')
          return paragraphResult

        case 'l':
          event.preventDefault()
          const listResult = this.transformToList(editor, currentNode, currentPos)
          this.announce(listResult ? '已转换为列表' : '无法转换为列表')
          return listResult

        case 'c':
          event.preventDefault()
          const codeResult = this.transformToCodeBlock(editor, currentNode, currentPos)
          this.announce(codeResult ? '已转换为代码块' : '无法转换为代码块')
          return codeResult

        case 'q':
          event.preventDefault()
          const quoteResult = this.transformToBlockquote(editor, currentNode, currentPos)
          this.announce(quoteResult ? '已转换为引用块' : '无法转换为引用块')
          return quoteResult
      }
    }

    // Alt + Shift + Arrow keys for advanced movement
    if (altKey && shiftKey && !ctrlKey) {
      switch (key) {
        case 'ArrowUp':
          event.preventDefault()
          const nodeUpResult = editor.commands.moveNodeUp()
          this.announce(nodeUpResult ? 
            `已将${this.getNodeTypeLabel(currentNode.type.name)}向上移动` : 
            '无法向上移动'
          )
          return nodeUpResult

        case 'ArrowDown':
          event.preventDefault()
          const nodeDownResult = editor.commands.moveNodeDown()
          this.announce(nodeDownResult ? 
            `已将${this.getNodeTypeLabel(currentNode.type.name)}向下移动` : 
            '无法向下移动'
          )
          return nodeDownResult
      }
    }

    // Ctrl + Alt + Arrow keys for moving to extremes
    if (altKey && ctrlKey && !shiftKey) {
      switch (key) {
        case 'ArrowUp':
          event.preventDefault()
          const moveToTopResult = this.moveNodeToTop(editor, currentNode, currentPos)
          this.announce(moveToTopResult ? 
            `已将${this.getNodeTypeLabel(currentNode.type.name)}移动到顶部` : 
            '无法移动到顶部'
          )
          return moveToTopResult

        case 'ArrowDown':
          event.preventDefault()
          const moveToBottomResult = this.moveNodeToBottom(editor, currentNode, currentPos)
          this.announce(moveToBottomResult ? 
            `已将${this.getNodeTypeLabel(currentNode.type.name)}移动到底部` : 
            '无法移动到底部'
          )
          return moveToBottomResult
      }
    }

    return false
  }

  /**
   * Handle keyboard navigation for click menus
   */
  static handleClickMenuKeyboard(
    editor: Editor,
    event: KeyboardEvent,
    menuState: any
  ): boolean {
    const { key, shiftKey, ctrlKey, altKey } = event

    // Ctrl + Space to show menu
    if (ctrlKey && key === ' ') {
      event.preventDefault()
      const pos = editor.state.selection.from
      const result = editor.commands.showClickMenu(pos)
      this.announce(result ? '已打开上下文菜单' : '无法打开菜单')
      return result
    }

    // Only handle other keys if menu is visible
    if (!menuState?.visible) return false

    switch (key) {
      case 'Escape':
        event.preventDefault()
        const hideResult = editor.commands.hideClickMenu()
        this.announce('已关闭菜单')
        return hideResult

      case 'ArrowUp':
        event.preventDefault()
        // Navigate menu up - implementation depends on click menu extension
        const upResult = this.navigateMenuUp(editor)
        if (upResult) {
          this.announceMenuNavigation(editor, 'up')
        }
        return upResult

      case 'ArrowDown':
        event.preventDefault()
        // Navigate menu down - implementation depends on click menu extension
        const downResult = this.navigateMenuDown(editor)
        if (downResult) {
          this.announceMenuNavigation(editor, 'down')
        }
        return downResult

      case 'Enter':
      case ' ':
        event.preventDefault()
        const selectResult = this.selectMenuItem(editor)
        this.announce(selectResult ? '已执行菜单项' : '无法执行菜单项')
        return selectResult

      case 'Tab':
        event.preventDefault()
        const tabResult = this.navigateMenu(editor, shiftKey ? 'up' : 'down')
        if (tabResult) {
          this.announceMenuNavigation(editor, shiftKey ? 'up' : 'down')
        }
        return tabResult

      case 'Home':
        event.preventDefault()
        const homeResult = this.navigateToFirstMenuItem(editor)
        if (homeResult) {
          this.announce('已跳转到第一个菜单项')
        }
        return homeResult

      case 'End':
        event.preventDefault()
        const endResult = this.navigateToLastMenuItem(editor)
        if (endResult) {
          this.announce('已跳转到最后一个菜单项')
        }
        return endResult

      // Quick access keys for common actions
      case 'd':
        if (!altKey && !ctrlKey && !shiftKey) {
          event.preventDefault()
          const duplicateResult = editor.commands.executeMenuItem('duplicate')
          this.announce(duplicateResult ? '已复制内容' : '无法复制内容')
          return duplicateResult
        }
        break

      case 'Delete':
      case 'Backspace':
        event.preventDefault()
        const deleteResult = editor.commands.executeMenuItem('delete')
        this.announce(deleteResult ? '已删除内容' : '无法删除内容')
        return deleteResult

      // Number keys for quick transformations
      case '1':
      case '2':
      case '3':
        if (!altKey && !ctrlKey && !shiftKey) {
          event.preventDefault()
          const level = parseInt(key)
          const headingResult = editor.commands.executeMenuItem(`convertToHeading${level}`)
          this.announce(headingResult ? `已转换为标题 ${level}` : `无法转换为标题 ${level}`)
          return headingResult
        }
        break

      case 'p':
        if (!altKey && !ctrlKey && !shiftKey) {
          event.preventDefault()
          const paragraphResult = editor.commands.executeMenuItem('convertToParagraph')
          this.announce(paragraphResult ? '已转换为段落' : '无法转换为段落')
          return paragraphResult
        }
        break

      case 'c':
        if (!altKey && !ctrlKey && !shiftKey) {
          event.preventDefault()
          const codeResult = editor.commands.executeMenuItem('convertToCodeBlock')
          this.announce(codeResult ? '已转换为代码块' : '无法转换为代码块')
          return codeResult
        }
        break

      case 'q':
        if (!altKey && !ctrlKey && !shiftKey) {
          event.preventDefault()
          const quoteResult = editor.commands.executeMenuItem('convertToBlockquote')
          this.announce(quoteResult ? '已转换为引用块' : '无法转换为引用块')
          return quoteResult
        }
        break
    }

    return false
  }

  /**
   * Navigate menu up
   */
  private static navigateMenuUp(editor: Editor): boolean {
    try {
      if (editor.storage.clickMenu) {
        const visibleItems = this.getVisibleMenuItems(editor)
        const currentIndex = editor.storage.clickMenu.selectedIndex || 0
        const newIndex = currentIndex > 0 ? currentIndex - 1 : visibleItems.length - 1
        editor.storage.clickMenu.selectedIndex = newIndex
        return true
      }
      return false
    } catch (error) {
      console.error('Failed to navigate menu up:', error)
      return false
    }
  }

  /**
   * Navigate menu down
   */
  private static navigateMenuDown(editor: Editor): boolean {
    try {
      if (editor.storage.clickMenu) {
        const visibleItems = this.getVisibleMenuItems(editor)
        const currentIndex = editor.storage.clickMenu.selectedIndex || 0
        const newIndex = currentIndex < visibleItems.length - 1 ? currentIndex + 1 : 0
        editor.storage.clickMenu.selectedIndex = newIndex
        return true
      }
      return false
    } catch (error) {
      console.error('Failed to navigate menu down:', error)
      return false
    }
  }

  /**
   * Navigate menu in direction
   */
  private static navigateMenu(editor: Editor, direction: 'up' | 'down'): boolean {
    return direction === 'up' ? this.navigateMenuUp(editor) : this.navigateMenuDown(editor)
  }

  /**
   * Select current menu item
   */
  private static selectMenuItem(editor: Editor): boolean {
    try {
      if (editor.storage.clickMenu) {
        const visibleItems = this.getVisibleMenuItems(editor)
        const selectedIndex = editor.storage.clickMenu.selectedIndex || 0
        const selectedItem = visibleItems[selectedIndex]
        
        if (selectedItem && typeof selectedItem.action === 'function') {
          const { state } = editor
          const pos = state.selection.from
          const node = state.doc.nodeAt(pos)
          
          if (node) {
            selectedItem.action(editor, node, pos)
            return true
          }
        }
      }
      return false
    } catch (error) {
      console.error('Failed to select menu item:', error)
      return false
    }
  }

  /**
   * Navigate to first menu item
   */
  private static navigateToFirstMenuItem(editor: Editor): boolean {
    try {
      if (editor.storage.clickMenu) {
        editor.storage.clickMenu.selectedIndex = 0
        return true
      }
      return false
    } catch (error) {
      console.error('Failed to navigate to first menu item:', error)
      return false
    }
  }

  /**
   * Navigate to last menu item
   */
  private static navigateToLastMenuItem(editor: Editor): boolean {
    try {
      const visibleItems = this.getVisibleMenuItems(editor)
      if (visibleItems.length > 0 && editor.storage.clickMenu) {
        editor.storage.clickMenu.selectedIndex = visibleItems.length - 1
        return true
      }
      return false
    } catch (error) {
      console.error('Failed to navigate to last menu item:', error)
      return false
    }
  }

  /**
   * Move node to top of document/parent
   */
  private static moveNodeToTop(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      const { state } = editor
      const $pos = state.doc.resolve(pos)
      const parent = $pos.parent
      const currentIndex = $pos.index()
      
      if (currentIndex === 0) return false // Already at top
      
      const tr = state.tr
      // Remove node from current position
      tr.delete(pos, pos + node.nodeSize)
      // Insert at beginning of parent
      const insertPos = $pos.start()
      tr.insert(insertPos, node)
      
      editor.view.dispatch(tr)
      return true
    } catch (error) {
      console.error('Failed to move node to top:', error)
      return false
    }
  }

  /**
   * Move node to bottom of document/parent
   */
  private static moveNodeToBottom(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      const { state } = editor
      const $pos = state.doc.resolve(pos)
      const parent = $pos.parent
      const currentIndex = $pos.index()
      
      if (currentIndex === parent.childCount - 1) return false // Already at bottom
      
      const tr = state.tr
      // Remove node from current position
      tr.delete(pos, pos + node.nodeSize)
      // Insert at end of parent
      const insertPos = $pos.end()
      tr.insert(insertPos, node)
      
      editor.view.dispatch(tr)
      return true
    } catch (error) {
      console.error('Failed to move node to bottom:', error)
      return false
    }
  }

  /**
   * Announce menu navigation to screen readers
   */
  private static announceMenuNavigation(editor: Editor, direction: 'up' | 'down'): void {
    try {
      // Get current menu state from editor storage
      const menuState = editor.storage.clickMenu?.menuState
      if (!menuState) return

      const selectedIndex = editor.storage.clickMenu?.selectedIndex || 0
      const visibleItems = this.getVisibleMenuItems(editor)
      
      if (visibleItems && visibleItems[selectedIndex]) {
        const item = visibleItems[selectedIndex]
        this.announce(`${item.label}，第 ${selectedIndex + 1} 项，共 ${visibleItems.length} 项`)
      }
    } catch (error) {
      console.error('Failed to announce menu navigation:', error)
    }
  }

  /**
   * Get visible menu items from editor
   */
  private static getVisibleMenuItems(editor: Editor): any[] {
    try {
      const extension = editor.extensionManager.extensions.find(ext => ext.name === 'clickMenu')
      if (extension && typeof extension.getVisibleMenuItems === 'function') {
        return extension.getVisibleMenuItems()
      }
      return []
    } catch {
      return []
    }
  }

  /**
   * Transform node to heading
   */
  private static transformToHeading(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      if (node.type.name === 'heading') {
        // Cycle through heading levels
        const currentLevel = node.attrs.level || 1
        const nextLevel = currentLevel >= 6 ? 1 : currentLevel + 1
        const tr = editor.state.tr
        const attrs = { ...node.attrs, level: nextLevel }
        tr.setNodeMarkup(pos, null, attrs)
        editor.view.dispatch(tr)
        return true
      } else if (['paragraph', 'blockquote'].includes(node.type.name)) {
        const headingType = editor.schema.nodes.heading
        if (headingType) {
          const tr = editor.state.tr
          const attrs = { level: 1 }
          tr.setNodeMarkup(pos, headingType, attrs)
          editor.view.dispatch(tr)
          return true
        }
      }
      return false
    } catch (error) {
      console.error('Failed to transform to heading:', error)
      return false
    }
  }

  /**
   * Transform node to paragraph
   */
  private static transformToParagraph(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      if (node.type.name === 'paragraph') return false
      
      const paragraphType = editor.schema.nodes.paragraph
      if (paragraphType) {
        const tr = editor.state.tr
        const attrs = {}
        tr.setNodeMarkup(pos, paragraphType, attrs)
        editor.view.dispatch(tr)
        return true
      }
      return false
    } catch (error) {
      console.error('Failed to transform to paragraph:', error)
      return false
    }
  }

  /**
   * Transform node to list
   */
  private static transformToList(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      if (['paragraph', 'heading', 'blockquote'].includes(node.type.name)) {
        editor.chain().focus().setTextSelection(pos).toggleBulletList().run()
        return true
      } else if (node.type.name === 'bulletList') {
        editor.chain().focus().setTextSelection(pos).toggleOrderedList().run()
        return true
      } else if (node.type.name === 'orderedList') {
        editor.chain().focus().setTextSelection(pos).toggleBulletList().run()
        return true
      }
      return false
    } catch (error) {
      console.error('Failed to transform to list:', error)
      return false
    }
  }

  /**
   * Transform node to code block
   */
  private static transformToCodeBlock(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      if (node.type.name === 'codeBlock') return false
      
      const codeBlockType = editor.schema.nodes.codeBlock
      if (codeBlockType && ['paragraph', 'heading', 'blockquote'].includes(node.type.name)) {
        const tr = editor.state.tr
        const attrs = { language: 'javascript' }
        tr.setNodeMarkup(pos, codeBlockType, attrs)
        editor.view.dispatch(tr)
        return true
      }
      return false
    } catch (error) {
      console.error('Failed to transform to code block:', error)
      return false
    }
  }

  /**
   * Transform node to blockquote
   */
  private static transformToBlockquote(
    editor: Editor, 
    node: ProseMirrorNode, 
    pos: number
  ): boolean {
    try {
      if (node.type.name === 'blockquote') return false
      
      const blockquoteType = editor.schema.nodes.blockquote
      if (blockquoteType && ['paragraph', 'heading'].includes(node.type.name)) {
        const tr = editor.state.tr
        const attrs = { style: 'default' }
        tr.setNodeMarkup(pos, blockquoteType, attrs)
        editor.view.dispatch(tr)
        return true
      }
      return false
    } catch (error) {
      console.error('Failed to transform to blockquote:', error)
      return false
    }
  }

  /**
   * Get human-readable label for node type
   */
  private static getNodeTypeLabel(nodeType: string): string {
    const labels: Record<string, string> = {
      paragraph: '段落',
      heading: '标题',
      listItem: '列表项',
      codeBlock: '代码块',
      blockquote: '引用块',
      bulletList: '无序列表',
      orderedList: '有序列表',
      table: '表格',
      image: '图片'
    }
    return labels[nodeType] || nodeType
  }

  /**
   * Add ARIA attributes to drag handles
   */
  static addDragHandleAria(element: HTMLElement, nodeType: string): void {
    const nodeLabel = this.getNodeTypeLabel(nodeType)
    element.setAttribute('role', 'button')
    element.setAttribute('aria-label', `拖拽${nodeLabel}`)
    element.setAttribute('aria-describedby', 'drag-instructions')
    element.setAttribute('tabindex', '0')
    element.setAttribute('data-node-type', nodeType)
    
    // Add keyboard shortcuts hint
    const shortcuts = this.getNodeTypeShortcuts(nodeType)
    if (shortcuts.length > 0) {
      element.setAttribute('aria-keyshortcuts', shortcuts.join(' '))
    }
    
    // Add instructions element if it doesn't exist
    if (!document.getElementById('drag-instructions')) {
      const instructions = document.createElement('div')
      instructions.id = 'drag-instructions'
      instructions.style.cssText = `
        position: absolute;
        left: -10000px;
        width: 1px;
        height: 1px;
        overflow: hidden;
      `
      instructions.innerHTML = `
        <p>拖拽操作说明：</p>
        <ul>
          <li>Alt + 方向键：移动内容</li>
          <li>Alt + Shift + 方向键：高级移动</li>
          <li>Ctrl + Alt + 方向键：移动到顶部/底部</li>
          <li>Alt + h：转换为标题</li>
          <li>Alt + p：转换为段落</li>
          <li>Alt + l：转换为列表</li>
          <li>Alt + c：转换为代码块</li>
          <li>Alt + q：转换为引用块</li>
          <li>Ctrl + Space：打开上下文菜单</li>
        </ul>
      `
      document.body.appendChild(instructions)
    }
  }

  /**
   * Get keyboard shortcuts for specific node type
   */
  private static getNodeTypeShortcuts(nodeType: string): string[] {
    const baseShortcuts = ['Alt+ArrowUp', 'Alt+ArrowDown', 'Ctrl+Space']
    const nodeSpecificShortcuts: Record<string, string[]> = {
      paragraph: ['Alt+h', 'Alt+l', 'Alt+c', 'Alt+q'],
      heading: ['Alt+p', 'Alt+l', 'Alt+c', 'Alt+q'],
      listItem: ['Alt+p', 'Alt+h', 'Alt+c'],
      codeBlock: ['Alt+p', 'Alt+h', 'Alt+q'],
      blockquote: ['Alt+p', 'Alt+h', 'Alt+l', 'Alt+c'],
      bulletList: ['Alt+p', 'Alt+h'],
      orderedList: ['Alt+p', 'Alt+h']
    }
    
    return [...baseShortcuts, ...(nodeSpecificShortcuts[nodeType] || [])]
  }

  /**
   * Add ARIA attributes to click menu
   */
  static addClickMenuAria(menuElement: HTMLElement, itemCount: number, nodeType?: string): void {
    const nodeLabel = nodeType ? this.getNodeTypeLabel(nodeType) : '内容'
    menuElement.setAttribute('role', 'menu')
    menuElement.setAttribute('aria-label', `${nodeLabel}上下文菜单`)
    menuElement.setAttribute('aria-orientation', 'vertical')
    menuElement.setAttribute('aria-activedescendant', '')
    menuElement.setAttribute('aria-describedby', 'menu-instructions')
    
    // Add menu instructions
    if (!document.getElementById('menu-instructions')) {
      const instructions = document.createElement('div')
      instructions.id = 'menu-instructions'
      instructions.style.cssText = `
        position: absolute;
        left: -10000px;
        width: 1px;
        height: 1px;
        overflow: hidden;
      `
      instructions.innerHTML = `
        <p>菜单导航说明：</p>
        <ul>
          <li>方向键：导航菜单项</li>
          <li>回车或空格：执行选中项</li>
          <li>Escape：关闭菜单</li>
          <li>Home：跳转到第一项</li>
          <li>End：跳转到最后一项</li>
          <li>数字键1-3：快速转换为标题</li>
          <li>p：转换为段落</li>
          <li>c：转换为代码块</li>
          <li>q：转换为引用块</li>
          <li>d：复制内容</li>
          <li>Delete：删除内容</li>
        </ul>
      `
      document.body.appendChild(instructions)
    }
    
    // Add menu items ARIA
    const menuItems = menuElement.querySelectorAll('[role="menuitem"]')
    menuItems.forEach((item, index) => {
      item.setAttribute('aria-posinset', (index + 1).toString())
      item.setAttribute('aria-setsize', itemCount.toString())
    })
  }

  /**
   * Add ARIA attributes to menu items
   */
  static addMenuItemAria(
    itemElement: HTMLElement, 
    label: string, 
    shortcut?: string,
    isSelected: boolean = false,
    isEnabled: boolean = true,
    group?: string
  ): void {
    itemElement.setAttribute('role', 'menuitem')
    itemElement.setAttribute('aria-label', label)
    itemElement.setAttribute('tabindex', isSelected ? '0' : '-1')
    itemElement.setAttribute('aria-disabled', isEnabled ? 'false' : 'true')
    
    if (shortcut) {
      itemElement.setAttribute('aria-keyshortcuts', shortcut)
      // Add shortcut to label for screen readers
      itemElement.setAttribute('aria-label', `${label} (${shortcut})`)
    }
    
    if (group) {
      itemElement.setAttribute('aria-describedby', `group-${group}`)
    }
    
    if (isSelected) {
      itemElement.setAttribute('aria-selected', 'true')
      itemElement.classList.add('selected')
    } else {
      itemElement.removeAttribute('aria-selected')
      itemElement.classList.remove('selected')
    }
    
    if (!isEnabled) {
      itemElement.classList.add('disabled')
    } else {
      itemElement.classList.remove('disabled')
    }
  }

  /**
   * Handle focus management for drag handles
   */
  static manageDragHandleFocus(
    handleElement: HTMLElement, 
    editor: Editor
  ): void {
    handleElement.addEventListener('keydown', (event) => {
      const { state } = editor
      const pos = state.selection.from
      const node = state.doc.nodeAt(pos)
      
      if (node) {
        this.handleDragKeyboard(editor, event, node, pos)
      }
    })

    handleElement.addEventListener('focus', () => {
      this.announce('拖拽手柄已获得焦点，使用 Alt + 方向键移动内容')
    })
  }

  /**
   * Handle focus management for click menus
   */
  static manageClickMenuFocus(
    menuElement: HTMLElement, 
    editor: Editor
  ): void {
    // Set initial focus to first menu item
    const firstItem = menuElement.querySelector('[role="menuitem"]') as HTMLElement
    if (firstItem) {
      firstItem.focus()
    }

    // Handle keyboard navigation within menu
    menuElement.addEventListener('keydown', (event) => {
      const menuState = editor.storage.clickMenu?.menuState
      this.handleClickMenuKeyboard(editor, event, menuState)
    })

    // Announce menu opening
    this.announce('上下文菜单已打开，使用方向键导航，回车键选择')
  }

  /**
   * Create group label for menu accessibility
   */
  static createGroupLabel(groupId: string, groupLabel: string): HTMLElement {
    let groupElement = document.getElementById(`group-${groupId}`)
    
    if (!groupElement) {
      groupElement = document.createElement('div')
      groupElement.id = `group-${groupId}`
      groupElement.style.cssText = `
        position: absolute;
        left: -10000px;
        width: 1px;
        height: 1px;
        overflow: hidden;
      `
      groupElement.textContent = `${groupLabel}组`
      document.body.appendChild(groupElement)
    }
    
    return groupElement
  }

  /**
   * Announce drag operation status
   */
  static announceDragOperation(
    operation: 'start' | 'move' | 'drop' | 'cancel',
    nodeType: string,
    details?: string
  ): void {
    const nodeLabel = this.getNodeTypeLabel(nodeType)
    const messages = {
      start: `开始拖拽${nodeLabel}`,
      move: `正在移动${nodeLabel}${details ? '，' + details : ''}`,
      drop: `${nodeLabel}已放置${details ? '，' + details : ''}`,
      cancel: `取消拖拽${nodeLabel}`
    }
    
    this.announce(messages[operation])
  }

  /**
   * Announce menu operation status
   */
  static announceMenuOperation(
    operation: 'open' | 'close' | 'execute' | 'navigate',
    details?: string
  ): void {
    const messages = {
      open: `上下文菜单已打开${details ? '，' + details : ''}`,
      close: '上下文菜单已关闭',
      execute: `已执行菜单项${details ? '：' + details : ''}`,
      navigate: `导航到菜单项${details ? '：' + details : ''}`
    }
    
    this.announce(messages[operation])
  }

  /**
   * Cleanup accessibility resources
   */
  static cleanup(): void {
    if (this.announcements && this.announcements.parentNode) {
      this.announcements.parentNode.removeChild(this.announcements)
      this.announcements = null
    }

    const elementsToCleanup = [
      'drag-instructions',
      'menu-instructions'
    ]
    
    elementsToCleanup.forEach(id => {
      const element = document.getElementById(id)
      if (element && element.parentNode) {
        element.parentNode.removeChild(element)
      }
    })
    
    // Clean up group labels
    const groupElements = document.querySelectorAll('[id^="group-"]')
    groupElements.forEach(element => {
      if (element.parentNode) {
        element.parentNode.removeChild(element)
      }
    })
  }

  /**
   * Check if user prefers reduced motion
   */
  static prefersReducedMotion(): boolean {
    return window.matchMedia('(prefers-reduced-motion: reduce)').matches
  }

  /**
   * Enhanced keyboard shortcuts for drag operations with better accessibility
   */
  static getEnhancedDragKeyboardShortcuts(): Record<string, string> {
    return {
      // Basic movement
      'Alt-ArrowUp': 'moveNodeUp',
      'Alt-ArrowDown': 'moveNodeDown',
      
      // Advanced movement
      'Alt-Shift-ArrowUp': 'moveToTop',
      'Alt-Shift-ArrowDown': 'moveToBottom',
      
      // Content transformation shortcuts
      'Alt-1': 'convertToHeading1',
      'Alt-2': 'convertToHeading2', 
      'Alt-3': 'convertToHeading3',
      'Alt-h': 'convertToHeading',
      'Alt-p': 'convertToParagraph',
      'Alt-l': 'convertToList',
      'Alt-c': 'convertToCodeBlock',
      'Alt-q': 'convertToBlockquote',
      
      // Menu activation
      'Ctrl-Space': 'showClickMenu',
      'Alt-m': 'showClickMenu',
      
      // Quick actions
      'Alt-d': 'duplicateNode',
      'Alt-Delete': 'deleteNode'
    }
  }

  /**
   * Enhanced keyboard shortcuts for click menu operations
   */
  static getEnhancedClickMenuKeyboardShortcuts(): Record<string, string> {
    return {
      // Menu navigation - only when menu is visible
      'ArrowUp': 'navigateMenuUp',
      'ArrowDown': 'navigateMenuDown',
      'Home': 'navigateToFirstMenuItem',
      'End': 'navigateToLastMenuItem',
      'Tab': 'navigateMenuNext',
      'Shift-Tab': 'navigateMenuPrevious',
      
      // Menu actions - only when menu is visible
      'Enter': 'selectMenuItem',
      'Space': 'selectMenuItem',
      'Escape': 'hideClickMenu',
      
      // Menu activation shortcuts - work globally
      'Ctrl-Space': 'showClickMenu',
      'Alt-m': 'showClickMenu'
      
      // Note: Quick transformations (1, 2, 3, etc.) are handled separately
      // and only work when menu is visible to avoid conflicts with normal typing
    }
  }

  /**
   * Get quick transformation shortcuts that only work when menu is visible
   */
  static getClickMenuQuickShortcuts(): Record<string, string> {
    return {
      '1': 'convertToHeading1',
      '2': 'convertToHeading2',
      '3': 'convertToHeading3',
      '4': 'convertToHeading4',
      '5': 'convertToHeading5',
      '6': 'convertToHeading6',
      'p': 'convertToParagraph',
      'c': 'convertToCodeBlock',
      'q': 'convertToBlockquote',
      'l': 'convertToList',
      'd': 'duplicateContent',
      'Delete': 'deleteContent',
      'Backspace': 'deleteContent'
    }
  }

  /**
   * Handle enhanced keyboard navigation for drag operations
   */
  static handleEnhancedDragKeyboard(
    editor: Editor,
    event: KeyboardEvent,
    currentNode: ProseMirrorNode,
    currentPos: number
  ): boolean {
    const { key, altKey, shiftKey, ctrlKey } = event
    
    // Enhanced Alt + number shortcuts for heading levels
    if (altKey && !shiftKey && !ctrlKey && /^[1-6]$/.test(key)) {
      event.preventDefault()
      const level = parseInt(key)
      const result = this.transformToHeadingLevel(editor, currentNode, currentPos, level)
      this.announce(result ? `已转换为${level}级标题` : `无法转换为${level}级标题`)
      return result
    }

    // Enhanced Ctrl+Space for menu activation
    if (ctrlKey && key === ' ') {
      event.preventDefault()
      const result = editor.commands.showClickMenu(currentPos)
      this.announce(result ? '已打开上下文菜单' : '无法打开菜单')
      return result
    }

    // Alt+m for menu activation (alternative)
    if (altKey && !shiftKey && !ctrlKey && key === 'm') {
      event.preventDefault()
      const result = editor.commands.showClickMenu(currentPos)
      this.announce(result ? '已打开上下文菜单' : '无法打开菜单')
      return result
    }

    // Enhanced Alt+d for duplication
    if (altKey && !shiftKey && !ctrlKey && key === 'd') {
      event.preventDefault()
      const result = this.duplicateNode(editor, currentNode, currentPos)
      this.announce(result ? `已复制${this.getNodeTypeLabel(currentNode.type.name)}` : '无法复制内容')
      return result
    }

    // Enhanced Alt+Delete for deletion
    if (altKey && key === 'Delete') {
      event.preventDefault()
      const result = this.deleteNode(editor, currentNode, currentPos)
      this.announce(result ? `已删除${this.getNodeTypeLabel(currentNode.type.name)}` : '无法删除内容')
      return result
    }

    // Enhanced Alt+Shift shortcuts for moving to extremes
    if (altKey && shiftKey && !ctrlKey) {
      switch (key) {
        case 'ArrowUp':
          event.preventDefault()
          const moveToTopResult = editor.commands.moveToTop(currentNode.type.name)
          this.announce(moveToTopResult ? 
            `已将${this.getNodeTypeLabel(currentNode.type.name)}移动到顶部` : 
            '无法移动到顶部'
          )
          return moveToTopResult

        case 'ArrowDown':
          event.preventDefault()
          const moveToBottomResult = editor.commands.moveToBottom(currentNode.type.name)
          this.announce(moveToBottomResult ? 
            `已将${this.getNodeTypeLabel(currentNode.type.name)}移动到底部` : 
            '无法移动到底部'
          )
          return moveToBottomResult
      }
    }

    // Fall back to original handler for other shortcuts
    return this.handleDragKeyboard(editor, event, currentNode, currentPos)
  }

  /**
   * Handle enhanced keyboard navigation for click menus
   */
  static handleEnhancedClickMenuKeyboard(
    editor: Editor,
    event: KeyboardEvent,
    menuState: any
  ): boolean {
    const { key, shiftKey, ctrlKey, altKey } = event

    // Enhanced Alt + number shortcuts for heading levels (1-6) - only with Alt key
    if (altKey && !ctrlKey && !shiftKey && /^[1-6]$/.test(key)) {
      event.preventDefault()
      const level = parseInt(key)
      const result = editor.commands.executeMenuItem(`convertToHeading${level}`)
      this.announce(result ? `已转换为${level}级标题` : `无法转换为${level}级标题`)
      return result
    }

    // Enhanced quick access shortcuts
    if (!altKey && !ctrlKey && !shiftKey) {
      switch (key) {
        case 'u':
          event.preventDefault()
          const bulletResult = editor.commands.executeMenuItem('convertToBulletList')
          this.announce(bulletResult ? '已转换为无序列表' : '无法转换为无序列表')
          return bulletResult

        case 'o':
          event.preventDefault()
          const orderedResult = editor.commands.executeMenuItem('convertToOrderedList')
          this.announce(orderedResult ? '已转换为有序列表' : '无法转换为有序列表')
          return orderedResult

        case 't':
          event.preventDefault()
          const tableResult = editor.commands.executeMenuItem('insertTable')
          this.announce(tableResult ? '已插入表格' : '无法插入表格')
          return tableResult

        case 'i':
          event.preventDefault()
          const imageResult = editor.commands.executeMenuItem('insertImage')
          this.announce(imageResult ? '已打开图片插入对话框' : '无法插入图片')
          return imageResult
      }
    }

    // Fall back to original handler
    return this.handleClickMenuKeyboard(editor, event, menuState)
  }

  /**
   * Transform node to specific heading level
   */
  private static transformToHeadingLevel(
    editor: Editor,
    node: ProseMirrorNode,
    pos: number,
    level: number
  ): boolean {
    try {
      if (level < 1 || level > 6) return false
      
      const headingType = editor.schema.nodes.heading
      if (headingType && ['paragraph', 'heading', 'blockquote'].includes(node.type.name)) {
        const tr = editor.state.tr
        const attrs = { level }
        tr.setNodeMarkup(pos, headingType, attrs)
        editor.view.dispatch(tr)
        return true
      }
      return false
    } catch (error) {
      console.error('Failed to transform to heading level:', error)
      return false
    }
  }

  /**
   * Duplicate current node
   */
  private static duplicateNode(
    editor: Editor,
    node: ProseMirrorNode,
    pos: number
  ): boolean {
    try {
      const { state } = editor
      const tr = state.tr
      
      // Insert a copy of the node after the current one
      const insertPos = pos + node.nodeSize
      tr.insert(insertPos, node)
      
      editor.view.dispatch(tr)
      return true
    } catch (error) {
      console.error('Failed to duplicate node:', error)
      return false
    }
  }

  /**
   * Delete current node
   */
  private static deleteNode(
    editor: Editor,
    node: ProseMirrorNode,
    pos: number
  ): boolean {
    try {
      const { state } = editor
      const tr = state.tr
      
      // Delete the node
      tr.delete(pos, pos + node.nodeSize)
      
      editor.view.dispatch(tr)
      return true
    } catch (error) {
      console.error('Failed to delete node:', error)
      return false
    }
  }

  /**
   * Enhanced screen reader announcements with context
   */
  static announceWithContext(
    message: string,
    context?: {
      nodeType?: string
      position?: number
      totalItems?: number
      operation?: string
    }
  ): void {
    let fullMessage = message
    
    if (context) {
      const parts = []
      
      if (context.nodeType) {
        parts.push(`${this.getNodeTypeLabel(context.nodeType)}`)
      }
      
      if (context.position !== undefined && context.totalItems !== undefined) {
        parts.push(`第${context.position + 1}项，共${context.totalItems}项`)
      }
      
      if (context.operation) {
        parts.push(`操作：${context.operation}`)
      }
      
      if (parts.length > 0) {
        fullMessage = `${message}。${parts.join('，')}`
      }
    }
    
    this.announce(fullMessage)
  }

  /**
   * Announce keyboard shortcut help
   */
  static announceKeyboardHelp(context: 'drag' | 'menu'): void {
    const helpMessages = {
      drag: `
        拖拽快捷键帮助：
        Alt + 方向键：移动内容；
        Alt + Shift + 方向键：移动到顶部或底部；
        Alt + 数字键1-6：转换为对应级别标题；
        Alt + h：转换为标题；
        Alt + p：转换为段落；
        Alt + l：转换为列表；
        Alt + c：转换为代码块；
        Alt + q：转换为引用块；
        Alt + d：复制内容；
        Alt + Delete：删除内容；
        Ctrl + Space 或 Alt + m：打开上下文菜单
      `,
      menu: `
        菜单快捷键帮助：
        方向键：导航菜单项；
        Home/End：跳转到第一项/最后一项；
        Tab/Shift+Tab：切换菜单项；
        回车或空格：执行选中项；
        Escape：关闭菜单；
        数字键1-6：转换为对应级别标题；
        p：转换为段落；
        c：转换为代码块；
        q：转换为引用块；
        l：转换为列表；
        u：转换为无序列表；
        o：转换为有序列表；
        t：插入表格；
        i：插入图片；
        d：复制内容；
        Delete：删除内容
      `
    }
    
    this.announce(helpMessages[context])
  }

  /**
   * Enhanced ARIA attributes for drag handles with better context
   */
  static addEnhancedDragHandleAria(
    element: HTMLElement, 
    nodeType: string,
    position?: number,
    totalNodes?: number
  ): void {
    const nodeLabel = this.getNodeTypeLabel(nodeType)
    
    // Basic ARIA attributes
    element.setAttribute('role', 'button')
    element.setAttribute('aria-label', `拖拽${nodeLabel}以重新排序`)
    element.setAttribute('aria-describedby', 'drag-instructions')
    element.setAttribute('tabindex', '0')
    element.setAttribute('data-node-type', nodeType)
    
    // Position information for screen readers
    if (position !== undefined && totalNodes !== undefined) {
      element.setAttribute('aria-posinset', (position + 1).toString())
      element.setAttribute('aria-setsize', totalNodes.toString())
      element.setAttribute('aria-label', 
        `拖拽${nodeLabel}以重新排序，第${position + 1}项，共${totalNodes}项`
      )
    }
    
    // Enhanced keyboard shortcuts
    const shortcuts = this.getEnhancedNodeTypeShortcuts(nodeType)
    if (shortcuts.length > 0) {
      element.setAttribute('aria-keyshortcuts', shortcuts.join(' '))
    }
    
    // Add drag state attributes
    element.setAttribute('aria-grabbed', 'false')
    
    // Enhanced instructions
    this.createEnhancedDragInstructions()
  }

  /**
   * Enhanced ARIA attributes for click menu with better accessibility
   */
  static addEnhancedClickMenuAria(
    menuElement: HTMLElement, 
    itemCount: number, 
    nodeType?: string,
    menuId?: string
  ): void {
    const nodeLabel = nodeType ? this.getNodeTypeLabel(nodeType) : '内容'
    const uniqueId = menuId || `click-menu-${Date.now()}`
    
    // Enhanced menu attributes
    menuElement.setAttribute('role', 'menu')
    menuElement.setAttribute('aria-label', `${nodeLabel}操作菜单，共${itemCount}项`)
    menuElement.setAttribute('aria-orientation', 'vertical')
    menuElement.setAttribute('aria-activedescendant', '')
    menuElement.setAttribute('aria-describedby', 'menu-instructions')
    menuElement.setAttribute('id', uniqueId)
    
    // Live region for announcements
    menuElement.setAttribute('aria-live', 'polite')
    menuElement.setAttribute('aria-atomic', 'false')
    
    // Enhanced menu instructions
    this.createEnhancedMenuInstructions()
    
    // Add menu items ARIA with enhanced context
    const menuItems = menuElement.querySelectorAll('[role="menuitem"]')
    menuItems.forEach((item, index) => {
      this.addEnhancedMenuItemAria(
        item as HTMLElement,
        item.textContent || '',
        undefined,
        false,
        true,
        undefined,
        index,
        itemCount
      )
    })
  }

  /**
   * Enhanced ARIA attributes for menu items with position context
   */
  static addEnhancedMenuItemAria(
    itemElement: HTMLElement,
    label: string,
    shortcut?: string,
    isSelected: boolean = false,
    isEnabled: boolean = true,
    group?: string,
    position?: number,
    totalItems?: number
  ): void {
    // Basic menu item attributes
    itemElement.setAttribute('role', 'menuitem')
    itemElement.setAttribute('tabindex', isSelected ? '0' : '-1')
    itemElement.setAttribute('aria-disabled', isEnabled ? 'false' : 'true')
    
    // Enhanced label with position information
    let enhancedLabel = label
    if (position !== undefined && totalItems !== undefined) {
      enhancedLabel = `${label}，第${position + 1}项，共${totalItems}项`
    }
    
    if (shortcut) {
      enhancedLabel += `，快捷键${shortcut}`
      itemElement.setAttribute('aria-keyshortcuts', shortcut)
    }
    
    itemElement.setAttribute('aria-label', enhancedLabel)
    
    // Position in set
    if (position !== undefined && totalItems !== undefined) {
      itemElement.setAttribute('aria-posinset', (position + 1).toString())
      itemElement.setAttribute('aria-setsize', totalItems.toString())
    }
    
    // Group information
    if (group) {
      itemElement.setAttribute('aria-describedby', `group-${group}`)
      this.createGroupLabel(group, group)
    }
    
    // Selection state
    if (isSelected) {
      itemElement.setAttribute('aria-selected', 'true')
      itemElement.classList.add('selected')
    } else {
      itemElement.removeAttribute('aria-selected')
      itemElement.classList.remove('selected')
    }
    
    // Enabled state
    if (!isEnabled) {
      itemElement.classList.add('disabled')
    } else {
      itemElement.classList.remove('disabled')
    }
  }

  /**
   * Get enhanced keyboard shortcuts for specific node type
   */
  private static getEnhancedNodeTypeShortcuts(nodeType: string): string[] {
    const baseShortcuts = ['Alt+ArrowUp', 'Alt+ArrowDown', 'Ctrl+Space', 'Alt+m']
    const nodeSpecificShortcuts: Record<string, string[]> = {
      paragraph: ['Alt+h', 'Alt+l', 'Alt+c', 'Alt+q', 'Alt+1', 'Alt+2', 'Alt+3'],
      heading: ['Alt+p', 'Alt+l', 'Alt+c', 'Alt+q', 'Alt+1', 'Alt+2', 'Alt+3'],
      listItem: ['Alt+p', 'Alt+h', 'Alt+c', 'Alt+1', 'Alt+2', 'Alt+3'],
      codeBlock: ['Alt+p', 'Alt+h', 'Alt+q', 'Alt+1', 'Alt+2', 'Alt+3'],
      blockquote: ['Alt+p', 'Alt+h', 'Alt+l', 'Alt+c', 'Alt+1', 'Alt+2', 'Alt+3'],
      bulletList: ['Alt+p', 'Alt+h', 'Alt+1', 'Alt+2', 'Alt+3'],
      orderedList: ['Alt+p', 'Alt+h', 'Alt+1', 'Alt+2', 'Alt+3']
    }
    
    const commonShortcuts = ['Alt+d', 'Alt+Delete', 'Alt+Shift+ArrowUp', 'Alt+Shift+ArrowDown']
    
    return [...baseShortcuts, ...(nodeSpecificShortcuts[nodeType] || []), ...commonShortcuts]
  }

  /**
   * Create enhanced drag instructions
   */
  private static createEnhancedDragInstructions(): void {
    if (document.getElementById('drag-instructions')) return
    
    const instructions = document.createElement('div')
    instructions.id = 'drag-instructions'
    instructions.style.cssText = `
      position: absolute;
      left: -10000px;
      width: 1px;
      height: 1px;
      overflow: hidden;
    `
    instructions.innerHTML = `
      <div>
        <h3>拖拽操作说明</h3>
        <h4>基本移动</h4>
        <ul>
          <li>Alt + 上方向键：向上移动内容</li>
          <li>Alt + 下方向键：向下移动内容</li>
          <li>Alt + Shift + 上方向键：移动到顶部</li>
          <li>Alt + Shift + 下方向键：移动到底部</li>
        </ul>
        <h4>内容转换</h4>
        <ul>
          <li>Alt + 数字键1-6：转换为对应级别标题</li>
          <li>Alt + h：转换为标题</li>
          <li>Alt + p：转换为段落</li>
          <li>Alt + l：转换为列表</li>
          <li>Alt + c：转换为代码块</li>
          <li>Alt + q：转换为引用块</li>
        </ul>
        <h4>其他操作</h4>
        <ul>
          <li>Alt + d：复制内容</li>
          <li>Alt + Delete：删除内容</li>
          <li>Ctrl + Space 或 Alt + m：打开上下文菜单</li>
          <li>Escape：取消当前操作</li>
        </ul>
      </div>
    `
    document.body.appendChild(instructions)
  }

  /**
   * Create enhanced menu instructions
   */
  private static createEnhancedMenuInstructions(): void {
    if (document.getElementById('menu-instructions')) return
    
    const instructions = document.createElement('div')
    instructions.id = 'menu-instructions'
    instructions.style.cssText = `
      position: absolute;
      left: -10000px;
      width: 1px;
      height: 1px;
      overflow: hidden;
    `
    instructions.innerHTML = `
      <div>
        <h3>菜单操作说明</h3>
        <h4>导航</h4>
        <ul>
          <li>上下方向键：在菜单项之间导航</li>
          <li>Home：跳转到第一个菜单项</li>
          <li>End：跳转到最后一个菜单项</li>
          <li>Tab：下一个菜单项</li>
          <li>Shift + Tab：上一个菜单项</li>
        </ul>
        <h4>执行</h4>
        <ul>
          <li>回车或空格：执行选中的菜单项</li>
          <li>Escape：关闭菜单</li>
        </ul>
        <h4>快速转换</h4>
        <ul>
          <li>数字键1-6：转换为对应级别标题</li>
          <li>p：转换为段落</li>
          <li>c：转换为代码块</li>
          <li>q：转换为引用块</li>
          <li>l：转换为列表</li>
          <li>u：转换为无序列表</li>
          <li>o：转换为有序列表</li>
          <li>t：插入表格</li>
          <li>i：插入图片</li>
        </ul>
        <h4>其他操作</h4>
        <ul>
          <li>d：复制内容</li>
          <li>Delete 或 Backspace：删除内容</li>
        </ul>
      </div>
    `
    document.body.appendChild(instructions)
  }

  /**
   * Get appropriate animation duration based on user preferences
   */
  static getAnimationDuration(): number {
    return this.prefersReducedMotion() ? 0 : 200
  }
}