{"enabled": true, "name": "Auto Translation Generator", "description": "Monitors changes to localization files and automatically generates translations for all configured target languages while maintaining context and locale-specific conventions", "version": "1", "when": {"type": "fileEdited", "patterns": ["**/*.json", "**/*.yaml", "**/*.yml", "**/locales/**/*", "**/i18n/**/*", "**/lang/**/*", "**/translations/**/*"]}, "then": {"type": "askAgent", "prompt": "A localization file has been modified. Please analyze the changes to identify new or modified text content that needs translation. For each change:\n\n1. Extract the new/modified text strings and their keys\n2. Identify the source language and context\n3. Generate accurate translations for all configured target languages\n4. Ensure translations maintain proper context, meaning, and cultural appropriateness\n5. Follow locale-specific conventions (date formats, number formats, etc.)\n6. Preserve any interpolation variables, HTML tags, or special formatting\n7. Consider the UI context where the text will be displayed\n\nProvide the translations in the same file format and structure as the source file. If multiple languages are configured, generate separate translation files or update existing ones as appropriate."}}