package com.shenmo.wen.app.core.comment.service.impl;

import cn.dev33.satoken.stp.StpUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.shenmo.wen.app.core.article.exception.ArticleExceptionEnum;
import com.shenmo.wen.app.core.article.mapper.WenArticleMapper;
import com.shenmo.wen.app.core.article.mapper.WenArticleShareMapper;
import com.shenmo.wen.app.core.article.pojo.entity.WenArticle;
import com.shenmo.wen.app.core.comment.exception.CommentExceptionEnum;
import com.shenmo.wen.app.core.comment.mapper.WenCommentMapper;
import com.shenmo.wen.app.core.comment.pojo.domain.WenCommonLocation;
import com.shenmo.wen.app.core.comment.pojo.entity.WenComment;
import com.shenmo.wen.app.core.comment.pojo.param.WenCommentLoadParam;
import com.shenmo.wen.app.core.comment.pojo.param.WenCommentSaveParam;
import com.shenmo.wen.app.core.comment.pojo.vo.WenCommentVo;
import com.shenmo.wen.app.core.comment.service.WenCommentService;
import com.shenmo.wen.app.core.favorite.mapper.WenFavoriteMapper;
import com.shenmo.wen.app.core.favorite.pojo.entity.WenFavorite;
import com.shenmo.wen.app.core.interaction.mapper.WenInteractionMapper;
import com.shenmo.wen.app.core.interaction.pojo.entity.WenInteraction;
import com.shenmo.wen.app.core.notification.mapper.WenNotificationMapper;
import com.shenmo.wen.app.core.notification.pojo.entity.WenNotification;
import com.shenmo.wen.app.core.notification.pojo.vo.WenNotificationVo;
import com.shenmo.wen.app.core.pojo.param.WenSearchParam;
import com.shenmo.wen.common.constant.WebSocketMessageConstant;
import com.shenmo.wen.common.enumeration.ArticlePublishedScopeEnum;
import com.shenmo.wen.common.enumeration.InteractionActionEnum;
import com.shenmo.wen.common.enumeration.InteractionTargetEnum;
import com.shenmo.wen.common.enumeration.NotificationReceiveTypeEnum;
import com.shenmo.wen.common.enumeration.NotificationTypeEnum;
import com.shenmo.wen.common.util.AssertUtils;
import com.shenmo.wen.common.util.IpUtils;
import com.shenmo.wen.common.util.TiptapUtils;
import com.shenmo.wen.modules.user.mapper.WenUserMapper;
import com.shenmo.wen.modules.user.pojo.entity.WenUser;

import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.lang.NonNull;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Service
@RequiredArgsConstructor
public class WenCommentServiceImpl implements WenCommentService {
    private final WenCommentMapper mapper;
    private final WenUserMapper userMapper;
    private final WenArticleMapper articleMapper;
    private final WenInteractionMapper interactionMapper;
    private final WenFavoriteMapper favoriteMapper;
    private final WenNotificationMapper notificationMapper;
    private final SimpMessagingTemplate simpMessagingTemplate;
    private final WenArticleShareMapper articleShareMapper;

    @Transactional(rollbackFor = RuntimeException.class)
    @Override
    public Long save(WenCommentSaveParam param) {
        final Long articleId = param.getArticleId();
        final WenArticle article = articleMapper.byId(articleId);
        final Integer operationLevel = article.getOperationLevel();
        final long loginId = StpUtil.getLoginIdAsLong();
        final WenUser user = userMapper.byId(loginId);
        final Integer level = user.getLevel();
        AssertUtils.isTrue(operationLevel != -1 && level >= operationLevel,
                CommentExceptionEnum.COMMENT_LEVEL_PERMISSION);
        final WenComment comment = new WenComment();
        BeanUtils.copyProperties(param, comment);
        comment.setUserId(loginId);
        final String ip = IpUtils.getIp();
        comment.setIp(ip);
        comment.setIpLocation(IpUtils.getIpLocation(ip));
        comment.setPublishedAt(System.currentTimeMillis());
        final Long parentCommentId = param.getParentCommentId();
        if (Objects.nonNull(parentCommentId)) {
            AssertUtils.isTrue(mapper.existsById(parentCommentId), CommentExceptionEnum.COMMENT_NOT_EXISTS);
        }
        setMentionUser(comment);
        mapper.insert(comment);
        // 更新评论数
        articleMapper.incrementCommentCount(articleId);
        if (Objects.nonNull(parentCommentId)) {
            mapper.incrementReplyCount(parentCommentId);
        }
        simpMessagingTemplate.convertAndSend(WebSocketMessageConstant.TOPIC_COMMENTS, entityToVo(comment));
        // 发布通知
        publishNotification(user, comment, article);
        return comment.getId();
    }

    private void publishNotification(WenUser user, WenComment comment, WenArticle article) {
        final String publisher = user.getUsername();
        final WenNotification notification = new WenNotification();
        final Long userId = user.getId();
        notification.setUserId(userId);
        notification.setArticleId(article.getId());
        notification.setCommentId(comment.getId());
        notification
                .setContent(String.format("[%s] 在文章《%s》评论了：%s", publisher, article.getTitle(), comment.getContent()));

        // 设置评论通知类型为评论通知
        notification.setType(NotificationTypeEnum.PUBLISH_COMMENT.getCode());

        // 通知文章发布人、评论层主、评论提及人
        final Set<Long> commentNotificationUserIds = new HashSet<>();
        commentNotificationUserIds.add(article.getUserId());
        Optional.ofNullable(comment.getParentCommentId()).ifPresent(id -> {
            final WenComment parentComment = mapper.selectById(id);
            commentNotificationUserIds.add(parentComment.getUserId());
        });
        final String mentionUserId = comment.getMentionUserId();
        if (StringUtils.isNotBlank(mentionUserId)) {
            for (String mui : mentionUserId.split(",")) {
                commentNotificationUserIds.add(Long.valueOf(mui));
            }
        }

        // 查找收藏了父评论的用户
        if (comment.getParentCommentId() != null) {
            List<Long> favoriteUserIds = favoriteMapper.listUserIdByTargetId(
                    InteractionTargetEnum.COMMENT.getCode(), comment.getParentCommentId());
            commentNotificationUserIds.addAll(favoriteUserIds);
        }

        final String commentNotificationUserId = commentNotificationUserIds.stream().map(String::valueOf)
                .collect(Collectors.joining(","));
        notification.setCommentNotificationUserId(commentNotificationUserId);
        notificationMapper.insert(notification);
        final WenNotificationVo vo = new WenNotificationVo();
        BeanUtils.copyProperties(notification, vo);
        vo.setPublisher(user.getUsername());
        vo.setPublisherAvatar(user.getAvatar());

        // 文章是否公开
        boolean isPublic = article.getPublishedScope() == ArticlePublishedScopeEnum.PUBLIC.getCode();

        for (Long nuid : commentNotificationUserIds) {
            // 获取用户通知接收类型
            WenUser notifyUser = userMapper.byId(nuid);
            Integer notificationReceiveTypeCode = notifyUser.getNotificationReceiveType();
            // 默认为收藏类型
            if (notificationReceiveTypeCode == null) {
                notificationReceiveTypeCode = NotificationReceiveTypeEnum.FAVORITE.getCode();
            }
            // 转换为枚举类型
            NotificationReceiveTypeEnum notificationReceiveType = NotificationReceiveTypeEnum
                    .of(notificationReceiveTypeCode);

            // 如果用户关闭了通知，不发送
            if (notificationReceiveType == NotificationReceiveTypeEnum.CLOSE) {
                continue;
            }

            // 判断是否需要发送通知
            boolean shouldSendNotification;
            // 判断用户是否是被分享的用户
            boolean isSharedUser = false;
            if (article.getPublishedScope() == ArticlePublishedScopeEnum.PERSONAL.getCode()) {
                isSharedUser = articleShareMapper.isSharedToUser(article.getId(), nuid);
            }

            // 用户是文章作者、评论层主或被提及用户
            boolean isRelatedUser = Objects.equals(nuid, article.getUserId()) ||
                    (comment.getParentCommentId() != null &&
                            Objects.equals(nuid, mapper.selectById(comment.getParentCommentId()).getUserId()))
                    ||
                    (StringUtils.isNotBlank(mentionUserId) && mentionUserId.contains(nuid.toString()));

            // 用户是否为评论作者(自己发布的评论)
            boolean isCommentAuthor = Objects.equals(nuid, comment.getUserId());

            // 用户收藏了父评论
            boolean hasFavoriteParentComment = comment.getParentCommentId() != null &&
                    favoriteMapper.targetById(nuid,
                            InteractionTargetEnum.COMMENT.getCode(),
                            comment.getParentCommentId()) != null;

            // 用户是否收藏了文章
            boolean hasFavoriteArticle = favoriteMapper.targetById(nuid,
                    InteractionTargetEnum.ARTICLE.getCode(),
                    article.getId()) != null;

            // 根据通知接收类型判断是否发送通知
            shouldSendNotification = switch (notificationReceiveType) {
                // 关闭 - 不接收任何通知
                case CLOSE -> false;
                // 全部 - 对于评论，接收以下情况的通知：
                // 1. 自己发布的评论
                // 2. 自己文章下的评论
                // 3. 与自己相关的评论（被提及、回复自己的评论）
                // 4. 公开文章下的相关评论
                // 5. 分享给自己的文章下的相关评论
                // 6. 自己收藏的父评论的回复
                case ALL ->
                    isCommentAuthor || Objects.equals(nuid, article.getUserId()) ||
                            (isRelatedUser && (isPublic || isSharedUser)) ||
                            hasFavoriteParentComment;
                // 发布 - 对于评论，与全部相同，因为评论只有发布操作
                case PUBLISH ->
                    isCommentAuthor || Objects.equals(nuid, article.getUserId()) ||
                            (isRelatedUser && (isPublic || isSharedUser)) ||
                            hasFavoriteParentComment;
                // 修改 - 对于评论，无修改操作，但保留规则
                case MODIFY ->
                    isCommentAuthor || Objects.equals(nuid, article.getUserId()) ||
                            (isRelatedUser && (isPublic || isSharedUser)) ||
                            hasFavoriteParentComment;
                // 收藏 - 只接收收藏的文章下的评论通知和收藏的父评论的回复
                case FAVORITE ->
                    hasFavoriteArticle || hasFavoriteParentComment;
                // 分享 - 只接收被分享给自己的文章下的评论通知
                case SHARE -> isSharedUser;
            };

            // 发送通知
            if (shouldSendNotification) {
                // 直接WebSocket通知，不保存未读状态到Redis
                simpMessagingTemplate.convertAndSendToUser(String.valueOf(nuid), WebSocketMessageConstant.NOTIFICATIONS,
                        vo);
            }
        }
    }

    private void setMentionUser(WenComment comment) {
        final String content = comment.getContent();
        final List<String> mentionUsers = TiptapUtils.mentionUsers(content);
        final List<WenUser> users = userMapper.listIdUsernameByUsernames(mentionUsers);
        comment.setMentionUserId(
                users.stream().map(user -> String.valueOf(user.getId())).collect(Collectors.joining(",")));
        comment.setMentionUsername(users.stream().map(WenUser::getUsername).collect(Collectors.joining(",")));
    }

    @Override
    public List<WenCommentVo> load(WenCommentLoadParam param) {
        final Long articleId = param.getArticleId();
        AssertUtils.isTrue(articleMapper.existsById(articleId), ArticleExceptionEnum.ARTICLE_NOT_EXISTS);

        // 检查当前用户是否有权限查看该文章的评论
        final long loginId = StpUtil.getLoginIdAsLong();
        final WenArticle article = articleMapper.byId(articleId);
        // 如果文章是个人可见，检查是否是文章作者或被分享用户
        if (article.getPublishedScope() == ArticlePublishedScopeEnum.PERSONAL.getCode()) {
            boolean isOwner = Objects.equals(loginId, article.getUserId());
            boolean isSharedUser = articleShareMapper.isSharedToUser(articleId, loginId);
            AssertUtils.isTrue(isOwner || isSharedUser, ArticleExceptionEnum.ARTICLE_LOOK_PERMISSION);
        }

        final LambdaQueryWrapper<WenComment> lambdaQuery = Wrappers.<WenComment>lambdaQuery()
                .eq(WenComment::getArticleId, articleId);

        // 根据排序类型设置排序规则
        switch (param.getSortType()) {
            case 1 -> // 按时间排序
                lambdaQuery.orderByDesc(WenComment::getPublishedAt);
            case 2 -> // 按回复数排序
                lambdaQuery.orderByDesc(WenComment::getReplyCount);
            default -> // 默认按点赞数排序
                lambdaQuery.orderByDesc(WenComment::getLikeCount);
        }

        final Long parentCommentId = param.getParentCommentId();
        if (Objects.nonNull(parentCommentId)) {
            AssertUtils.isTrue(mapper.existsById(parentCommentId), CommentExceptionEnum.COMMENT_NOT_EXISTS);
            lambdaQuery.eq(WenComment::getParentCommentId, parentCommentId);
        } else {
            lambdaQuery.isNull(WenComment::getParentCommentId);
        }
        final Long id = param.getId();
        if (Objects.nonNull(id)) {
            lambdaQuery.gt(WenComment::getId, id);
        }
        lambdaQuery.last("limit " + param.getLoadSize());
        final int targetCode = InteractionTargetEnum.COMMENT.getCode();
        return entityToVoList(mapper.selectList(lambdaQuery), vo -> {
            final WenInteraction interaction = interactionMapper.targetById(loginId, targetCode, id);
            if (Objects.nonNull(interaction)) {
                switch (InteractionActionEnum.of(interaction.getActionType())) {
                    case LIKE -> vo.setIsLike(true);
                    case DISLIKE -> vo.setIsDislike(true);
                }
            }
            final WenFavorite favorite = favoriteMapper.targetById(loginId, targetCode, id);
            if (Objects.nonNull(favorite)) {
                vo.setIsFavorite(true);
            }
        });
    }

    @Override
    public WenCommentVo loadById(Long id) {
        return entityToVo(mapper.selectById(id));
    }

    @Override
    public List<WenCommentVo> parents(Long id) {
        List<WenComment> comments = new ArrayList<>();
        Long currentId = id;
        while (currentId != null) {
            WenComment comment = mapper.selectById(currentId);
            if (comment != null) {
                if (!currentId.equals(id)) {
                    comments.add(comment);
                }
                currentId = comment.getParentCommentId();
            } else {
                break;
            }
        }
        return entityToVoList(comments);
    }

    private List<WenCommentVo> entityToVoList(List<WenComment> wenComments, Consumer<WenCommentVo> voConsumer) {
        return wenComments.stream().map(comment -> {
            final WenCommentVo vo = entityToVo(comment);
            if (Objects.nonNull(voConsumer)) {
                voConsumer.accept(vo);
            }
            return vo;
        }).toList();
    }

    private List<WenCommentVo> entityToVoList(List<WenComment> wenComments) {
        return entityToVoList(wenComments, null);
    }

    private WenCommentVo entityToVo(WenComment comment) {
        final WenCommentVo commentVo = new WenCommentVo();
        BeanUtils.copyProperties(comment, commentVo);
        final long loginId = StpUtil.getLoginIdAsLong();
        final Long commentUserId = comment.getUserId();
        // 发布用户信息
        final WenUser user = userMapper.byId(commentUserId);
        commentVo.setPublisher(user.getUsername());
        commentVo.setPublisherAvatar(user.getAvatar());
        commentVo.setPublisherJob(user.getJob());
        commentVo.setIsOwner(Objects.equals(loginId, commentUserId));
        return commentVo;
    }

    @Override
    public List<WenCommentVo> search(WenSearchParam param) {
        final String searchKey = param.getSearchKey();
        final String tag = param.getTag();
        final Boolean owner = param.getOwner();
        final Boolean interaction = param.getInteraction();
        final Boolean favorite = param.getFavorite();
        if (StringUtils.isBlank(searchKey) && StringUtils.isBlank(tag)
                && Boolean.FALSE.equals(owner) && Boolean.FALSE.equals(interaction) && Boolean.FALSE.equals(favorite)) {
            return List.of();
        }
        final long loginId = StpUtil.getLoginIdAsLong();

        // 如果有标签筛选，先获取包含该标签的文章ID列表
        List<Long> tagArticleIds = null;
        if (StringUtils.isNotBlank(tag)) {
            // 使用FIND_IN_SET函数进行精确匹配
            LambdaQueryWrapper<WenArticle> articleQuery = Wrappers.<WenArticle>lambdaQuery()
                    .apply("FIND_IN_SET({0}, tag)", tag);

            tagArticleIds = articleMapper.selectList(articleQuery).stream()
                    .map(WenArticle::getId)
                    .collect(Collectors.toList());

            // 如果没有找到包含该标签的文章，直接返回空列表
            if (CollectionUtils.isEmpty(tagArticleIds)) {
                return List.of();
            }
        }

        final LambdaQueryWrapper<WenComment> lambdaQuery = Wrappers.<WenComment>lambdaQuery()
                .and(StringUtils.isNotBlank(searchKey), l -> l.like(WenComment::getContent, searchKey))
                .orderByAsc(WenComment::getCtTm);

        // 如果有标签筛选，只查询对应标签文章下的评论
        if (CollectionUtils.isNotEmpty(tagArticleIds)) {
            lambdaQuery.in(WenComment::getArticleId, tagArticleIds);
        }
        // 我的评论
        if (Boolean.TRUE.equals(owner)) {
            lambdaQuery.eq(WenComment::getUserId, loginId);
        }
        // 互动的评论
        if (Boolean.TRUE.equals(interaction)) {
            final List<Long> interactionCommentIds = new ArrayList<>(interactionMapper.listCommentIdByUserId(loginId));
            final List<Long> mentionCommentIds = mapper.listIdsByMentionUserId(loginId);
            interactionCommentIds.addAll(mentionCommentIds);
            if (CollectionUtils.isEmpty(interactionCommentIds)) {
                return List.of();
            }
            lambdaQuery.in(CollectionUtils.isNotEmpty(interactionCommentIds), WenComment::getId, interactionCommentIds);
        }
        // 收藏的评论
        if (Boolean.TRUE.equals(favorite)) {
            final List<Long> interactionCommentIds = favoriteMapper.listCommentIdByUserId(loginId);
            if (CollectionUtils.isEmpty(interactionCommentIds)) {
                return List.of();
            }
            lambdaQuery.in(CollectionUtils.isNotEmpty(interactionCommentIds), WenComment::getId, interactionCommentIds);
        }
        final List<WenComment> wenComments = mapper.selectList(lambdaQuery);
        return entityToVoList(wenComments);
    }

    @Override
    public WenCommonLocation location(Long id) {
        final WenCommonLocation commonLocation = new WenCommonLocation();
        final WenComment comment = mapper.selectById(id);
        final Long articleId = comment.getArticleId();

        // 检查当前用户是否有权限查看该文章的评论
        final long loginId = StpUtil.getLoginIdAsLong();
        final WenArticle article = articleMapper.byId(articleId);
        // 如果文章是个人可见，检查是否是文章作者或被分享用户
        if (article.getPublishedScope() == ArticlePublishedScopeEnum.PERSONAL.getCode()) {
            boolean isOwner = Objects.equals(loginId, article.getUserId());
            boolean isSharedUser = articleShareMapper.isSharedToUser(articleId, loginId);
            AssertUtils.isTrue(isOwner || isSharedUser, ArticleExceptionEnum.ARTICLE_LOOK_PERMISSION);
        }

        final Long parentCommentId = comment.getParentCommentId();
        final LambdaQueryWrapper<WenComment> lambdaQuery = Wrappers.<WenComment>lambdaQuery()
                .le(WenComment::getId, id)
                .eq(WenComment::getArticleId, articleId);
        parentWrapper(lambdaQuery, parentCommentId);
        final List<WenCommentVo> parents = parents(id);
        final List<WenComment> comments;
        commonLocation.setParents(parents);
        comments = mapper.selectList(lambdaQuery);
        final int limit = 10;
        if (CollectionUtils.isNotEmpty(comments) && comments.size() <= limit) {
            final List<WenComment> gt = mapper.selectList(parentWrapper(Wrappers.<WenComment>lambdaQuery()
                    .gt(WenComment::getId, id)
                    .eq(WenComment::getArticleId, articleId)
                    .last("limit " + limit), parentCommentId));
            comments.addAll(gt);
        }
        commonLocation.setComments(entityToVoList(comments));
        return commonLocation;
    }

    private LambdaQueryWrapper<WenComment> parentWrapper(LambdaQueryWrapper<WenComment> lambdaQuery,
            Long parentCommentId) {
        if (Objects.isNull(parentCommentId)) {
            lambdaQuery.isNull(WenComment::getParentCommentId);
        } else {
            lambdaQuery.eq(WenComment::getParentCommentId, parentCommentId);
        }
        return lambdaQuery;
    }

    @NonNull
    private String analyseMentionUserId(String text) {
        StringBuilder result = new StringBuilder();
        int index = 0;
        while (index < text.length()) {
            int atIndex = text.indexOf('@', index);
            if (atIndex == -1) {
                break;
            }
            int endIndex = text.indexOf(' ', atIndex);
            if (endIndex == -1) {
                endIndex = text.length();
            }
            String mention = text.substring(atIndex + 1, endIndex);
            String[] splitMentions = mention.split(",");
            for (String s : splitMentions) {
                if (!s.trim().isEmpty()) {
                    if (!result.isEmpty()) {
                        result.append(",");
                    }
                    result.append(s.trim());
                }
            }
            index = endIndex + 1;
        }
        return result.toString();
    }
}
