<template>
  <template v-for="button in visibleButtons" :key="button.tooltip">
    <TiptapBtn
      :icon="button.icon"
      :show="extensionsSet.has(button.extensionName)"
      :trigger="() => handleButtonClick(button)"
      :is-active="button.isActive?.(editor) || false"
      :tooltip="button.tooltip"
    />
  </template>
</template>

<script setup lang="ts">
import { computed } from 'vue'

import TiptapBtn from '@/components/tiptap/toolbar/TiptapBtn.vue'

import type { ToolbarButtonConfig } from '../configs/toolbarButtons'
import type { Editor } from '@tiptap/vue-3'

interface ModalType {
  [key: string]: unknown
}

interface Props {
  buttons: ToolbarButtonConfig[]
  editor: Editor
  extensionsSet: Set<string>
  showModal?: (title: string, trigger: () => void, onlyInputValue?: boolean) => void
  modal?: ModalType
}

const props = defineProps<Props>()
const emit = defineEmits<{
  'image-upload': []
  'toggle-fullscreen': []
}>()

// 计算可见的按钮
const visibleButtons = computed(() => {
  return props.buttons.filter((button) => props.extensionsSet.has(button.extensionName))
})

// 处理按钮点击
const handleButtonClick = (button: ToolbarButtonConfig) => {
  if (button.emit) {
    if (button.emit === 'image-upload') {
      emit('image-upload')
    } else if (button.emit === 'toggle-fullscreen') {
      emit('toggle-fullscreen')
    }
  } else {
    button.trigger(props.editor, props.showModal, props.modal)
  }
}
</script>
