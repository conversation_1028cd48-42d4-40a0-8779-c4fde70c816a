package com.shenmo.wen.app.core.notification.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shenmo.wen.common.mybatis.handler.TimestampToLongTypeHandler;
import org.apache.ibatis.type.JdbcType;
import lombok.Data;

/**
 * 通知用户已读关系实体类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@TableName(value = "wen_notification_user_read", autoResultMap = true)
public class WenNotificationUserRead {

    /**
     * 主键ID
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 通知ID
     */
    private Long notificationId;

    /**
     * 用户ID
     */
    private Long userId;

    /**
     * 创建时间
     * 使用数据库默认值
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long ctTm;
}