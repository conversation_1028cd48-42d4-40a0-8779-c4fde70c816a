package com.shenmo.wen.app.core.article.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Constants;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.shenmo.wen.app.core.article.pojo.entity.WenArticle;
import com.shenmo.wen.common.mybatis.handler.TimestampToLongTypeHandler;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Select;
import org.springframework.lang.NonNull;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface WenArticleMapper extends BaseMapper<WenArticle> {

    @NonNull
    default WenArticle byId(@NonNull Long id) {
        return selectById(id);
    }

    @NonNull
    default String titleById(@NonNull Long id) {
        return Optional.ofNullable(selectOne(Wrappers.<WenArticle>lambdaQuery()
                        .select(WenArticle::getTitle)
                        .eq(WenArticle::getId, id)))
                .map(WenArticle::getTitle)
                .orElse("");
    }

    default boolean existsById(@NonNull Long id) {
        return exists(Wrappers.<WenArticle>lambdaQuery().eq(WenArticle::getId, id));
    }


    default void incrementLikeCount(Long id) {
        update(Wrappers.<WenArticle>lambdaUpdate().eq(WenArticle::getId, id).setIncrBy(WenArticle::getLikeCount, 1));
    }


    default void incrementDislikeCount(Long id) {
        update(Wrappers.<WenArticle>lambdaUpdate().eq(WenArticle::getId, id).setIncrBy(WenArticle::getDislikeCount, 1));
    }


    default void incrementFavoriteCount(Long id) {
        update(Wrappers.<WenArticle>lambdaUpdate().eq(WenArticle::getId, id).setIncrBy(WenArticle::getFavoriteCount, 1));
    }


    default void incrementCommentCount(Long id) {
        update(Wrappers.<WenArticle>lambdaUpdate().eq(WenArticle::getId, id).setIncrBy(WenArticle::getCommentCount, 1));
    }


    default void decrementLikeCount(Long id) {
        update(Wrappers.<WenArticle>lambdaUpdate().eq(WenArticle::getId, id).gt(WenArticle::getLikeCount, 0).setDecrBy(WenArticle::getLikeCount, 1));
    }


    default void decrementDislikeCount(Long id) {
        update(Wrappers.<WenArticle>lambdaUpdate().eq(WenArticle::getId, id).gt(WenArticle::getDislikeCount, 0).setDecrBy(WenArticle::getDislikeCount, 1));
    }


    default void decrementFavoriteCount(Long id) {
        update(Wrappers.<WenArticle>lambdaUpdate().eq(WenArticle::getId, id).gt(WenArticle::getFavoriteCount, 0).setDecrBy(WenArticle::getFavoriteCount, 1));
    }


    default Integer favoriteCountById(Long id) {
        return selectOne(Wrappers.<WenArticle>lambdaQuery().select(WenArticle::getFavoriteCount).eq(WenArticle::getId, id)).getFavoriteCount();
    }

    default WenArticle likeAndDislikeCountById(Long id) {
        return selectOne(Wrappers.<WenArticle>lambdaQuery()
                .select(WenArticle::getLikeCount, WenArticle::getDislikeCount)
                .eq(WenArticle::getId, id));
    }

    default boolean existsByContent(Long id, String content) {
        return exists(Wrappers.<WenArticle>lambdaQuery().eq(WenArticle::getId, id).eq(WenArticle::getContent, content));
    }
}
