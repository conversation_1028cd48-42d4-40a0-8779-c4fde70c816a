package com.shenmo.wen.app.core.config;

import com.shenmo.wen.app.core.config.socket.WebSocketInterceptor;
import com.shenmo.wen.common.constant.WebSocketMessageConstant;

import org.springframework.context.annotation.Configuration;
import org.springframework.messaging.simp.config.MessageBrokerRegistry;
import org.springframework.web.socket.config.annotation.EnableWebSocketMessageBroker;
import org.springframework.web.socket.config.annotation.StompEndpointRegistry;
import org.springframework.web.socket.config.annotation.WebSocketMessageBrokerConfigurer;
/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Configuration
@EnableWebSocketMessageBroker
public class WebSocketConfiguration implements WebSocketMessageBrokerConfigurer {

    @Override
    public void configureMessageBroker(MessageBrokerRegistry config) {
        config.enableSimpleBroker(WebSocketMessageConstant.TOPIC, WebSocketMessageConstant.USER);
    }

    @Override
    public void registerStompEndpoints(StompEndpointRegistry registry) {
        registry.addEndpoint(WebSocketMessageConstant.PATH).setAllowedOriginPatterns("*").withSockJS().setInterceptors(new WebSocketInterceptor());
    }
}
