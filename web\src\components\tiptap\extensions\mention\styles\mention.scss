// Mention基础样式 - 统一的mention样式定义
%mention-base {
  display: inline-flex;
  align-items: center;
  background: var(--gray-2);
  border-radius: 0.2rem;
  padding: 0.15rem 0.3rem;
  margin: 0 0.1rem;
  gap: 0.3rem;
  text-decoration: none;
  cursor: pointer;
  transition: background-color 0.2s ease;
  white-space: nowrap;

  &:hover {
    background: var(--gray-3);
  }
}

%mention-avatar-base {
  max-width: initial;
  min-width: initial;
  display: inline-block;
  margin: 0;
  box-shadow: none;
  border-radius: 50%;
  height: 1.25rem;
  width: 1.25rem;
  vertical-align: middle;
  transition: none;
  order: 2;
  object-fit: cover;
  flex-shrink: 0;

  &:hover {
    transform: none;
  }
}

%mention-name-base {
  font-size: 0.85rem;
  color: var(--purple);
  order: 1;
  font-weight: 500;
  flex-shrink: 0;
}

// 应用基础样式到所有mention元素
.mention,
span[data-type='mention'] {
  @extend %mention-base;

  img,
  .mention-avatar {
    @extend %mention-avatar-base;
  }

  .mention-name {
    @extend %mention-name-base;
  }
}

// 确保mention元素在任何情况下都有正确的样式
span[data-type='mention'] {
  // 处理复制粘贴后的mention元素（简单文本形式）
  &:empty::before {
    content: '@' attr(data-label);
    font-size: 0.85rem;
    color: var(--purple);
    font-weight: 500;
  }

  // 确保contenteditable为false
  &[contenteditable='false'] {
    user-select: none;
  }
}

// ProseMirror编辑器内的mention样式继承基础样式
.ProseMirror {
  .mention,
  span[data-type='mention'] {
    @extend %mention-base;

    img,
    .mention-avatar {
      @extend %mention-avatar-base;
    }

    .mention-name {
      @extend %mention-name-base;
    }
  }
}

// 下拉菜单样式
.dropdown-menu {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow:
    0 0 0 1px rgba(0, 0, 0, 5%),
    0 2px 8px rgba(0, 0, 0, 10%);
  overflow: hidden;
  padding: 0.25rem;
  max-height: 15rem;
  overflow-y: auto;

  button {
    background: none;
    border: none;
    display: flex;
    align-items: center;
    border-radius: 0.25rem;
    gap: 0.5rem;
    width: 100%;
    text-align: left;
    cursor: pointer;
    transition: background-color 0.2s;

    &:hover,
    &.is-selected {
      background-color: rgba(0, 0, 0, 5%);
    }

    .dropdown-avatar {
      width: 1.5rem;
      height: 1.5rem;
      border-radius: 50%;
      object-fit: cover;
    }
  }

  .item {
    padding: 0.25rem;
    color: #888;
  }
}

// 暗色模式
.dark-theme {
  .dropdown-menu {
    background-color: var(--white-2);
    box-shadow:
      0 0 0 1px rgba(255, 255, 255, 5%),
      0 2px 8px rgba(0, 0, 0, 30%);

    button {
      &:hover,
      &.is-selected {
        background-color: rgba(255, 255, 255, 10%);
      }
    }

    .item {
      color: #aaa;
    }
  }
}
