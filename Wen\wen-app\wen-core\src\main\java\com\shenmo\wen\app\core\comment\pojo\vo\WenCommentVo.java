package com.shenmo.wen.app.core.comment.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.shenmo.wen.common.mybatis.handler.TimestampToLongTypeHandler;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class WenCommentVo {
    /**
     * 评论ID
     */
    private Long id;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 目标文章ID，用于关联对应的文章，表明该评论所属的文章，可为空（比如在一些特殊情况下可能暂未关联文章）。
     */
    private Long articleId;

    /**
     * 父评论ID，如果该评论是回复其他评论的，则记录被回复评论的ID，可为空，表示是直接对文章的评论，而非回复其他评论。
     */
    private Long parentCommentId;

    /**
     * 评论发布人
     */
    private String publisher;

    /**
     * 发布人头像URL
     */
    private String publisherAvatar;

    /**
     * 发布人职业
     */
    private String publisherJob;

    /**
     * 是否评论拥有着
     */
    private Boolean isOwner;

    /**
     * 评论发布时的IP地址
     */
    private String ipLocation;

    /**
     * 发布时间
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long publishedAt;

    /**
     * 点赞数，默认值为0
     */
    private Integer likeCount;
    private Boolean isLike = false;

    /**
     * 点踩数，默认值为0
     */
    private Integer dislikeCount;
    private Boolean isDislike = false;

    /**
     * 回复数，记录该评论的回复数量，默认值为0
     */
    private Integer replyCount;

    /**
     * 收藏数，默认值为0
     */
    private Integer favoriteCount;
    private Boolean isFavorite = false;
}
