package com.shenmo.wen.app.core.article.service;

import java.io.IOException;
import java.util.List;

import com.shenmo.wen.app.core.article.pojo.domain.WenHotTag;
import com.shenmo.wen.app.core.article.pojo.param.WenArticleSaveParam;
import com.shenmo.wen.app.core.article.pojo.param.WenArticleSearchParam;
import com.shenmo.wen.app.core.article.pojo.param.WenArticleUpdateParam;
import com.shenmo.wen.app.core.article.pojo.vo.WenArticleVo;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public interface WenArticleService {

    List<WenArticleVo> search(WenArticleSearchParam param);

    Long save(WenArticleSaveParam param);

    void edit(WenArticleUpdateParam param);

    String title(Long id);

    WenArticleVo detail(Long id);

    void download(Long id) throws IOException;

    /**
     * 切换文章发布范围
     * 
     * @param id 文章ID
     */
    void togglePublishedScope(Long id);

    /**
     * 获取热门标签
     * 
     * @param limit 返回的标签数量
     * @return 标签统计列表，按使用次数降序排序
     */
    List<WenHotTag> getHotTags(int limit);

    /**
     * 删除文章及其所有关联数据
     * 
     * @param id 文章ID
     */
    void delete(Long id);
}
