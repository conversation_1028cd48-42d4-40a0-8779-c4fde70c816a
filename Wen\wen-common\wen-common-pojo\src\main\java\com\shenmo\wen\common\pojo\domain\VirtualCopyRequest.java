package com.shenmo.wen.common.pojo.domain;

import jakarta.servlet.http.HttpServletRequest;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.catalina.connector.Connector;
import org.apache.catalina.connector.Request;
import org.apache.catalina.connector.RequestFacade;
import org.springframework.lang.Nullable;

import java.lang.reflect.Field;
import java.util.Collections;
import java.util.Enumeration;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 虚拟拷贝请求
 *
 * <AUTHOR>
 * @version 0.0.11
 */
@Slf4j
public class VirtualCopyRequest extends RequestFacade implements HttpServletRequest {

    /**
     * 虚拟拷贝请求头
     */
    @Getter
    private final Map<String, String> headers = new ConcurrentHashMap<>(32);

    /**
     * 虚拟拷贝原始请求
     */
    private final HttpServletRequest request;

    /**
     * 构造方法
     *
     * @param request {@link Request}
     * <AUTHOR>
     */
    public VirtualCopyRequest(@Nullable HttpServletRequest request) {

        super(determineRequest(request));
        this.request = request;
        // 初始化请求头
        initHeader();
    }

    /**
     * 确定请求{@link Request}
     *
     * @param request http servlet 请求
     * @return {@link Request}
     * <AUTHOR>
     */
    public static Request determineRequest(HttpServletRequest request) {
        if (request instanceof RequestFacade) {
            try {

                final Field requestField = RequestFacade.class.getDeclaredField("request");
                requestField.setAccessible(true);
                return (Request) requestField.get(request);
            } catch (Exception e) {
                log.error("determine request fail", e);
            }
        }
        return new Request(new Connector());
    }

    /**
     * 初始化请求头
     *
     * <AUTHOR>
     */
    private void initHeader() {

        final HttpServletRequest request = Objects.nonNull(this.request) ? this.request : super.request;
        final Enumeration<String> headerNames;
        try {
            headerNames = request.getHeaderNames();
        } catch (Exception ignored) {
            return;
        }
        while (headerNames.hasMoreElements()) {

            final String name = headerNames.nextElement();
            final String value = request.getHeader(name);
            setHeader(name, value);
        }
    }

    @Override
    public Enumeration<String> getHeaderNames() {

        return Collections.enumeration(getHeaders().keySet());
    }

    @Override
    public String getHeader(String name) {

        return headers.get(name.toLowerCase());
    }

    /**
     * 设置虚拟拷贝请求头
     *
     * @param name  请求头名称
     * @param value 请求头值
     * <AUTHOR>
     */
    public void setHeader(String name, String value) {

        headers.put(name.toLowerCase(), value);
    }
}
