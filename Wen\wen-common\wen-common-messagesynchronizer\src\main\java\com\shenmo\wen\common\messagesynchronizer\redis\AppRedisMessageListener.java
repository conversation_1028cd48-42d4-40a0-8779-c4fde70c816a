package com.shenmo.wen.common.messagesynchronizer.redis;

import com.shenmo.wen.common.messagesynchronizer.MessageData;
import com.shenmo.wen.common.util.spring.SpringEnvUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.lang.NonNull;

/**
 * 应用维度的redis消息监听器
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class AppRedisMessageListener<T extends MessageData<?>> extends AbstractRedisMessageListener<T> {

    /**
     * 构造方法
     *
     * @param redisTemplate redis操作模板
     * @param channel       监听通道
     * <AUTHOR>
     */
    public AppRedisMessageListener(@NonNull RedisTemplate<String, T> redisTemplate, String channel) {

        super(redisTemplate, processChannel(channel));
    }

    /**
     * 获取处理后的监听通道
     * <p>
     * 使用应用名称作为前缀
     *
     * @param channel 监听通道
     * @return 处理后的监听通道
     * <AUTHOR>
     */
    public static String processChannel(String channel) {
        final String appName = SpringEnvUtils.getConfig("spring.application.name");
        return StringUtils.isNotBlank(appName) ? appName + ":" + channel : channel;
    }
}
