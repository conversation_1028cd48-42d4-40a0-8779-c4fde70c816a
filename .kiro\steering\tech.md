# Technology Stack

## Frontend (web/)
- **Framework**: Vue 3 with Composition API
- **Build Tool**: Vite 6.0+ with TypeScript support
- **UI Library**: Naive UI components
- **Rich Text Editor**: TipTap with extensive extensions
- **State Management**: Pinia
- **Routing**: Vue Router 4
- **Styling**: SCSS with CSS modules
- **Real-time**: STOMP.js for WebSocket communication
- **Testing**: Vitest for unit tests, Playwright for e2e

## Backend (Wen/)
- **Framework**: Spring Boot (Java microservices)
- **Architecture**: Modular microservices with separate modules
- **Service Discovery**: Nacos
- **File Storage**: MinIO object storage
- **Database**: MySQL with custom schema (sm_wen.sql)

## Development Tools
- **Linting**: ESLint with TypeScript and Vue plugins
- **Formatting**: Prettier with strict import ordering
- **Style Linting**: Stylelint for CSS/SCSS
- **Git Hooks**: <PERSON><PERSON> with lint-staged
- **Commit Convention**: Conventional commits with commitlint

## Common Commands

### Frontend Development
```bash
# Development server
npm run dev

# Production build
npm run build

# Type checking
npm run type-check

# Code quality checks (runs all linting)
npm run code-check

# Testing
npm run test:unit

# Linting and formatting
npm run lint
npm run format
npm run stylelint
```

### Backend Services
```bash
# Start individual services
start-wen-core.bat
start-wen-gateway.bat  
start-wen-authentication.bat
```

## Key Dependencies
- **TipTap**: Extensive rich text editing with 20+ extensions
- **Axios**: HTTP client with custom request configuration
- **Tippy.js**: Tooltips and popovers
- **Vue Draggable Plus**: Drag and drop functionality
- **Lowlight**: Syntax highlighting for code blocks