<template>
  <BubbleMenu :tippy-options="tippyOptions" :editor="editor as Editor" :should-show="shouldShow">
    <div class="editor-bubble-menu" v-if="selectBubbleMenu.image"></div>
    <div class="editor-bubble-menu" v-else-if="selectBubbleMenu.bilibili"></div>
    <div class="editor-bubble-menu" v-else>
      <!-- 文本格式按钮 -->
      <ToolbarButtonGroup
        :buttons="bubbleMenuButtons.textFormat"
        :editor="editor"
        :extensions-set="extensionsSet"
        :show-modal="showModal"
      />

      <!-- 引用按钮 -->
      <ToolbarButtonGroup
        :buttons="[bubbleMenuButtons.other[0]]"
        :editor="editor"
        :extensions-set="extensionsSet"
        :show-modal="showModal"
      />

      <!-- 链接按钮 -->
      <TiptapBtn
        :icon="LinkOutlined"
        :show="extensionsSet.has('link')"
        :trigger="
          () =>
            showModal(
              '设置链接',
              () => {
                // 不在这里直接设置链接，而是在TipTapEditor.vue的handlePositiveClick中处理
                // 这里只需要扩展链接标记范围
                editor?.chain().focus().extendMarkRange('link').run()
              },
              true,
            )
        "
        :is-active="editor?.isActive('link')"
        tooltip="链接"
      />

      <!-- 颜色选择器 -->
      <ColorPicker
        :show="extensionsSet.has('color')"
        type="bubble-menu"
        :editor="editor"
        colorType="color"
      />
      <ColorPicker
        :show="extensionsSet.has('backgroundColor')"
        type="bubble-menu"
        :editor="editor"
        colorType="backgroundColor"
      />

      <!-- 对齐按钮 -->
      <ToolbarButtonGroup
        :buttons="bubbleMenuButtons.align"
        :editor="editor"
        :extensions-set="extensionsSet"
        :show-modal="showModal"
      />
    </div>
  </BubbleMenu>
</template>

<script lang="ts" setup>
import { BubbleMenu, Editor } from '@tiptap/vue-3'
import { LinkOutlined } from '@/icons'

import ColorPicker from '@/components/tiptap/extensions/color/ColorPicker.vue'
import ToolbarButtonGroup from '@/components/tiptap/toolbar/components/ToolbarButtonGroup.vue'
import { allButtonConfigs } from '@/components/tiptap/toolbar/configs/toolbarButtons'
import TiptapBtn from '@/components/tiptap/toolbar/TiptapBtn.vue'

// 气泡菜单专用的按钮配置（不包含某些不适合气泡菜单的按钮）
const bubbleMenuButtons = {
  textFormat: allButtonConfigs.textFormat,
  align: allButtonConfigs.align,
  other: [allButtonConfigs.other[0]], // 只包含引用按钮
}

interface Props {
  editor: Editor
  extensionsSet: Set<string>
  selectBubbleMenu: {
    image: boolean
    bilibili: boolean
  }
}

defineProps<Props>()

const emit = defineEmits(['show-modal'])

const tippyOptions = {
  duration: 100,
  appendTo: document.body,
}

const showModal = (title: string, trigger: () => void, onlyInputValue = false) => {
  emit('show-modal', { title, trigger, onlyInputValue })
}

interface BubbleMenuProps {
  editor: {
    isEditable: boolean
  }
  state: {
    selection: {
      empty: boolean
      node?: {
        type?: {
          name: string
        }
      }
      from: number
      to: number
    }
    doc: {
      nodesBetween: (
        from: number,
        to: number,
        callback: (node: { type: { name: string } }) => boolean | void,
      ) => void
    }
  }
}

const shouldShow = (props: BubbleMenuProps) => {
  const { editor, state } = props
  const { selection } = state

  // 如果选择是空的(只有光标)，不显示
  if (selection.empty) return false

  // 如果编辑器不可编辑，不显示
  if (!editor.isEditable) return false

  // 如果当前选中的是图片、bilibili或代码块节点，不显示气泡菜单
  const node = selection.node
  if (
    node?.type?.name === 'image' ||
    node?.type?.name === 'bilibili' ||
    node?.type?.name === 'codeBlock'
  )
    return false

  // 检查选择范围内是否包含代码块
  const { from, to } = selection
  let hasCodeBlock = false
  state.doc.nodesBetween(from, to, (node: { type: { name: string } }) => {
    if (node.type.name === 'codeBlock') {
      hasCodeBlock = true
      return false // 停止遍历
    }
  })

  if (hasCodeBlock) return false

  // 显示选择菜单
  return true
}
</script>
