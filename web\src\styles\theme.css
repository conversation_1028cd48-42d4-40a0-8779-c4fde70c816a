/* 暗色主题相关样式 */
.dark-theme {
  /* 覆盖亮色主题的CSS变量 */
  --white: #121212;
  --white-1: #242424;
  --white-2: #363636;
  --creamy-white-1: #1a1a1a;
  --creamy-white-2: #262626;
  --creamy-white-3: #333;
  --black: #e0e0e0;
  --black-contrast: #fff;
  --gray-1: rgba(200, 200, 200, 5%);
  --gray-2: rgba(200, 200, 200, 8%);
  --gray-3: rgba(200, 200, 200, 12%);
  --gray-4: rgba(200, 200, 200, 30%);
  --gray-5: rgba(200, 200, 200, 60%);
  --green: #22c55e;
  --blue: #4ba3fd;
  --blue-light: #2a3745;
  --purple: #9d6dff;
  --purple-contrast: #8a5cf5;
  --purple-light: rgba(154, 92, 255, 15%);
  --yellow-contrast: #facc15;
  --yellow: rgba(250, 204, 21, 40%);
  --yellow-light: #3f3a14;
  --red: #ff5c33;
  --red-light: #3d1a12;
  --border-1: 0.1rem solid var(--gray-3);
  --shadow: 0 0.25rem 0.6rem rgba(255, 255, 255, 10%);

  /* 评论组件颜色变量 */
  --comment-info-bg: var(--white-1);
  --comment-list-bg: var(--white-1);
  --comment-container-bg: var(--white-2);
  --comment-fixed-bg: var(--blue-light);
  --comment-input-bg: var(--white-1);
  --comment-reply-bg: var(--white-2);
  --comment-reply-btn-bg: var(--white-2);
  --comment-fixed-reply-bg: var(--blue-light);
  --comment-fixed-reply-btn-bg: var(--blue-light);
  --prosemirror-input-bg: var(--white-2);
  --comment-input-prosemirror-bg: var(--white-1);
  --comment-reply-prosemirror-bg: var(--white-2);
  --comment-fixed-prosemirror-bg: var(--blue-light);
}

/* 自定义一些特定组件在暗色模式下的样式 */
.dark-theme .article-content {
  background-color: var(--white-1);
  color: var(--black);
}

.dark-theme .card-item {
  background-color: var(--white-2);
}

.dark-theme .article-title {
  color: var(--black);
}

.dark-theme .article-title:hover {
  color: var(--blue);
}

.dark-theme .comment-danmaku-item {
  color: var(--black);
}

/* 文章详情页深色模式样式 */
.dark-theme .article-layout {
  background-color: var(--creamy-white-1);
}

.dark-theme .article-info-container .article-header {
  color: var(--black);
}

.dark-theme .article-info-container .article-content {
  background-color: var(--white-1);
}

/* ProseMirror编辑器在深色模式下的样式 */
.dark-theme .ProseMirror {
  color: var(--black);
}

.dark-theme .ProseMirror h1,
.dark-theme .ProseMirror h2,
.dark-theme .ProseMirror h3,
.dark-theme .ProseMirror h4,
.dark-theme .ProseMirror h5,
.dark-theme .ProseMirror h6 {
  color: var(--black);
}

.dark-theme .ProseMirror a {
  color: var(--blue);
}

.dark-theme .ProseMirror blockquote {
  border-left-color: var(--gray-3);
  background-color: var(--white-2);
}

.dark-theme .ProseMirror pre {
  background-color: var(--white-2);
}

.dark-theme .ProseMirror code {
  background-color: var(--white-2);
  color: var(--black);
}

/* 评论组件深色模式样式 */
.dark-theme .comment-info-container {
  background-color: var(--comment-info-bg);
  color: var(--black);
}

/* 确保评论列表容器背景色与评论信息容器一致 */
.dark-theme .comment-list-container {
  background-color: var(--comment-list-bg);
}

/* 普通评论容器背景色需要保持原有的白色2，在深色模式下有足够的对比度 */
.dark-theme .user-comment-container {
  background-color: var(--comment-container-bg);
  border-color: var(--gray-3);
}

.dark-theme .user-comment-container-fixed,
.dark-theme .comment-flash {
  background-color: var(--comment-fixed-bg);
  border-color: var(--gray-3);
  opacity: 1; /* 确保不透明 */
}

.dark-theme .user-nickname,
.dark-theme .user-extra-info,
.dark-theme .comment-content-row,
.dark-theme .comment-interaction-reply {
  color: var(--black);
}

/* 评论内容应该与评论容器颜色一致 */
.dark-theme .user-comment-container .comment-content-row .ProseMirror,
.dark-theme .user-comment-container .comment-content-row .tiptap-editor-wrapper,
.dark-theme .user-comment-container .comment-content-row .editor-content,
.dark-theme .user-comment-container .comment-content-row p,
.dark-theme .user-comment-container .comment-content-row blockquote {
  background-color: var(--comment-container-bg);
}

/* 固定评论内容应该与固定评论容器颜色一致 */
.dark-theme .user-comment-container-fixed .comment-content-row .ProseMirror,
.dark-theme .user-comment-container-fixed .comment-content-row .tiptap-editor-wrapper,
.dark-theme .user-comment-container-fixed .comment-content-row .editor-content,
.dark-theme .user-comment-container-fixed .comment-content-row p,
.dark-theme .user-comment-container-fixed .comment-content-row blockquote,
.dark-theme .comment-flash .comment-content-row .ProseMirror,
.dark-theme .comment-flash .comment-content-row .tiptap-editor-wrapper,
.dark-theme .comment-flash .comment-content-row .editor-content,
.dark-theme .comment-flash .comment-content-row p,
.dark-theme .comment-flash .comment-content-row blockquote {
  background-color: var(--comment-fixed-bg);
  opacity: 1; /* 确保不透明 */
}

/* 评论输入行应与评论列表容器颜色一致 */
.dark-theme .comment-input-row,
.dark-theme .comment-reply-row {
  border-color: var(--gray-3);
}

/* 确保普通评论的回复框继承普通评论容器的背景色 */
.dark-theme .user-comment-container .comment-reply-row {
  background-color: var(--comment-reply-bg);
}

.dark-theme .user-comment-container .comment-reply-send-btn {
  background-color: var(--comment-reply-btn-bg);
}

/* 确保固定评论中的回复框在深色模式下也保持蓝色背景 */
.dark-theme .user-comment-container-fixed .comment-reply-row,
.dark-theme .comment-flash .comment-reply-row {
  background-color: var(--comment-fixed-reply-bg);
  border-color: var(--gray-3);
  opacity: 1; /* 确保不透明 */
}

/* 确保固定评论中的回复框中的编辑器也保持蓝色背景 */
.dark-theme .user-comment-container-fixed .comment-reply-row .tiptap-editor-wrapper,
.dark-theme .user-comment-container-fixed .comment-reply-row .editor-content,
.dark-theme .comment-flash .comment-reply-row .tiptap-editor-wrapper,
.dark-theme .comment-flash .comment-reply-row .editor-content {
  background-color: var(--comment-fixed-reply-bg);
  opacity: 1; /* 确保不透明 */
}

/* 确保固定评论中的按钮区域也保持蓝色背景 */
.dark-theme .user-comment-container-fixed .comment-reply-send-btn,
.dark-theme .comment-flash .comment-reply-send-btn {
  background-color: var(--comment-fixed-reply-btn-bg);
  opacity: 1; /* 确保不透明 */
}

/* 确保普通评论中的编辑器内容区域保持普通评论容器的背景色 */
.dark-theme .user-comment-container .comment-reply-row .tiptap-editor-wrapper,
.dark-theme .user-comment-container .comment-reply-row .editor-content {
  background-color: var(--comment-reply-bg);
}

/* 确保普通评论中的输入框有合适的背景色 */
.dark-theme .user-comment-container .comment-reply-row .ProseMirrorInput {
  background-color: var(--comment-reply-prosemirror-bg);
  border-color: var(--gray-4);
  color: var(--black);
}

/* 为暗色模式下的评论输入框添加更明显的边框和对比度 */
.dark-theme .comment-input-row {
  border-top: 1px solid var(--gray-4);
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 20%);
  background-color: var(--comment-input-bg);
}

/* 主评论输入框的编辑器背景色也需要与容器一致 */
.dark-theme .comment-input-row .tiptap-editor-wrapper,
.dark-theme .comment-input-row .editor-content {
  background-color: var(--comment-input-bg);
}

.dark-theme .comment-input-row .ProseMirrorInput {
  background-color: var(--comment-input-prosemirror-bg);
  border-color: var(--gray-3);
  color: var(--black);
}

/* 固定评论的输入框 */
.dark-theme .user-comment-container-fixed .comment-reply-row .ProseMirrorInput,
.dark-theme .comment-flash .comment-reply-row .ProseMirrorInput {
  background-color: var(--comment-fixed-prosemirror-bg);
  border-color: var(--gray-4);
  color: var(--black);
}

/* 为暗色模式中的快速回复框增加视觉分隔 */
.dark-theme .comment-reply-row {
  border-top: 1px solid var(--gray-3);
  margin-top: 0.5rem;
}

.dark-theme .comment-title-container {
  color: var(--black);
}

.dark-theme .breadcrumb-text {
  color: var(--blue);
}

.dark-theme .comment-flash {
  background-color: var(--blue-light);
}

/* 输入框和滚动条在深色模式下的样式 */
.dark-theme .n-input {
  background-color: var(--white-2);
  color: var(--black);
}

.dark-theme .n-scrollbar-rail {
  background-color: var(--white-2);
}

.dark-theme .n-scrollbar-thumb {
  background-color: var(--gray-4);
}

/* TipTap编辑器在深色模式下的样式 */
.dark-theme .ProseMirrorInput {
  background-color: var(--prosemirror-input-bg);
  border-color: var(--gray-3);
  color: var(--black);
}

/* 全屏模式下编辑器的暗色主题 */
.dark-theme .tiptap-fullscreen {
  background-color: var(--white-2);
  color: var(--black);
}

/* 全屏模式下编辑器内容区的暗色主题 */
.dark-theme .tiptap-fullscreen .editor-content {
  background-color: var(--white-2);
}

/* 确保评论输入区域的TipTap编辑器背景与容器一致 */
.dark-theme .comment-input-row .tiptap-editor-wrapper .editor-content,
.dark-theme .comment-reply-row .tiptap-editor-wrapper .editor-content {
  background-color: var(--white-1);
}

/* 确保评论内容的TipTap编辑器背景与评论容器一致 */
.dark-theme .comment-content-row .tiptap-editor-wrapper .editor-content {
  background-color: var(--white-2);
}

/* 确保编辑器内容区的背景色与其父容器一致 */
.dark-theme .tiptap-editor-wrapper .editor-content {
  background-color: transparent;
}

.dark-theme .tiptap-toolbar {
  background-color: var(--white-2);
  border-color: var(--gray-3);
}

.dark-theme .tiptap-toolbar button {
  color: var(--black);
}

.dark-theme .tiptap-toolbar button:hover {
  background-color: var(--gray-3);
}

/* 编辑器字数计数器颜色 */
.dark-theme .character-count {
  color: var(--gray-5);
}

/* 首页样式在深色模式下 */
.dark-theme .common-layout-top {
  background-color: var(--creamy-white-2);
  color: var(--black);
}

.dark-theme .common-layout-content {
  background-color: var(--creamy-white-1);
}

.dark-theme .online-info,
.dark-theme .search-container {
  color: var(--black);
}

.dark-theme .n-tabs .n-tab {
  color: var(--black);
}

.dark-theme .n-tabs .n-tab--active {
  color: var(--blue);
}

.dark-theme .n-tabs-tab-wrapper {
  border-bottom-color: var(--gray-3);
}

/* 用户信息弹框在深色模式下的样式 */
.dark-theme .user-info {
  background-color: var(--white-2);
  color: var(--black);
}

.dark-theme .info-row {
  border-color: var(--gray-3);
}

/* 深色主题下的开关样式 */
.dark-theme .n-switch {
  --n-rail-color: var(--white-1);
  --n-rail-color-active: var(--blue);
}

/* 确保所有卡片和容器在深色模式下有正确的背景色 */
.dark-theme .n-card {
  background-color: var(--white-2);
  color: var(--black);
}

.dark-theme .n-card-header {
  border-bottom-color: var(--gray-3);
}

.dark-theme .n-modal {
  background-color: var(--white-2);
  color: var(--black);
}

.dark-theme .n-drawer-content {
  background-color: var(--white-2);
  color: var(--black);
}

.dark-theme .n-dialog {
  background-color: var(--white-2);
  color: var(--black);
}

/* 对话框标题在暗色模式下的样式 */
.dark-theme .n-dialog__title {
  color: var(--black-contrast);
}

.dark-theme .n-popover {
  background-color: var(--white-2);
  color: var(--black);
}

.dark-theme .n-dropdown-menu {
  background-color: var(--white-2);
  color: var(--black);
}

.dark-theme .n-dropdown-option {
  color: var(--black);
}

.dark-theme .n-dropdown-option:hover {
  background-color: var(--gray-3);
}

/* 按钮和交互元素在深色模式下 */
.dark-theme .n-button--ghost {
  border-color: var(--gray-3);
  color: var(--black);
}

.dark-theme .n-button--ghost:hover {
  border-color: var(--blue);
  color: var(--blue);
}

.dark-theme .n-button--text {
  color: var(--blue);
}

/* 确保登录页面在深色模式下正确显示 */
.dark-theme .layout-container {
  background-color: var(--creamy-white-3);
}

.dark-theme .login-form-ipt,
.dark-theme .register-form-ipt {
  background-color: var(--white-1);
  color: var(--black);
}

/* 通知样式在深色模式下的调整 */
.dark-theme .notification-table .n-data-table-th {
  background-color: var(--white-1);
  color: var(--black-contrast);
}

.dark-theme .notification-table .n-data-table-td {
  background-color: #2c2c2c; /* 自定义介于creamy-white-2(#262626)和creamy-white-3(#333333)之间的颜色 */
  color: var(--black-contrast);
}

.dark-theme .notification-table .n-data-table__pagination {
  background-color: var(--creamy-white-2);
}

/* 确保通知右上角按钮和弹出内容在暗色模式下正确显示 */
.dark-theme .notification-container .notification-btn {
  color: var(--black);
}

.dark-theme .n-badge .n-badge-sup {
  background-color: var(--red);
  color: var(--black-contrast);
}

/* 通知消息的暗色模式样式 */
.dark-theme .n-notification {
  background-color: var(--white-2);
  color: var(--black);
  border: 1px solid var(--gray-3);
}

.dark-theme .n-notification .n-notification-main .n-notification-main__content {
  color: var(--black);
}

.dark-theme .n-notification .n-notification-main .n-notification-main__title {
  color: var(--blue);
}

.dark-theme .n-notification .n-notification-main .n-notification-main__avatar .n-avatar {
  border: 1px solid var(--gray-3);
}

.dark-theme .n-notification .n-notification-main .n-notification-main__action .n-button {
  color: var(--blue);
}

.dark-theme .n-notification .n-notification-main .n-notification-main__close {
  color: var(--gray-5);
}

.dark-theme .n-notification .n-notification-main .n-notification-main__close:hover {
  color: var(--black);
}

/* 删除所有重复的和使用!important的规则 */
