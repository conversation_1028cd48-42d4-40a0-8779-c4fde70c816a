<template>
  <div class="dropdown-menu">
    <template v-if="items.length">
      <button
        :class="{ 'is-selected': index === selectedIndex }"
        v-for="(item, index) in items"
        :key="index"
        @click="selectItem(index)"
      >
        <img
          v-if="item.avatar"
          :src="fileApi.getResourceURL(item.avatar)"
          alt="avatar"
          class="dropdown-avatar"
        />
        <span>{{ item.username }}</span>
      </button>
    </template>
    <div class="item" v-else>...</div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch } from 'vue'

import fileApi from '@/api/file'
import type { SearchUser } from '@/types/user.types'
import '@/components/tiptap/extensions/mention/styles/mention.scss'
const props = defineProps<{
  items: SearchUser[]
  command: (item: { id: string; label: string; avatar?: string }) => void
}>()

const selectedIndex = ref(0)

// 监听 items 变化，重置 selectedIndex
watch(
  () => props.items,
  () => {
    selectedIndex.value = 0
  },
)

// 键盘事件处理
const onKeyDown = ({ event }: { event: KeyboardEvent }) => {
  if (event.key === 'ArrowUp') {
    upHandler()
    return true
  }

  if (event.key === 'ArrowDown') {
    downHandler()
    return true
  }

  if (event.key === 'Enter') {
    enterHandler()
    return true
  }

  return false
}

// 上箭头处理
const upHandler = () => {
  selectedIndex.value = (selectedIndex.value + props.items.length - 1) % props.items.length
}

// 下箭头处理
const downHandler = () => {
  selectedIndex.value = (selectedIndex.value + 1) % props.items.length
}

// 回车处理
const enterHandler = () => {
  selectItem(selectedIndex.value)
}

// 选择项处理
const selectItem = (index: number) => {
  const item: SearchUser = props.items[index] as SearchUser
  if (item) {
    const commandData = { id: item.id, label: item.username, avatar: item.avatar }
    props.command(commandData)
  }
}

// 暴露 onKeyDown 方法
defineExpose({
  onKeyDown,
})
</script>
