package com.shenmo.wen.app.authentication.service;

import com.shenmo.wen.app.authentication.pojo.param.LoginParam;
import com.shenmo.wen.app.authentication.pojo.param.RegisterParam;
import com.shenmo.wen.modules.user.pojo.vo.WenUserVo;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface WenAuthenticationService {
    WenUserVo login(LoginParam param);

    WenUserVo register(RegisterParam param);

    void logout();
}
