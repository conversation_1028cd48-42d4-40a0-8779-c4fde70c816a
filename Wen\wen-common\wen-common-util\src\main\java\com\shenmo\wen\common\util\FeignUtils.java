package com.shenmo.wen.common.util;

import com.shenmo.wen.common.pojo.response.ResponseData;
import feign.FeignException;
import org.apache.commons.lang3.StringUtils;

import java.nio.ByteBuffer;
import java.util.Objects;

/**
 * feign调用相关工具
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public abstract class FeignUtils {


    /**
     * 默认错误信息
     */
    private static final String DEFAULT_ERROR_MESSAGE = "服务调用失败";


    /**
     * 解析feign响应
     *
     * @param feignException 异常信息
     * @return 响应数据
     * <AUTHOR>
     */
    public static ResponseData<?> parseFeignResponse(FeignException feignException) {

        if (Objects.isNull(feignException)) {
            return ResponseData.error(DEFAULT_ERROR_MESSAGE);
        }
        final String body = new String(feignException.responseBody().orElse(ByteBuffer.wrap(new byte[]{})).array());
        return StringUtils.isBlank(body) || !JacksonUtils.isValid(body)
                ? ResponseData.error(DEFAULT_ERROR_MESSAGE)
                : JacksonUtils.fromJson(body, ResponseData.class);
    }

    /**
     * 寻找异常栈中是否存在feign异常
     *
     * @param throwable 异常信息
     * @return feign异常
     * <AUTHOR>
     */
    public static FeignException findFeignException(Throwable throwable) {

        if (Objects.isNull(throwable)) {

            return null;
        }
        return throwable instanceof FeignException
                ? (FeignException) throwable
                : findFeignException(throwable.getCause());
    }

    /**
     * 获取feign异常信息
     *
     * @param throwable 异常
     * @return 异常信息
     * <AUTHOR>
     */
    public static String getFeignMessage(Throwable throwable) {

        return FeignUtils.parseFeignResponse(FeignUtils.findFeignException(throwable)).getMessage();
    }
}
