import { ref, computed } from 'vue'
import type { MenuPosition } from '../types'

interface ViewportInfo {
  width: number
  height: number
  scrollX: number
  scrollY: number
}

interface ElementBounds {
  top: number
  left: number
  right: number
  bottom: number
  width: number
  height: number
}

export function useMenuPositioning() {
  // Default spacing from target element
  const DEFAULT_OFFSET = 8
  const MOBILE_OFFSET = 12
  const EDGE_PADDING = 16

  /**
   * Get current viewport information
   */
  const getViewportInfo = (): ViewportInfo => {
    return {
      width: window.innerWidth,
      height: window.innerHeight,
      scrollX: window.scrollX,
      scrollY: window.scrollY
    }
  }

  /**
   * Get element bounds relative to viewport
   */
  const getElementBounds = (element: HTMLElement): ElementBounds => {
    const rect = element.getBoundingClientRect()
    return {
      top: rect.top,
      left: rect.left,
      right: rect.right,
      bottom: rect.bottom,
      width: rect.width,
      height: rect.height
    }
  }

  /**
   * Check if device is mobile based on viewport width
   */
  const isMobileDevice = computed(() => {
    return window.innerWidth <= 768
  })

  /**
   * Calculate optimal menu position with intelligent placement
   */
  const calculatePosition = (
    targetElement: HTMLElement,
    menuElement: HTMLElement,
    preferredPlacement: 'auto' | 'left' | 'right' = 'auto'
  ): MenuPosition => {
    try {
      const viewport = getViewportInfo()
      const targetBounds = getElementBounds(targetElement)
      const menuBounds = getElementBounds(menuElement)
      
      const offset = isMobileDevice.value ? MOBILE_OFFSET : DEFAULT_OFFSET

      // Calculate available space in each direction
      const spaceAbove = targetBounds.top
      const spaceBelow = viewport.height - targetBounds.bottom
      const spaceLeft = targetBounds.left
      const spaceRight = viewport.width - targetBounds.right

      let placement: 'top' | 'bottom' | 'left' | 'right' = 'bottom'
      let x = targetBounds.left
      let y = targetBounds.bottom + offset

      // Determine optimal placement based on available space
      if (preferredPlacement === 'auto') {
        // Prefer bottom placement, but switch if not enough space
        if (spaceBelow >= menuBounds.height + offset) {
          placement = 'bottom'
          y = targetBounds.bottom + offset
        } else if (spaceAbove >= menuBounds.height + offset) {
          placement = 'top'
          y = targetBounds.top - menuBounds.height - offset
        } else if (spaceRight >= menuBounds.width + offset) {
          placement = 'right'
          x = targetBounds.right + offset
          y = targetBounds.top
        } else if (spaceLeft >= menuBounds.width + offset) {
          placement = 'left'
          x = targetBounds.left - menuBounds.width - offset
          y = targetBounds.top
        } else {
          // Fallback to bottom with viewport adjustment
          placement = 'bottom'
          y = targetBounds.bottom + offset
        }
      } else if (preferredPlacement === 'left') {
        if (spaceLeft >= menuBounds.width + offset) {
          placement = 'left'
          x = targetBounds.left - menuBounds.width - offset
          y = targetBounds.top
        } else {
          // Fallback to right
          placement = 'right'
          x = targetBounds.right + offset
          y = targetBounds.top
        }
      } else if (preferredPlacement === 'right') {
        if (spaceRight >= menuBounds.width + offset) {
          placement = 'right'
          x = targetBounds.right + offset
          y = targetBounds.top
        } else {
          // Fallback to left
          placement = 'left'
          x = targetBounds.left - menuBounds.width - offset
          y = targetBounds.top
        }
      }

      return {
        x,
        y,
        placement,
        offset: { x: 0, y: 0 }
      }

    } catch (error) {
      console.error('Failed to calculate menu position:', error)
      
      // Fallback to simple positioning
      const rect = targetElement.getBoundingClientRect()
      return {
        x: rect.left,
        y: rect.bottom + DEFAULT_OFFSET,
        placement: 'bottom',
        offset: { x: 0, y: 0 }
      }
    }
  }

  /**
   * Adjust position to stay within viewport boundaries
   */
  const adjustForViewport = (position: MenuPosition, menuElement: HTMLElement): MenuPosition => {
    try {
      const viewport = getViewportInfo()
      const menuBounds = getElementBounds(menuElement)
      
      let { x, y } = position
      const offset = { x: 0, y: 0 }

      // Adjust horizontal position
      if (x + menuBounds.width > viewport.width - EDGE_PADDING) {
        // Menu extends beyond right edge
        const overflow = (x + menuBounds.width) - (viewport.width - EDGE_PADDING)
        x -= overflow
        offset.x = -overflow
      }
      
      if (x < EDGE_PADDING) {
        // Menu extends beyond left edge
        const underflow = EDGE_PADDING - x
        x += underflow
        offset.x = underflow
      }

      // Adjust vertical position
      if (y + menuBounds.height > viewport.height - EDGE_PADDING) {
        // Menu extends beyond bottom edge
        const overflow = (y + menuBounds.height) - (viewport.height - EDGE_PADDING)
        y -= overflow
        offset.y = -overflow
      }
      
      if (y < EDGE_PADDING) {
        // Menu extends beyond top edge
        const underflow = EDGE_PADDING - y
        y += underflow
        offset.y = underflow
      }

      return {
        ...position,
        x,
        y,
        offset
      }

    } catch (error) {
      console.error('Failed to adjust menu position for viewport:', error)
      return position
    }
  }

  /**
   * Get responsive position optimized for mobile devices
   */
  const getResponsivePosition = (
    targetElement: HTMLElement,
    menuElement: HTMLElement,
    isMobile: boolean = isMobileDevice.value
  ): MenuPosition => {
    try {
      if (isMobile) {
        // Mobile-specific positioning strategy
        const viewport = getViewportInfo()
        const targetBounds = getElementBounds(targetElement)
        
        // On mobile, prefer centering the menu horizontally
        // and positioning it below the target with more spacing
        const menuBounds = getElementBounds(menuElement)
        
        let x = Math.max(
          EDGE_PADDING,
          Math.min(
            targetBounds.left + (targetBounds.width - menuBounds.width) / 2,
            viewport.width - menuBounds.width - EDGE_PADDING
          )
        )
        
        let y = targetBounds.bottom + MOBILE_OFFSET
        let placement: 'top' | 'bottom' | 'left' | 'right' = 'bottom'

        // Check if menu fits below target
        if (y + menuBounds.height > viewport.height - EDGE_PADDING) {
          // Try positioning above
          const aboveY = targetBounds.top - menuBounds.height - MOBILE_OFFSET
          if (aboveY >= EDGE_PADDING) {
            y = aboveY
            placement = 'top'
          } else {
            // Keep below but adjust to fit
            y = viewport.height - menuBounds.height - EDGE_PADDING
          }
        }

        return {
          x,
          y,
          placement,
          offset: { x: 0, y: 0 }
        }
      } else {
        // Desktop positioning
        return calculatePosition(targetElement, menuElement, 'auto')
      }

    } catch (error) {
      console.error('Failed to calculate responsive position:', error)
      return calculatePosition(targetElement, menuElement, 'auto')
    }
  }

  /**
   * Get position for click coordinates (when clicking directly)
   */
  const getPositionFromClick = (
    clickX: number,
    clickY: number,
    menuElement: HTMLElement
  ): MenuPosition => {
    try {
      const viewport = getViewportInfo()
      const menuBounds = getElementBounds(menuElement)
      
      let x = clickX
      let y = clickY + DEFAULT_OFFSET
      let placement: 'top' | 'bottom' | 'left' | 'right' = 'bottom'

      // Adjust for viewport boundaries
      if (x + menuBounds.width > viewport.width - EDGE_PADDING) {
        x = clickX - menuBounds.width
        placement = 'left'
      }
      
      if (y + menuBounds.height > viewport.height - EDGE_PADDING) {
        y = clickY - menuBounds.height - DEFAULT_OFFSET
        placement = 'top'
      }

      // Ensure minimum padding from edges
      x = Math.max(EDGE_PADDING, Math.min(x, viewport.width - menuBounds.width - EDGE_PADDING))
      y = Math.max(EDGE_PADDING, Math.min(y, viewport.height - menuBounds.height - EDGE_PADDING))

      return {
        x,
        y,
        placement,
        offset: { x: 0, y: 0 }
      }

    } catch (error) {
      console.error('Failed to calculate position from click:', error)
      
      return {
        x: clickX,
        y: clickY + DEFAULT_OFFSET,
        placement: 'bottom',
        offset: { x: 0, y: 0 }
      }
    }
  }

  /**
   * Check if position needs adjustment for better UX
   */
  const validatePosition = (
    position: MenuPosition,
    menuElement: HTMLElement,
    targetElement?: HTMLElement
  ): { isValid: boolean; issues: string[] } => {
    const issues: string[] = []
    const viewport = getViewportInfo()
    const menuBounds = getElementBounds(menuElement)

    // Check viewport boundaries
    if (position.x < 0 || position.x + menuBounds.width > viewport.width) {
      issues.push('Menu extends beyond horizontal viewport')
    }
    
    if (position.y < 0 || position.y + menuBounds.height > viewport.height) {
      issues.push('Menu extends beyond vertical viewport')
    }

    // Check minimum distance from edges
    if (position.x < EDGE_PADDING || position.x + menuBounds.width > viewport.width - EDGE_PADDING) {
      issues.push('Menu too close to horizontal edges')
    }
    
    if (position.y < EDGE_PADDING || position.y + menuBounds.height > viewport.height - EDGE_PADDING) {
      issues.push('Menu too close to vertical edges')
    }

    // Check overlap with target element if provided
    if (targetElement) {
      const targetBounds = getElementBounds(targetElement)
      const menuRight = position.x + menuBounds.width
      const menuBottom = position.y + menuBounds.height
      
      if (
        position.x < targetBounds.right &&
        menuRight > targetBounds.left &&
        position.y < targetBounds.bottom &&
        menuBottom > targetBounds.top
      ) {
        issues.push('Menu overlaps with target element')
      }
    }

    return {
      isValid: issues.length === 0,
      issues
    }
  }

  return {
    // Core positioning functions
    calculatePosition,
    adjustForViewport,
    getResponsivePosition,
    getPositionFromClick,
    
    // Utilities
    validatePosition,
    getViewportInfo,
    getElementBounds,
    
    // Computed properties
    isMobileDevice
  }
}