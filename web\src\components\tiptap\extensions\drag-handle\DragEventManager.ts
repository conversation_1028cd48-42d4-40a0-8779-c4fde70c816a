import type { Editor } from '@tiptap/core'
import type { EditorView } from '@tiptap/pm/view'
import type { Node as ProseMirrorNode } from '@tiptap/pm/model'
import type { DragOperation, DropZone, DragHandleOptions } from './types'
import { NodeValidationUtils } from './utils/nodeValidation'
import { PerformanceUtils } from '../shared/utils/performanceOptimizations'
import { PerformanceMonitoring } from '../shared/utils/performanceMonitoring'
import { PerformanceConfigManager } from '../shared/config/performanceConfig'

export class DragEventManager {
  private editor: Editor
  private view: EditorView
  private options: DragHandleOptions
  private currentOperation: DragOperation | null = null
  private dropZones: DropZone[] = []
  private ghostImage: HTMLElement | null = null
  private dragStartTime: number = 0

  constructor(editor: Editor, view: EditorView, options: DragHandleOptions) {
    this.editor = editor
    this.view = view
    this.options = options
  }

  /**
   * 初始化拖拽操作
   */
  initiateDrag(sourceElement: HTMLElement, event: DragEvent): boolean {
    const endMeasure = PerformanceMonitoring.Profiler.start('dragEventManager-initiateDrag')
    
    try {
      // 记录拖拽开始时间
      this.dragStartTime = Date.now()

      // 获取源节点位置
      const pos = this.view.posAtDOM(sourceElement, 0)
      if (pos === null || pos === undefined) {
        console.warn('Could not determine position for drag source')
        return false
      }

      // 获取源节点
      const sourceNode = this.view.state.doc.nodeAt(pos)
      if (!sourceNode) {
        console.warn('Could not find source node for drag')
        return false
      }

      // 验证节点类型是否支持拖拽
      if (!this.options.supportedNodes.includes(sourceNode.type.name)) {
        console.warn(`Node type ${sourceNode.type.name} is not supported for dragging`)
        return false
      }

      // 获取节点类型配置
      const nodeConfig = this.options.nodeTypeConfigs[sourceNode.type.name]
      if (!nodeConfig) {
        console.warn(`No configuration found for node type ${sourceNode.type.name}`)
        return false
      }

      // 获取嵌套上下文
      const $pos = this.view.state.doc.resolve(pos)
      const nestingContext = {
        sourceParent: $pos.parent,
        sourceDepth: $pos.depth
      }

      // 保存节点属性
      const preservedAttributes = this.preserveNodeAttributes(sourceNode, nodeConfig)

      // 创建拖拽操作对象
      this.currentOperation = {
        sourceNode,
        sourcePos: pos,
        targetPos: null,
        dragElement: sourceElement,
        ghostImage: this.createGhostImage(sourceElement),
        isActive: true,
        nodeType: sourceNode.type.name,
        preservedAttributes,
        nestingContext
      }

      // 设置拖拽数据
      if (event.dataTransfer) {
        event.dataTransfer.effectAllowed = 'move'
        event.dataTransfer.setData('text/html', sourceElement.outerHTML)
        event.dataTransfer.setData('application/x-tiptap-position', pos.toString())
        if (this.currentOperation.ghostImage) {
          event.dataTransfer.setDragImage(this.currentOperation.ghostImage, 0, 0)
        }
      }

      // 创建并显示拖放区域 with performance optimization
      const config = PerformanceConfigManager.getConfig()
      const createDropZonesThrottled = PerformanceUtils.throttle(() => {
        this.createDropZones()
        this.showDropZones()
      }, config.throttling.dragEvents)
      
      createDropZonesThrottled()

      // 添加视觉反馈 with DOM batching
      const batcher = new PerformanceUtils.DOMBatcher()
      batcher.add(() => {
        sourceElement.classList.add('dragging')
        document.body.classList.add('drag-active')
      })
      batcher.flushSync()

      // 更新编辑器存储
      this.updateEditorStorage()

      // 触发拖拽开始事件
      this.editor.emit('dragStart', {
        operation: this.currentOperation,
        event
      })

      endMeasure(true)
      return true
    } catch (error) {
      endMeasure(false)
      console.error('Error initiating drag:', error)
      this.cleanup()
      return false
    }
  }

  /**
   * 处理拖拽结束
   */
  handleDragEnd(event: DragEvent): void {
    const duration = Date.now() - this.dragStartTime
    const endMeasure = PerformanceMonitoring.Profiler.start('dragEventManager-handleDragEnd')

    try {
      // Record performance metrics
      PerformanceMonitoring.MetricsCollector.getInstance().recordDragOperation(
        duration, 
        this.currentOperation?.targetPos !== null
      )

      // 触发拖拽结束事件
      this.editor.emit('dragEnd', {
        operation: this.currentOperation,
        duration,
        event
      })

      // 如果没有成功放置，执行取消动画
      if (this.currentOperation && !this.currentOperation.targetPos) {
        this.animateCancelledDrag()
      }
      
      endMeasure(true)
    } catch (error) {
      endMeasure(false)
      console.error('Error handling drag end:', error)
    } finally {
      // 清理资源
      this.cleanup()
    }
  }

  /**
   * 验证拖放目标
   */
  validateDropTarget(targetElement: HTMLElement, event: DragEvent): boolean {
    if (!this.currentOperation) return false

    try {
      // 获取目标位置
      const targetPos = this.view.posAtDOM(targetElement, 0)
      if (targetPos === null || targetPos === undefined) return false

      // 不能拖拽到自己
      if (targetElement === this.currentOperation.dragElement) return false

      // 检查是否在支持的节点类型上
      const targetNode = this.view.state.doc.nodeAt(targetPos)
      if (!targetNode) return false

      // 验证节点类型兼容性
      if (!this.validateNodeTypeCompatibility(this.currentOperation.sourceNode, targetNode)) {
        return false
      }

      // 检查是否可以在此位置插入
      const canInsert = this.canInsertAt(targetPos, this.currentOperation.sourceNode)
      if (!canInsert) return false

      // 检查基本嵌套规则
      if (!this.validateNestingRules(targetPos, this.currentOperation.sourceNode)) return false

      // 检查高级嵌套规则
      if (!this.validateAdvancedNestingRules(targetPos, this.currentOperation.sourceNode)) {
        return false
      }

      return true
    } catch (error) {
      console.error('Error validating drop target:', error)
      return false
    }
  }

  /**
   * 执行拖放操作
   */
  executeDrop(targetElement: HTMLElement, event: DragEvent): boolean {
    if (!this.currentOperation) return false

    try {
      const targetPos = this.view.posAtDOM(targetElement, 0)
      if (targetPos === null || targetPos === undefined) return false

      // 验证拖放目标
      if (!this.validateDropTarget(targetElement, event)) {
        console.warn('Invalid drop target')
        return false
      }

      // 执行内容移动
      const success = this.moveContent(this.currentOperation.sourcePos, targetPos)
      
      if (success) {
        // 更新操作状态
        this.currentOperation.targetPos = targetPos
        
        // 执行成功动画
        this.animateSuccessfulDrop(targetElement)
        
        // 触发拖放成功事件
        this.editor.emit('dropSuccess', {
          operation: this.currentOperation,
          targetPos,
          event
        })
      }

      return success
    } catch (error) {
      console.error('Error executing drop:', error)
      return false
    }
  }

  /**
   * 取消拖拽操作
   */
  cancelDrag(): void {
    if (!this.currentOperation) return

    try {
      // 执行取消动画
      this.animateCancelledDrag()
      
      // 触发取消事件
      this.editor.emit('dragCancel', {
        operation: this.currentOperation
      })
    } catch (error) {
      console.error('Error canceling drag:', error)
    } finally {
      // 清理资源
      this.cleanup()
    }
  }

  /**
   * 创建幽灵图像
   */
  private createGhostImage(element: HTMLElement): HTMLElement {
    const ghost = element.cloneNode(true) as HTMLElement
    
    // 设置幽灵图像样式
    ghost.style.position = 'absolute'
    ghost.style.top = '-1000px'
    ghost.style.left = '-1000px'
    ghost.style.opacity = this.options.ghostImageOpacity.toString()
    ghost.style.pointerEvents = 'none'
    ghost.style.transform = 'rotate(5deg)'
    ghost.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.2)'
    ghost.style.borderRadius = '8px'
    ghost.style.maxWidth = '300px'
    ghost.style.zIndex = '1000'
    ghost.classList.add('drag-ghost-image')

    // 清理内部的拖拽句柄
    const handles = ghost.querySelectorAll('.drag-handle')
    handles.forEach(handle => handle.remove())

    document.body.appendChild(ghost)
    this.ghostImage = ghost
    
    return ghost
  }

  /**
   * 创建拖放区域
   */
  private createDropZones(): void {
    const endMeasure = PerformanceMonitoring.Profiler.start('dragEventManager-createDropZones')
    
    try {
      this.dropZones = []
      
      const { dom } = this.view
      const supportedSelectors = this.getSupportedSelectors()
      const elements = dom.querySelectorAll(supportedSelectors)
      const config = PerformanceConfigManager.getConfig()

      // Use DOM batcher for efficient drop zone creation
      const batcher = new PerformanceUtils.DOMBatcher()

      // Use virtual scrolling for large documents
      if (config.virtualScrolling.enabled && elements.length >= config.virtualScrolling.threshold) {
        this.createDropZonesVirtual(elements, batcher)
      } else {
        elements.forEach((element) => {
          if (element instanceof HTMLElement && element !== this.currentOperation?.dragElement) {
            batcher.add(() => {
              const dropZone = this.createDropZone(element)
              if (dropZone) {
                this.dropZones.push(dropZone)
              }
            })
          }
        })
      }

      // Execute batched operations
      batcher.flushSync()
      
      endMeasure(true)
    } catch (error) {
      endMeasure(false)
      console.error('Error creating drop zones:', error)
    }
  }

  /**
   * Create drop zones using virtual scrolling for large documents
   */
  private createDropZonesVirtual(elements: NodeListOf<Element>, batcher: PerformanceUtils.DOMBatcher) {
    const config = PerformanceConfigManager.getConfig()
    const containerRect = this.view.dom.getBoundingClientRect()
    const scrollTop = this.view.dom.scrollTop
    const containerHeight = containerRect.height
    
    // Calculate visible range
    const virtualManager = new PerformanceUtils.VirtualScrollManager(
      this.view.dom,
      config.virtualScrolling.itemHeight
    )
    virtualManager.setTotalItems(elements.length)
    
    const visibleRange = virtualManager.calculateVisibleRange(
      scrollTop,
      containerHeight,
      config.virtualScrolling.bufferSize
    )
    
    // Only create drop zones for visible elements
    for (let i = visibleRange.start; i <= visibleRange.end && i < elements.length; i++) {
      const element = elements[i]
      if (element instanceof HTMLElement && element !== this.currentOperation?.dragElement) {
        batcher.add(() => {
          const dropZone = this.createDropZone(element)
          if (dropZone) {
            this.dropZones.push(dropZone)
          }
        })
      }
    }
  }

  /**
   * 创建单个拖放区域
   */
  private createDropZone(element: HTMLElement): DropZone | null {
    try {
      const pos = this.view.posAtDOM(element, 0)
      if (pos === null || pos === undefined) return null

      // 获取目标节点信息
      const targetNode = this.view.state.doc.nodeAt(pos)
      if (!targetNode) return null

      const nodeConfig = this.options.nodeTypeConfigs[targetNode.type.name]
      if (!nodeConfig) return null

      // 计算嵌套级别
      const $pos = this.view.state.doc.resolve(pos)
      const nestingLevel = $pos.depth

      // 确定可接受的节点类型
      const canAcceptNodeTypes = nodeConfig.canReceiveFrom

      // 验证是否为有效的拖放目标
      const isValid = this.currentOperation ? 
        this.validateDropTarget(element, new DragEvent('dragover')) : false

      // 创建视觉指示器
      const indicator = document.createElement('div')
      indicator.className = `drop-zone-indicator ${isValid ? 'valid' : 'invalid'}`
      indicator.style.cssText = `
        position: absolute;
        top: -2px;
        left: 0;
        right: 0;
        height: 4px;
        background: ${isValid ? 'var(--color-primary, #3b82f6)' : 'var(--color-error, #ef4444)'};
        border-radius: 2px;
        opacity: 0;
        transition: opacity 0.2s ease;
        z-index: 100;
        pointer-events: none;
      `

      // 添加到元素
      element.style.position = 'relative'
      element.appendChild(indicator)

      // 添加事件监听器
      this.addDropZoneListeners(element, indicator)

      return {
        element,
        position: pos,
        isValid,
        visualIndicator: indicator,
        nodeType: targetNode.type.name,
        canAcceptNodeTypes,
        nestingLevel,
        validationReason: isValid ? undefined : 'Node type incompatibility or nesting rules violation'
      }
    } catch (error) {
      console.error('Error creating drop zone:', error)
      return null
    }
  }

  /**
   * 添加拖放区域事件监听器
   */
  private addDropZoneListeners(element: HTMLElement, indicator: HTMLElement): void {
    const handleDragOver = (e: DragEvent) => {
      e.preventDefault()
      e.dataTransfer!.dropEffect = 'move'
      indicator.style.opacity = '1'
      element.classList.add('drop-zone-active')
    }

    const handleDragLeave = (e: DragEvent) => {
      if (!element.contains(e.relatedTarget as Node)) {
        indicator.style.opacity = '0'
        element.classList.remove('drop-zone-active')
      }
    }

    const handleDrop = (e: DragEvent) => {
      e.preventDefault()
      this.executeDrop(element, e)
    }

    element.addEventListener('dragover', handleDragOver)
    element.addEventListener('dragleave', handleDragLeave)
    element.addEventListener('drop', handleDrop)

    // 存储监听器以便后续清理
    ;(element as any)._dragListeners = {
      dragover: handleDragOver,
      dragleave: handleDragLeave,
      drop: handleDrop
    }
  }

  /**
   * 显示拖放区域
   */
  private showDropZones(): void {
    this.dropZones.forEach(zone => {
      zone.element.classList.add('drop-zone')
    })
  }

  /**
   * 隐藏拖放区域
   */
  private hideDropZones(): void {
    this.dropZones.forEach(zone => {
      // 移除视觉指示器
      if (zone.visualIndicator && zone.visualIndicator.parentNode) {
        zone.visualIndicator.parentNode.removeChild(zone.visualIndicator)
      }

      // 移除CSS类
      zone.element.classList.remove('drop-zone', 'drop-zone-active')

      // 移除事件监听器
      const listeners = (zone.element as any)._dragListeners
      if (listeners) {
        zone.element.removeEventListener('dragover', listeners.dragover)
        zone.element.removeEventListener('dragleave', listeners.dragleave)
        zone.element.removeEventListener('drop', listeners.drop)
        delete (zone.element as any)._dragListeners
      }
    })
    
    this.dropZones = []
  }

  /**
   * 移动内容
   */
  private moveContent(sourcePos: number, targetPos: number): boolean {
    try {
      const { state, dispatch } = this.view
      const { tr } = state

      // 获取源节点
      const sourceNode = state.doc.nodeAt(sourcePos)
      if (!sourceNode || !this.currentOperation) return false

      // 使用保存的属性重建节点
      const nodeConfig = this.options.nodeTypeConfigs[sourceNode.type.name]
      if (!nodeConfig) return false

      // 使用验证工具保存和重建节点
      const preservation = NodeValidationUtils.preserveNodeContent(sourceNode, nodeConfig)
      preservation.attributes = {
        ...preservation.attributes,
        ...this.currentOperation.preservedAttributes
      }
      
      const preservedNode = NodeValidationUtils.createPreservedNode(sourceNode, preservation)

      // 计算调整后的目标位置
      let adjustedTargetPos = targetPos
      if (sourcePos < targetPos) {
        adjustedTargetPos -= sourceNode.nodeSize
      }

      // 更新嵌套上下文
      const $target = state.doc.resolve(adjustedTargetPos)
      this.currentOperation.nestingContext.targetParent = $target.parent
      this.currentOperation.nestingContext.targetDepth = $target.depth

      // 创建事务
      const newTr = tr
        .delete(sourcePos, sourcePos + sourceNode.nodeSize)
        .insert(adjustedTargetPos, preservedNode)

      // 应用事务
      dispatch(newTr)
      return true
    } catch (error) {
      console.error('Error moving content:', error)
      return false
    }
  }

  /**
   * 检查是否可以在指定位置插入节点
   */
  private canInsertAt(pos: number, node: ProseMirrorNode): boolean {
    try {
      const { state } = this.view
      const $pos = state.doc.resolve(pos)
      return $pos.parent.canReplaceWith($pos.index(), $pos.index(), node.type)
    } catch {
      return false
    }
  }

  /**
   * 验证嵌套规则
   */
  private validateNestingRules(targetPos: number, sourceNode: ProseMirrorNode): boolean {
    try {
      const { state } = this.view
      const $target = state.doc.resolve(targetPos)
      
      // 检查是否会创建无效的嵌套
      // 例如：不能将列表项拖拽到段落内部
      if (sourceNode.type.name === 'listItem' && $target.parent.type.name === 'paragraph') {
        return false
      }
      
      return true
    } catch {
      return false
    }
  }

  /**
   * 获取支持的选择器
   */
  private getSupportedSelectors(): string {
    return this.options.supportedNodes.map(nodeType => {
      const config = this.options.nodeTypeConfigs[nodeType]
      return config ? config.selector : this.getDefaultSelector(nodeType)
    }).join(', ')
  }

  /**
   * 获取默认选择器
   */
  private getDefaultSelector(nodeType: string): string {
    switch (nodeType) {
      case 'paragraph': return 'p'
      case 'heading': return 'h1, h2, h3, h4, h5, h6'
      case 'listItem': return 'li'
      case 'codeBlock': return '.code-block, pre'
      case 'blockquote': return 'blockquote'
      case 'bulletList': return 'ul'
      case 'orderedList': return 'ol'
      default: return `.${nodeType}`
    }
  }

  /**
   * 保存节点属性
   */
  private preserveNodeAttributes(node: ProseMirrorNode, config: any): Record<string, any> {
    const preservation = NodeValidationUtils.preserveNodeContent(node, config)
    return preservation.attributes
  }

  /**
   * 验证节点类型兼容性
   */
  private validateNodeTypeCompatibility(sourceNode: ProseMirrorNode, targetNode: ProseMirrorNode): boolean {
    if (!this.currentOperation) return false
    
    const sourceConfig = this.options.nodeTypeConfigs[sourceNode.type.name]
    const targetConfig = this.options.nodeTypeConfigs[targetNode.type.name]
    
    if (!sourceConfig || !targetConfig) return false
    
    // Use the validation utility
    const $targetPos = this.view.state.doc.resolve(this.view.posAtDOM(this.currentOperation.dragElement, 0) || 0)
    const validationResult = NodeValidationUtils.validateNodeMovement(
      sourceNode, targetNode, sourceConfig, targetConfig, $targetPos
    )
    
    return validationResult.isValid
  }

  /**
   * 验证嵌套规则
   */
  private validateAdvancedNestingRules(targetPos: number, sourceNode: ProseMirrorNode): boolean {
    try {
      const { state } = this.view
      const $target = state.doc.resolve(targetPos)
      const sourceConfig = this.options.nodeTypeConfigs[sourceNode.type.name]
      
      if (!sourceConfig) return false
      
      // Use the validation utility
      const nestingValidation = NodeValidationUtils.validateNesting(
        sourceNode, sourceConfig, $target
      )
      
      return nestingValidation.canNest
    } catch {
      return false
    }
  }

  /**
   * 成功拖放动画
   */
  private animateSuccessfulDrop(targetElement: HTMLElement): void {
    if (!this.currentOperation) return

    const sourceElement = this.currentOperation.dragElement
    
    // 添加成功动画类
    targetElement.classList.add('drop-success')
    sourceElement.classList.add('drag-success')

    // 移除动画类
    setTimeout(() => {
      targetElement.classList.remove('drop-success')
      sourceElement.classList.remove('drag-success')
    }, this.options.animationDuration)
  }

  /**
   * 取消拖拽动画
   */
  private animateCancelledDrag(): void {
    if (!this.currentOperation) return

    const sourceElement = this.currentOperation.dragElement
    
    // 添加取消动画类
    sourceElement.classList.add('drag-cancelled')

    // 移除动画类
    setTimeout(() => {
      sourceElement.classList.remove('drag-cancelled')
    }, this.options.animationDuration)
  }

  /**
   * 更新编辑器存储
   */
  private updateEditorStorage(): void {
    if (this.editor.storage.dragHandle) {
      this.editor.storage.dragHandle.isDragging = true
      this.editor.storage.dragHandle.currentDragElement = this.currentOperation?.dragElement || null
      this.editor.storage.dragHandle.dragState = this.currentOperation
    }
  }

  /**
   * 清理资源
   */
  private cleanup(): void {
    const endMeasure = PerformanceMonitoring.Profiler.start('dragEventManager-cleanup')
    
    try {
      // Use DOM batcher for efficient cleanup
      const batcher = new PerformanceUtils.DOMBatcher()
      
      // 移除视觉反馈
      if (this.currentOperation) {
        batcher.add(() => {
          this.currentOperation!.dragElement.classList.remove('dragging', 'drag-success', 'drag-cancelled')
        })
      }
      
      batcher.add(() => {
        document.body.classList.remove('drag-active')
      })

      // 隐藏拖放区域
      batcher.add(() => {
        this.hideDropZones()
      })

      // 移除幽灵图像
      if (this.ghostImage && this.ghostImage.parentNode) {
        batcher.add(() => {
          this.ghostImage!.parentNode!.removeChild(this.ghostImage!)
        })
      }

      // Execute batched DOM operations
      batcher.flushSync()

      // Reset references
      this.ghostImage = null
      this.currentOperation = null
      this.dropZones = []

      // 更新编辑器存储
      if (this.editor.storage.dragHandle) {
        this.editor.storage.dragHandle.isDragging = false
        this.editor.storage.dragHandle.currentDragElement = null
        this.editor.storage.dragHandle.dragState = null
      }
      
      endMeasure(true)
    } catch (error) {
      endMeasure(false)
      console.error('Error during cleanup:', error)
    }
  }

  /**
   * 获取当前拖拽操作
   */
  getCurrentOperation(): DragOperation | null {
    return this.currentOperation
  }

  /**
   * 检查是否正在拖拽
   */
  isDragging(): boolean {
    return this.currentOperation !== null && this.currentOperation.isActive
  }
}