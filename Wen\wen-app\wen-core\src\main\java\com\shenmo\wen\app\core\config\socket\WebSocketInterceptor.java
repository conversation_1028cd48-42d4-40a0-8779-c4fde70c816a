package com.shenmo.wen.app.core.config.socket;

import cn.dev33.satoken.stp.StpUtil;
import com.shenmo.wen.common.util.spring.SpringIocUtils;
import com.shenmo.wen.modules.user.mapper.WenUserMapper;

import lombok.RequiredArgsConstructor;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.lang.NonNull;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.Map;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@RequiredArgsConstructor
public class WebSocketInterceptor implements HandshakeInterceptor {

    @Override
    public boolean beforeHandshake(@NonNull ServerHttpRequest request,
                                   @NonNull ServerHttpResponse response,
                                   @NonNull WebSocketHandler wsHandler,
                                   @NonNull Map<String, Object> attributes) throws Exception {
        return true;
    }

    @Override
    public void afterHandshake(@NonNull ServerHttpRequest request,
                               @NonNull ServerHttpResponse response,
                               @NonNull WebSocketHandler wsHandler,
                               Exception ex) {
        // 握手后的处理逻辑
    }
}
