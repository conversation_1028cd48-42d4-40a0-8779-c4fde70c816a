# Enhanced Menus for TipTap Editor

这个模块提供了增强的点击菜单和块菜单功能，基于 tiptap-starter-kit 的实现，并针对 Vue 3 + TypeScript 环境进行了优化。

## 功能特性

### 点击菜单 (Click Menu)
- **悬停显示**: 鼠标悬停在块元素上时显示操作按钮
- **拖拽支持**: 支持拖拽移动块元素
- **上下文菜单**: 点击拖拽按钮显示转换选项
- **智能定位**: 自动根据块类型调整菜单位置
- **键盘友好**: 支持键盘操作隐藏菜单

### 块菜单 (Block Menu)
- **斜杠命令**: 输入 `/` 触发命令菜单
- **智能过滤**: 支持按名称和关键词过滤命令
- **键盘导航**: 支持方向键选择和回车确认
- **占位符提示**: 空行显示提示文本
- **分组显示**: 支持分隔符组织命令

## 使用方法

### 基本配置

```typescript
import { Editor } from '@tiptap/core'
import { 
  createEnhancedClickMenu, 
  createEnhancedBlockMenu, 
  registerBlockMenuItems 
} from '@/components/tiptap/extensions/enhanced-menus'

// 创建编辑器
const editor = new Editor({
  extensions: [
    // ... 其他扩展
    createEnhancedClickMenu(),
    createEnhancedBlockMenu(),
  ],
  // ... 其他配置
})

// 注册块菜单项
registerBlockMenuItems(editor)
```

### 自定义点击菜单

```typescript
import { ClickMenuExtension } from '@/components/tiptap/extensions/enhanced-menus'

const clickMenu = ClickMenuExtension.configure({
  enabled: true,
  onMenu: ({ root, editor, active, selection }) => {
    // 自定义菜单内容
    const button = document.createElement('button')
    button.textContent = 'Custom Action'
    button.addEventListener('click', () => {
      // 执行自定义操作
      editor.chain().focus().setParagraph().run()
    })
    root.appendChild(button)
  },
})
```

### 自定义块菜单项

```typescript
import { BlockMenuExtension, blockMenuItems } from '@/components/tiptap/extensions/enhanced-menus'

// 添加自定义菜单项
const customBlockItems = {
  ...blockMenuItems,
  customBlock: {
    id: 'customBlock',
    name: 'Custom Block',
    icon: '<svg>...</svg>',
    keywords: 'custom special',
    action: (editor) => {
      // 执行自定义操作
      editor.chain().focus().insertContent('Custom content').run()
    },
  },
}

const blockMenu = BlockMenuExtension.configure({
  items: Object.keys(customBlockItems),
  dictionary: {
    lineEmpty: "输入 '/' 来插入块...",
    lineSlash: '继续输入以过滤...',
    queryEmpty: '未找到匹配的命令',
  },
})
```

## 样式定制

引入样式文件：

```scss
@import '@/components/tiptap/extensions/click-menu/styles/enhanced-click-menu.scss';
```

或者自定义样式：

```scss
.ProseMirror-cm {
  // 点击菜单容器样式
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.ProseMirror-bm {
  // 块菜单容器样式
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  max-height: 400px;
  overflow-y: auto;
}
```

## 支持的块类型

### 基础块
- `heading1`, `heading2`, `heading3` - 标题
- `paragraph` - 段落
- `blockquote` - 引用
- `codeBlock` - 代码块

### 列表
- `bulletList` - 无序列表
- `orderedList` - 有序列表
- `taskList` - 任务列表

### 媒体
- `image` - 图片
- `table` - 表格
- `horizontalRule` - 分隔线

## 键盘快捷键

- `Mod+Alt+1` - 标题 1
- `Mod+Alt+2` - 标题 2
- `Mod+Alt+3` - 标题 3
- `Mod+Alt+0` - 段落
- `Mod+Shift+8` - 无序列表
- `Mod+Shift+7` - 有序列表
- `Mod+Shift+B` - 引用
- `Mod+Alt+C` - 代码块

## 浏览器兼容性

- Chrome 88+
- Firefox 85+
- Safari 14+
- Edge 88+

## 依赖项

- `@tiptap/core`
- `@tiptap/pm/state`
- `@tiptap/pm/view`
- `@tiptap/pm/model`
- `@tiptap/suggestion`
- `tippy.js`
- `smooth-scroll-into-view-if-needed`

## 注意事项

1. 确保在编辑器初始化后调用 `registerBlockMenuItems`
2. 点击菜单需要编辑器容器有相对定位
3. 块菜单的图标使用 SVG 格式以确保清晰度
4. 拖拽功能需要浏览器支持 HTML5 拖拽 API

## 故障排除

### 菜单不显示
- 检查是否正确引入样式文件
- 确认编辑器容器的定位设置
- 验证扩展是否正确注册

### 拖拽不工作
- 确认浏览器支持拖拽 API
- 检查是否有其他事件监听器干扰
- 验证节点是否可选择

### 样式问题
- 检查 CSS 优先级
- 确认 tippy.js 主题设置
- 验证响应式断点设置
