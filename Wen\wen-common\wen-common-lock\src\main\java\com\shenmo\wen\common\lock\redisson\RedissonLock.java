package com.shenmo.wen.common.lock.redisson;

import org.springframework.http.HttpStatus;

import java.lang.annotation.*;

/**
 * redisson lock注解
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Documented
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.METHOD)
public @interface RedissonLock {

    /**
     * 锁的key名
     * <p>
     * 支持Spel表达式
     */
    String value();

    /**
     * 是否开启前缀
     */
    boolean enablePrefix() default true;

    /**
     * 未获取到锁是否持续尝试
     */
    boolean enableContinueTry() default false;

    /**
     * 开启异常
     * <p>
     * 只有当{@link #enableContinueTry()}关闭才有效
     */
    boolean enableException() default true;

    /**
     * http状态
     * <p>
     * 只有当{@link #enableException}开启才生效
     */
    HttpStatus httpStatus() default HttpStatus.FORBIDDEN;

    /**
     * 未获取到锁后的异常内容
     * <p>
     * 只有当{@link #enableException}开启才生效
     */
    String message() default "当前操作正在进行中,请稍后再试";
}
