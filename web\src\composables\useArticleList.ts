import { useDialog } from 'naive-ui'
import { ref, computed, nextTick, watch } from 'vue'

import articleApi from '@/api/article'
import fileApi from '@/api/file'
import {
  ArticlePublishedScope,
  ARTICLE_PUBLISHED_SCOPE_LABEL,
} from '@/constants/article_published_scope.constants'
import type { Article } from '@/types/article.types'
import type { ResponseData } from '@/types/response_data.types'
import message from '@/utils/message'
import { activeTheme, ThemeType } from '@/utils/theme'
import tiptap from '@/utils/tiptap'

import type { Span } from 'naive-ui/es/legacy-grid/src/interface'

// 文章卡片固定高度
const CARD_HEIGHT = 470

export function useArticleList(props: any) {
  // 文章列表数据
  const articleList = ref<Article[]>([])
  const loading = ref(false)
  const noMore = ref(false)
  const cardColSpan = ref<Span>(6)
  const articleTiptapEditorMap = ref(new Map())
  // 追踪当前已加载的文章总数
  const currentLoadedArticlesCount = ref(0)
  // 追踪每行卡片数
  const cardsPerRow = ref(1)

  // DOM引用
  const containerRef = ref<HTMLElement | null>(null)
  const scrollContainerRef = ref<HTMLElement | null>(null)

  // 对话框
  const dialog = useDialog()

  // 颜色配置
  const lightColors = [
    '#ffd6d6',
    '#ffe8d1',
    '#fff8c4',
    '#d5edd7',
    '#d0e8fa',
    '#ded6f2',
    '#ebcfe9',
    '#f8d4de',
  ]

  const darkColors = [
    '#8c3a3a',
    '#7d6339',
    '#75763a',
    '#366d5a',
    '#355678',
    '#534878',
    '#664766',
    '#6a4251',
  ]

  // 判断当前是否为暗色主题
  const isDarkTheme = computed(() => activeTheme.value === ThemeType.DARK)

  // 按彩虹顺序获取卡片颜色
  const getCardColor = (id: string, index: number) => {
    const colorSet = isDarkTheme.value ? darkColors : lightColors
    return colorSet[index % colorSet.length]
  }

  // 根据窗口宽度动态调整卡片列数并返回每行卡片数
  const updateColSpan = () => {
    const width = window.innerWidth
    let span: Span = 24
    let newCardsPerRow = 1

    if (width >= 1680) {
      span = 6
      newCardsPerRow = 4
    } else if (width >= 1260) {
      span = 8
      newCardsPerRow = 3
    } else if (width >= 840) {
      span = 12
      newCardsPerRow = 2
    } else {
      span = 24
      newCardsPerRow = 1
    }
    cardColSpan.value = span
    // 更新 cardsPerRow ref
    cardsPerRow.value = newCardsPerRow
  }

  // 计算初始加载量（铺满屏幕）或后续加载量（首次加载量的一半，并向上取整到每行卡片数的倍数）
  const calculatedLoadSize = computed(() => {
    const currentCardsPerRow = cardsPerRow.value // 使用响应式的 cardsPerRow
    const containerHeight = containerRef.value?.clientHeight || 0

    let size: number

    if (articleList.value.length === 0) {
      // 首次加载：计算铺满屏幕所需的文章数量
      const rowsNeeded = Math.ceil(containerHeight / CARD_HEIGHT)
      size = rowsNeeded * currentCardsPerRow
      // 确保至少加载一屏幕的内容，并是每行卡片数的倍数
      size = Math.max(currentCardsPerRow, size)
    } else {
      // 后续加载：首次加载量的一半，并向上取整到每行卡片数的倍数
      const initialLoadSize =
        Math.ceil((containerRef.value?.clientHeight || 0) / CARD_HEIGHT) * currentCardsPerRow
      size = Math.ceil(initialLoadSize / 2)
      // 确保是 currentCardsPerRow 的倍数，向上取整
      size = Math.ceil(size / currentCardsPerRow) * currentCardsPerRow
      // 确保至少加载 currentCardsPerRow 数量的文章
      size = Math.max(currentCardsPerRow, size)
    }

    console.log(
      'Calculated Load Size:',
      size,
      'Cards Per Row:',
      currentCardsPerRow,
      'Current Loaded Articles:',
      currentLoadedArticlesCount.value,
      'Card Height:',
      CARD_HEIGHT,
    )
    return size
  })

  // 监听 cardsPerRow 变化，当视口改变导致每行卡片数变化时，如果当前文章数量不是其倍数，则加载更多文章
  watch(cardsPerRow, (newCardsPerRow, oldCardsPerRow) => {
    if (newCardsPerRow !== oldCardsPerRow) {
      const remainder = articleList.value.length % newCardsPerRow
      if (remainder !== 0) {
        const articlesToLoad = newCardsPerRow - remainder
        console.log(
          `视口变化，每行卡片数从 ${oldCardsPerRow} 变为 ${newCardsPerRow}，需要加载 ${articlesToLoad} 篇文章以补齐倍数。`,
        )
        // 强制加载补齐数量的文章
        loadArticles(true, undefined, articlesToLoad)
      }
    }
  })

  // 重置文章列表
  const resetList = () => {
    articleList.value = []
    noMore.value = false
    // 重置已加载数量
    currentLoadedArticlesCount.value = 0
    loadArticles()
  }

  // 加载文章
  const loadArticles = (loadMore = false, signal?: AbortSignal, forceLoadSize?: number) => {
    console.log('loadArticles called:', {
      loadMore,
      loading: loading.value,
      noMore: noMore.value,
      searchCondition: props.searchCondition,
      forceLoadSize,
    })
    // 使用 forceLoadSize 或计算属性的加载大小
    const loadSize = forceLoadSize !== undefined ? forceLoadSize : calculatedLoadSize.value

    if (loading.value || noMore.value) {
      console.log('loadArticles early return:', { loading: loading.value, noMore: noMore.value })
      return Promise.resolve()
    }

    loading.value = true

    const lastArticle =
      articleList.value.length > 0 ? articleList.value[articleList.value.length - 1] : null
    const requestId = lastArticle?.id

    return new Promise<void>((resolve, reject) => {
      nextTick(() => {
        const searchParams = { ...props.searchCondition, id: requestId, loadSize: loadSize }
        console.log('Making API request with params:', searchParams)

        articleApi
          .search(searchParams, signal)
          .then((res: ResponseData) => {
            console.log('API response received:', res)
            if (!res || !res.data) {
              resolve()
              return
            }

            const data = res.data
            if (data.length === 0) {
              noMore.value = true
              resolve()
              return
            }

            if (data.length < loadSize) {
              noMore.value = true
            }

            const newArticlesAdded = addArticles(data)

            if (!newArticlesAdded) {
              noMore.value = true
              resolve()
              return
            }
            // 更新已加载文章数量
            currentLoadedArticlesCount.value += data.length
          })
          .catch((error) => {
            if (error.name === 'CanceledError' || error.message === 'canceled') {
              resolve()
              return
            }

            console.error('加载文章失败:', error)
            noMore.value = false
            if (!signal?.aborted) {
              reject(error)
            } else {
              resolve()
            }
          })
          .finally(() => {
            if (!signal?.aborted) {
              loading.value = false
            }
          })
      })
    })
  }

  // 添加文章到列表
  const addArticles = (list: Article[]): boolean => {
    const existingArticleIds = new Set(articleList.value.map((article) => article.id))
    const newArticles = list.filter((article) => !existingArticleIds.has(article.id))

    if (newArticles.length === 0) return false

    const processedList = newArticles.map((article) => ({
      ...article,
      contentObj: tiptap.toJsonObject(article.content),
      publisherAvatar: getResourceURL(article.publisherAvatar),
      tags: article.tag?.split(',') || [],
    }))

    articleList.value = [...articleList.value, ...processedList]
    return true
  }

  // 获取资源URL
  const getResourceURL = (uri: string): string => {
    return fileApi.getResourceURL(uri)
  }

  // 切换文章发布范围
  const handleToggleScope = (article: Article) => {
    const currentScope = article.publishedScope
    const targetScope =
      currentScope === ArticlePublishedScope.PERSONAL
        ? ArticlePublishedScope.PUBLIC
        : ArticlePublishedScope.PERSONAL

    dialog.warning({
      title: '切换发布范围',
      content: `确定要将文章《${article.title}》切换为${ARTICLE_PUBLISHED_SCOPE_LABEL[targetScope]}可见吗？`,
      positiveText: '确定',
      negativeText: '取消',
      onPositiveClick: () => {
        articleApi.togglePublishedScope(article.id).then((res: ResponseData) => {
          if (res.code === 200) {
            article.publishedScope = targetScope
            message.success(`文章已切换为${ARTICLE_PUBLISHED_SCOPE_LABEL[targetScope]}可见`)
          } else {
            message.error(res.message || '操作失败')
          }
        })
      },
    })
  }

  // 处理删除文章
  const handleDeleteArticle = (article: Article) => {
    dialog.warning({
      title: '删除文章',
      content: `确定要删除文章《${article.title}》吗？此操作不可恢复。`,
      positiveText: '删了',
      negativeText: '算了',
      onPositiveClick: () => {
        articleApi
          .delete(article.id)
          .then((res: ResponseData) => {
            if (res.code === 200) {
              const index = articleList.value.findIndex((item) => item.id === article.id)
              if (index > -1) {
                articleList.value.splice(index, 1)
              }
              message.success('文章已删除')
            } else {
              message.error(res.message || '删除失败')
            }
          })
          .catch(() => {
            message.error('删除失败，请稍后再试')
          })
      },
    })
  }

  // 处理文章重新排序
  const handleReorderArticles = (
    draggedId: string,
    targetId: string,
    position: 'before' | 'after',
  ) => {
    const draggedIndex = articleList.value.findIndex((item) => item.id === draggedId)
    const targetIndex = articleList.value.findIndex((item) => item.id === targetId)

    if (draggedIndex === -1 || targetIndex === -1) return

    const [draggedArticle] = articleList.value.splice(draggedIndex, 1)

    let newIndex = targetIndex
    if (position === 'after') {
      newIndex = draggedIndex < targetIndex ? targetIndex : targetIndex + 1
    } else {
      newIndex = draggedIndex > targetIndex ? targetIndex : targetIndex
    }

    articleList.value.splice(newIndex, 0, draggedArticle)
    message.success('移动成功！')
  }

  return {
    articleList,
    loading,
    noMore,
    cardColSpan,
    articleTiptapEditorMap,
    containerRef,
    scrollContainerRef,
    currentLoadedArticlesCount,

    // 计算属性
    getCardColor,
    calculatedLoadSize,

    // 方法
    updateColSpan,
    resetList,
    loadArticles,
    handleToggleScope,
    handleDeleteArticle,
    handleReorderArticles,
  }
}
