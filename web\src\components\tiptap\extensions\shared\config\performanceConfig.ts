/**
 * Performance configuration for TipTap extensions
 * Centralized configuration for performance optimizations
 */

export interface PerformanceConfig {
  // Throttling and debouncing settings
  throttling: {
    dragEvents: number
    menuEvents: number
    scrollEvents: number
    resizeEvents: number
    updateEvents: number
  }
  
  debouncing: {
    menuHide: number
    searchInput: number
    autoSave: number
  }
  
  // Virtual scrolling settings
  virtualScrolling: {
    enabled: boolean
    itemHeight: number
    bufferSize: number
    threshold: number // Number of items before enabling virtual scrolling
  }
  
  // Memory management settings
  memoryManagement: {
    cleanupInterval: number
    maxCachedItems: number
    gcThreshold: number
  }
  
  // Animation settings
  animations: {
    respectReducedMotion: boolean
    defaultDuration: number
    easing: string
    disableOnLowEnd: boolean
  }
  
  // Event delegation settings
  eventDelegation: {
    enabled: boolean
    containerSelector: string
    batchSize: number
  }
  
  // Performance monitoring
  monitoring: {
    enabled: boolean
    sampleRate: number
    maxMeasurements: number
  }
}

/**
 * Default performance configuration
 */
export const defaultPerformanceConfig: PerformanceConfig = {
  throttling: {
    dragEvents: 16, // 60fps
    menuEvents: 50, // 20fps
    scrollEvents: 16, // 60fps
    resizeEvents: 100, // 10fps
    updateEvents: 100, // 10fps
  },
  
  debouncing: {
    menuHide: 300,
    searchInput: 300,
    autoSave: 1000,
  },
  
  virtualScrolling: {
    enabled: true,
    itemHeight: 40,
    bufferSize: 5,
    threshold: 100,
  },
  
  memoryManagement: {
    cleanupInterval: 30000, // 30 seconds
    maxCachedItems: 1000,
    gcThreshold: 10000, // 10MB
  },
  
  animations: {
    respectReducedMotion: true,
    defaultDuration: 200,
    easing: 'ease-out',
    disableOnLowEnd: true,
  },
  
  eventDelegation: {
    enabled: true,
    containerSelector: '.ProseMirror',
    batchSize: 50,
  },
  
  monitoring: {
    enabled: process.env.NODE_ENV === 'development',
    sampleRate: 0.1, // 10% sampling
    maxMeasurements: 1000,
  },
}

/**
 * Performance configuration for different device types
 */
export const deviceSpecificConfigs = {
  mobile: {
    ...defaultPerformanceConfig,
    throttling: {
      ...defaultPerformanceConfig.throttling,
      dragEvents: 33, // 30fps for mobile
      menuEvents: 100, // 10fps for mobile
    },
    virtualScrolling: {
      ...defaultPerformanceConfig.virtualScrolling,
      threshold: 50, // Lower threshold for mobile
      bufferSize: 3,
    },
    animations: {
      ...defaultPerformanceConfig.animations,
      defaultDuration: 150, // Faster animations on mobile
    },
  },
  
  lowEnd: {
    ...defaultPerformanceConfig,
    throttling: {
      ...defaultPerformanceConfig.throttling,
      dragEvents: 50, // 20fps for low-end devices
      menuEvents: 200, // 5fps for low-end devices
    },
    virtualScrolling: {
      ...defaultPerformanceConfig.virtualScrolling,
      threshold: 25, // Very low threshold
      bufferSize: 2,
    },
    animations: {
      ...defaultPerformanceConfig.animations,
      disableOnLowEnd: true,
      defaultDuration: 0, // Instant animations
    },
    memoryManagement: {
      ...defaultPerformanceConfig.memoryManagement,
      cleanupInterval: 10000, // More frequent cleanup
      maxCachedItems: 100,
    },
  },
}

/**
 * Device detection utilities
 */
export class DeviceDetector {
  private static _isMobile: boolean | null = null
  private static _isLowEnd: boolean | null = null

  static get isMobile(): boolean {
    if (this._isMobile === null) {
      this._isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
        navigator.userAgent
      ) || window.innerWidth <= 768
    }
    return this._isMobile
  }

  static get isLowEnd(): boolean {
    if (this._isLowEnd === null) {
      // Check for low-end device indicators
      const connection = (navigator as any).connection
      const hardwareConcurrency = navigator.hardwareConcurrency || 1
      const deviceMemory = (navigator as any).deviceMemory || 1

      this._isLowEnd = (
        hardwareConcurrency <= 2 ||
        deviceMemory <= 2 ||
        (connection && connection.effectiveType === 'slow-2g') ||
        (connection && connection.effectiveType === '2g')
      )
    }
    return this._isLowEnd
  }

  static get isHighEnd(): boolean {
    return !this.isMobile && !this.isLowEnd
  }
}

/**
 * Performance configuration manager
 */
export class PerformanceConfigManager {
  private static config: PerformanceConfig = defaultPerformanceConfig

  /**
   * Get current performance configuration
   */
  static getConfig(): PerformanceConfig {
    return this.config
  }

  /**
   * Set performance configuration
   */
  static setConfig(config: Partial<PerformanceConfig>): void {
    this.config = { ...this.config, ...config }
  }

  /**
   * Get device-appropriate configuration
   */
  static getDeviceConfig(): PerformanceConfig {
    if (DeviceDetector.isLowEnd) {
      return deviceSpecificConfigs.lowEnd
    } else if (DeviceDetector.isMobile) {
      return deviceSpecificConfigs.mobile
    } else {
      return defaultPerformanceConfig
    }
  }

  /**
   * Auto-configure based on device capabilities
   */
  static autoConfig(): void {
    this.config = this.getDeviceConfig()
  }

  /**
   * Reset to default configuration
   */
  static reset(): void {
    this.config = defaultPerformanceConfig
  }
}

/**
 * Performance optimization factory
 */
export class PerformanceOptimizationFactory {
  private static config = PerformanceConfigManager.getConfig()

  /**
   * Create throttled function based on event type
   */
  static createThrottledFunction<T extends (...args: any[]) => any>(
    func: T,
    eventType: keyof PerformanceConfig['throttling']
  ): T {
    const delay = this.config.throttling[eventType]
    return PerformanceUtils.throttle(func, delay) as T
  }

  /**
   * Create debounced function based on event type
   */
  static createDebouncedFunction<T extends (...args: any[]) => any>(
    func: T,
    eventType: keyof PerformanceConfig['debouncing']
  ): T {
    const delay = this.config.debouncing[eventType]
    return PerformanceUtils.debounce(func, delay) as T
  }

  /**
   * Create RAF throttled function for animations
   */
  static createAnimationFunction<T extends (...args: any[]) => any>(func: T): T {
    if (this.config.animations.respectReducedMotion && 
        window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      return func // No throttling for reduced motion
    }
    return PerformanceUtils.rafThrottle(func) as T
  }

  /**
   * Check if virtual scrolling should be enabled
   */
  static shouldUseVirtualScrolling(itemCount: number): boolean {
    return this.config.virtualScrolling.enabled && 
           itemCount >= this.config.virtualScrolling.threshold
  }

  /**
   * Get animation duration based on configuration
   */
  static getAnimationDuration(): number {
    if (this.config.animations.disableOnLowEnd && DeviceDetector.isLowEnd) {
      return 0
    }
    if (this.config.animations.respectReducedMotion && 
        window.matchMedia('(prefers-reduced-motion: reduce)').matches) {
      return 0
    }
    return this.config.animations.defaultDuration
  }
}

// Auto-configure on module load
PerformanceConfigManager.autoConfig()

// Import PerformanceUtils for factory
import * as PerformanceUtils from '../utils/performanceOptimizations'

export { PerformanceUtils }
export default PerformanceConfigManager