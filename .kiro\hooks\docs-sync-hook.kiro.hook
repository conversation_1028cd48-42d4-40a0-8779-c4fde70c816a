{"enabled": true, "name": "Documentation Sync", "description": "Listens to source code changes across the repository and automatically updates documentation in README files and docs folders", "version": "1", "when": {"type": "fileEdited", "patterns": ["web/src/**/*.ts", "web/src/**/*.vue", "web/src/**/*.js", "Wen/**/*.java", "web/package.json", "Wen/pom.xml", "web/vite.config.ts", "web/tsconfig.json"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified code in the changed files for potential improvements. Focus on:\n\n1. Code Smells: Identify any code smells like long methods, large classes, duplicate code, or complex conditionals.  \n   - Ensure that the number of lines of code in any function or method does not exceed 80 lines.  \n   - Ensure that the total number of lines in any `.ts`, `.scss`, or `.vue` file does not exceed 300 lines; the total number of lines in any `.java` file does not exceed 1000 lines.\n\n2. **Design Patterns**: Suggest appropriate design patterns that could improve the code structure\n\n3. **Best Practices**: Check adherence to language-specific and framework-specific best practices\n\n4. **Readability**: Suggest improvements for variable naming, code organization, and documentation\n\n5. **Maintainability**: Identify areas that could be refactored for better maintainability\n\n6. **Performance**: Suggest optimizations that could improve performance without breaking functionality\n\nFor each suggestion, provide:\n- Clear explanation of the issue\n- Specific recommendation for improvement\n- Code example if applicable\n- Rationale for why this change would be beneficial\n\nConsider the technology stack: Vue 3 with TypeScript, Java Spring Boot, and the existing project structure. Maintain compatibility with existing functionality while suggesting improvements. "}}