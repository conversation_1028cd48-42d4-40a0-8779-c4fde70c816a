import { reactive } from 'vue'

import { getDanmuRightPosition } from './layout/positionCalculator'

import type { DanChannel } from './types'

export function useChannelManagement(danmakuConfig: any) {
  // 弹幕轨道管理
  const danChannel = reactive<DanChannel>({})

  /**
   * 获取可插入的弹幕轨道索引
   */
  function getChannelIndex(el: HTMLDivElement, dmContainer: HTMLDivElement): number {
    let _channels = [...Array(danmakuConfig.channels).keys()]

    if (danmakuConfig.randomChannel) {
      _channels = _channels.sort(() => 0.5 - Math.random())
    }

    for (const i of _channels) {
      const items = danChannel[i]

      if (items && items.length) {
        for (let j = 0; j < items.length; j++) {
          const danRight = getDanmuRightPosition(items[j], dmContainer) - 10
          // 安全距离判断
          if (danRight <= (el.offsetWidth - items[j].offsetWidth) * 0.75 || danRight <= 0) {
            break
          }
          if (j === items.length - 1) {
            danChannel[i].push(el)
            el.addEventListener('animationend', () => danChannel[i].splice(0, 1))
            return i % danmakuConfig.channels
          }
        }
      } else {
        danChannel[i] = [el]
        el.addEventListener('animationend', () => danChannel[i].splice(0, 1))
        return i % danmakuConfig.channels
      }
    }
    return -1
  }

  return {
    danChannel,
    getChannelIndex,
  }
}
