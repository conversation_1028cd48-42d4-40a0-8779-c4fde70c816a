package com.shenmo.wen.app.core.user.controller;

import com.shenmo.wen.app.core.user.service.WenUserService;
import com.shenmo.wen.common.pojo.response.ResponseData;
import com.shenmo.wen.modules.user.pojo.domain.WenSearchUser;
import com.shenmo.wen.modules.user.pojo.vo.WenUserVo;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.List;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("users")
@RequiredArgsConstructor
public class WenUserController {

    private final WenUserService service;

    @GetMapping("/online")
    public ResponseData<Long> online() {
        return ResponseData.success(service.online());
    }

    @GetMapping("/info")
    public ResponseData<WenUserVo> info() {
        return ResponseData.success(service.info());
    }


    @PutMapping("/avatar")
    public ResponseData<String> changeAvatar(@RequestParam("file") MultipartFile avatarFile) throws IOException {
        return ResponseData.success(service.changeAvatar(avatarFile));
    }

    /**
     * 更新通知接收类型
     * @param type 通知接收类型
     * @return 是否更新成功
     */
    @PutMapping("/notification-receive-type/{type}")
    public ResponseData<Boolean> updateNotificationReceiveType(@PathVariable("type") Integer type) {
        return ResponseData.success(service.updateNotificationReceiveType(type));
    }

    @GetMapping("/search/{username}")
    public ResponseData<List<WenSearchUser>> search(@PathVariable("username") String username) {
        return ResponseData.success(service.search(username));
    }
}
