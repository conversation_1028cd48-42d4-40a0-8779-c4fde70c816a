<template>
  <div class="code-block-header">
    <!-- 语言标识 - 始终不可修改 -->
    <span
      class="code-block-language"
      contenteditable="false"
      :style="{ userSelect: 'none', pointerEvents: 'none' }"
      >{{ language }}</span
    >

    <!-- 工具栏 -->
    <div class="code-block-toolbar">
      <!-- 换行切换按钮 -->
      <button
        class="code-wrap-button"
        :class="{ active: wrapMode }"
        :title="wrapMode ? '禁用换行' : '启用换行'"
        @click="$emit('toggle-wrap')"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <polyline points="17 2 21 6 17 10"></polyline>
          <path d="M3 11V9a4 4 0 0 1 4-4h14"></path>
          <polyline points="7 22 3 18 7 14"></polyline>
          <path d="M21 13v2a4 4 0 0 1-4 4H3"></path>
        </svg>
      </button>

      <!-- 复制按钮 -->
      <button
        class="code-copy-button"
        :class="{ active: copyState.copied }"
        :title="copyState.copied ? '已复制' : '复制代码'"
        @click="$emit('copy-code')"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <rect x="9" y="9" width="13" height="13" rx="2" ry="2"></rect>
          <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"></path>
        </svg>
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
interface CopyState {
  copied: boolean
  timer: number | null
}

defineProps<{
  language: string
  wrapMode: boolean
  copyState: CopyState
}>()

defineEmits<{
  'toggle-wrap': []
  'copy-code': []
}>()
</script>

<style scoped>
.code-block-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background-color: var(--creamy-white-2, #e4e1d8);
  padding: 0.4rem 0.8rem;
  border-bottom: 1px solid var(--creamy-white-3, #dcd8ca);
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans',
    'Helvetica Neue', sans-serif;
  font-size: 0.85rem;
  color: var(--gray-5, rgba(28, 25, 23, 60%));
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
  flex-shrink: 0;
}

.code-block-language {
  color: var(--gray-5, rgba(28, 25, 23, 60%));
  font-weight: 500;
  font-family:
    -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans',
    'Helvetica Neue', sans-serif;
  text-transform: lowercase;
  flex-shrink: 0;
}

.code-block-toolbar {
  display: flex;
  gap: 0.5rem;
}

.code-wrap-button,
.code-copy-button {
  width: 24px;
  height: 24px;
  padding: 4px;
  background-color: transparent;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.code-wrap-button svg,
.code-copy-button svg {
  width: 16px;
  height: 16px;
  stroke: var(--gray-5, rgba(28, 25, 23, 60%));
  transition: all 0.2s ease;
}

.code-wrap-button:hover,
.code-copy-button:hover {
  background-color: var(--creamy-white-3, #dcd8ca);
}

.code-wrap-button:hover svg,
.code-copy-button:hover svg {
  stroke: var(--black, #2e2b29);
}

/* 按钮激活状态 */
.code-wrap-button.active,
.code-copy-button.active {
  background-color: var(--creamy-white-3, #dcd8ca);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 10%);
}

.code-wrap-button.active svg,
.code-copy-button.active svg {
  stroke: var(--black, #2e2b29);
}

/* 复制按钮成功状态 */
.code-copy-button.copied {
  background-color: rgba(34, 197, 94, 10%);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 5%);
}

.code-copy-button.copied svg {
  stroke: var(--green, #22c55e);
}

/* 换行按钮激活状态 */
.code-wrap-button.active {
  background-color: rgba(75, 163, 253, 10%);
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 5%);
}

.code-wrap-button.active svg {
  stroke: var(--blue, #4ba3fd);
}
</style>
