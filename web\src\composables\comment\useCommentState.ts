import { ref, computed, watch, nextTick } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import commentApi from '@/api/comment'
import { ARTICLE_COMMENTS } from '@/constants/frequency_key.constants'
import { useCommentStore } from '@/stores'
import type { Comment } from '@/types/comment.types'
import type { ResponseData } from '@/types/response_data.types'
import DateTime from '@/utils/date-time'
import frequencyLimit from '@/utils/frequency-limit'
import tiptap from '@/utils/tiptap'

export function useCommentState(getArticleId: () => string) {
  // 评论列表相关状态
  const commentList = ref<Comment[]>([])
  const commentRefs = ref<Map<string, HTMLElement>>(new Map())
  const commentLoading = ref(false)
  const commentNoMore = ref(false)
  const commentScrollTrigger = ref<string>('0')
  const commentStore = useCommentStore()
  const route = useRoute()
  const router = useRouter()
  const hasCommentPermission = ref(false)
  const sortType = ref('0') // 排序类型：0-按点赞数排序（默认），1-按时间排序，2-按回复数排序

  // 初始化评论权限
  const initCommentPermission = () => {
    commentApi
      .load(`?articleId=${getArticleId()}&id=&parentCommentId=&loadSize=1`)
      .then((res: ResponseData) => {
        if (res?.data) {
          hasCommentPermission.value = true
        }
      })
      .catch((error) => {
        if (error.response && error.response.status === 403) {
          hasCommentPermission.value = false
        }
      })
  }

  // 面包屑相关状态
  const getFirstBreadcrumb = (): Comment => {
    return { id: '', publisher: '评论列表' } as Comment
  }
  const breadcrumb = ref<Comment[]>([getFirstBreadcrumb()])
  const isLastBreadcrumb = computed(() => breadcrumb.value.length === 3)
  const lastBreadcrumbIndex = computed(() => breadcrumb.value.length - 1)
  const lastBreadcrumbComment = computed(() => breadcrumb.value[breadcrumb.value.length - 1])

  // 重置面包屑
  const resetBreadcrumb = () => {
    breadcrumb.value = [getFirstBreadcrumb()]
  }

  // 增加面包屑
  const addBreadcrumb = (comment: Comment) => {
    breadcrumb.value.push(comment)
  }

  // 展示回复列表
  const showReplyListBtn = ref(true)
  const setShowReplyListBtn = () => {
    showReplyListBtn.value = lastBreadcrumbIndex.value < 2
  }

  // 重置评论列表
  const resetCommentList = () => {
    commentList.value = []
    commentRefs.value.clear()
    commentLoading.value = false
    commentNoMore.value = false
    resetScrollTrigger()
  }

  // 重置滚动触发器
  const resetScrollTrigger = () => {
    commentScrollTrigger.value = String(Date.now())
  }

  // 监听路由变化
  watch(route, () => {
    if (getCommentId()) {
      locationComment()
    } else {
      loadCurrentCommentList()
    }
  })

  // 监听评论ID变化
  watch(
    () => commentStore.getId,
    (newCommentId) => {
      if (newCommentId) {
        locationComment(newCommentId)
      }
    },
  )

  // 更新评论引用
  const updateCommentRef = (commentId: string, el: HTMLElement) => {
    commentRefs.value.set(commentId, el)
  }

  // 获取评论ID
  const getCommentId = (): string => {
    return commentStore.getId
  }

  // 闪烁评论
  const flashCommentId = ref<string>('')
  const flashComment = (commentId: string) => {
    flashCommentId.value = commentId
    setTimeout(() => {
      flashCommentId.value = ''
    }, 1000)
  }

  // 滚动到指定评论
  const prepareScrollToComment = (comments: Comment[]) => {
    resetCommentList()
    addCommentList(comments)
  }

  const scrollToComment = (commentId: string) => {
    nextTick(() => {
      const commentRef = commentRefs.value.get(commentId)
      if (commentRef) {
        commentRef.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        })
        setTimeout(() => {
          commentRef.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
          })
          flashComment(commentId)
        }, 10)
      }
    })
  }

  // 定位评论
  const locationComment = (commentId: string = getCommentId()) => {
    commentLoading.value = true
    if (commentId) {
      // 更新commentStore中的评论ID
      commentStore.setId(commentId)
      // 更新路由参数
      router.push({
        params: {
          ...route.params,
          commentId: commentId,
        },
      })
      commentApi
        .location(commentId)
        .then((res: ResponseData) => {
          const commentLocation = res?.data
          if (commentLocation) {
            const parents = commentLocation.parents
            const comments = commentLocation.comments
            prepareScrollToComment(comments)
            resetBreadcrumb()
            const parentsLength = parents.length
            if (parentsLength > 0) {
              for (let i = parentsLength - 1; i >= 0; i--) {
                const comment = parents[i]
                addBreadcrumb(comment)
              }
              const parentComment = parents[0]
              setFirstFixedComment(parentComment)
            }
            scrollToComment(commentId)
          }
        })
        .catch((error) => {
          // 处理评论加载错误
          if (error.response && error.response.status === 403) {
            // 权限错误，仅重置评论列表
            resetCommentList()
          } else {
            console.error('定位评论失败:', error)
          }
        })
        .finally(() => {
          commentLoading.value = false
        })
    }
  }

  // 添加评论到列表
  const addCommentList = (list: Comment[]) => {
    if (!list || list.length === 0) return

    list
      .map(({ ...original }) => ({
        ...original,
        publishedAt: DateTime.getRelativeTime(original.publishedAt),
        exactPublishedAt: DateTime.toTimeString(original.publishedAt),
        fixed: false,
      }))
      .forEach((e: Comment) => {
        e.contentObj = tiptap.toJsonObject(e.content)
        e.quickCommentReply = undefined
        commentList.value.push(e)
      })
  }

  // 更新评论列表首位固定评论
  const updateCommentListByFirstFixedComment = (comment: Comment) => {
    commentList.value.unshift({
      ...comment,
      publishedAt: DateTime.getRelativeTime(comment.publishedAt),
      exactPublishedAt: DateTime.toTimeString(comment.publishedAt),
      fixed: true,
      contentObj: tiptap.toJsonObject(comment.content),
    })
  }

  // 设置首位固定评论
  const setFirstFixedComment = async (comment: Comment, newest: boolean = false) => {
    if (
      lastBreadcrumbIndex.value > 0 &&
      (commentList.value.length == 0 || !commentList.value[0].fixed)
    ) {
      if (newest) {
        const res: ResponseData = await commentApi.loadById(comment.id)
        if (res?.data) {
          updateCommentListByFirstFixedComment(res.data)
        }
      } else {
        updateCommentListByFirstFixedComment(comment)
      }
    }
  }

  // 加载当前评论列表
  const loadCurrentCommentList = (reset: boolean = true) => {
    // 如果已经在加载中，避免重复加载
    if (commentLoading.value && !reset) {
      return
    }

    // 如果已经没有更多数据，且不是主动重置
    if (commentNoMore.value && !reset) {
      // 如果没有数据，则重置加载状态让用户可以重试
      if (commentList.value.length === 0) {
        commentNoMore.value = false
      } else {
        return
      }
    }

    if (reset) {
      resetCommentList()
    }

    loadCommentList(lastBreadcrumbComment.value)
  }

  // 加载评论列表
  const loadCommentList = (comment: Comment) => {
    // 第一检查，避免重复加载或无数据可加载
    if (commentLoading.value || commentNoMore.value) {
      return
    }

    // 设置loading状态
    commentLoading.value = true
    const loadSize = 5

    // 使用节流函数控制频率
    frequencyLimit.throttle(
      ARTICLE_COMMENTS,
      () => {
        // 获取分页ID
        const id =
          commentList.value.length > 0 ? commentList.value[commentList.value.length - 1].id : ''

        // 在执行API调用前再次检查，确保状态一致性
        if (commentNoMore.value) {
          commentLoading.value = false
          return
        }

        Promise.allSettled([
          commentApi
            .load(
              `?articleId=${getArticleId()}&id=${id}&parentCommentId=${comment?.id}&loadSize=${loadSize}&sortType=${sortType.value}`,
            )
            .catch((error) => {
              // 处理评论加载错误
              if (error.response && error.response.status === 403) {
                // 显示权限错误，并重置加载状态
                commentLoading.value = false
                // 不添加提示消息
                throw error // 继续传播错误，以便Promise.allSettled能捕获
              }
              throw error
            }),
          setFirstFixedComment(comment, true),
        ])
          .then((settledRes: PromiseSettledResult<ResponseData | void>[]) => {
            // 只有当第一个Promise不是rejected状态时才处理数据
            if (settledRes[0].status === 'fulfilled' && settledRes[0]?.value?.data) {
              const res = settledRes[0]?.value
              const dataList = res.data
              // 处理空数据情况
              if (dataList?.length == 0) {
                commentNoMore.value = true
                return
              }
              // 处理最后一页数据
              if (dataList.length < loadSize) {
                commentNoMore.value = true
              }
              // 将数据添加到列表
              addCommentList(dataList)
              setShowReplyListBtn()
              // 使用nextTick确保DOM更新后再重置滚动触发器
              nextTick(() => {
                resetScrollTrigger()
              })
            } else {
              // 处理响应但没有数据的情况
              commentNoMore.value = true
            }
          })
          .catch((error) => {
            console.error('加载评论失败:', error)
            // 出错时恢复状态，允许用户重试
            commentNoMore.value = false
          })
          .finally(() => {
            // 无论如何，加载完成后取消loading状态
            commentLoading.value = false
          })
      },
      300,
    )
  }

  return {
    commentList,
    commentRefs,
    commentLoading,
    commentNoMore,
    commentScrollTrigger,
    flashCommentId,
    breadcrumb,
    isLastBreadcrumb,
    lastBreadcrumbIndex,
    lastBreadcrumbComment,
    showReplyListBtn,
    hasCommentPermission,
    sortType,
    resetBreadcrumb,
    addBreadcrumb,
    resetCommentList,
    updateCommentRef,
    locationComment,
    loadCurrentCommentList,
    loadCommentList,
    scrollToComment,
    setFirstFixedComment,
    addCommentList,
    getCommentId,
    commentStore,
    initCommentPermission,
  }
}
