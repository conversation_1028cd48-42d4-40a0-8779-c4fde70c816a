package com.shenmo.wen.app.core.article.exception;

import com.shenmo.wen.common.exception.enumeration.ExceptionEnum;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnumOption;
import com.shenmo.wen.common.exception.enumeration.ExceptionType;
import org.springframework.http.HttpStatus;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@ExceptionType(type = ArticleException.class, module = ArticleExceptionEnum.MODULE)
public enum ArticleExceptionEnum implements ExceptionEnum {

    /**
     * 当前用户未拥有文章修改权限
     */
    ARTICLE_MODIFY_PERMISSION(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "不是你的别乱弄！")),

    /**
     * 文章等级不可超过用户自身等级
     */
    ARTICLE_LEVEL_CONTROL(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "等级不够，去发点啥吧~")),

    /**
     * 当前用户未拥有文章查看权限
     */
    ARTICLE_LOOK_PERMISSION(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "哎呀，这篇文章看不了噢~")),

    /**
     * 文章不存在
     */
    ARTICLE_NOT_EXISTS(ExceptionEnumOption.of(HttpStatus.FORBIDDEN, "文章消失了...")),
    ;

    public static final String MODULE = "005";

    /**
     * 异常枚举选项
     */
    private final ExceptionEnumOption exceptionEnumOption;

    ArticleExceptionEnum(ExceptionEnumOption exceptionEnumOption) {

        this.exceptionEnumOption = exceptionEnumOption;
    }

    @Override
    public ExceptionEnumOption getOption() {

        return exceptionEnumOption;
    }
}
