package com.shenmo.wen.app.core.notification.controller;

import com.shenmo.wen.app.core.notification.pojo.param.WenNotificationLoadParam;
import com.shenmo.wen.app.core.notification.pojo.vo.WenNotificationVo;
import com.shenmo.wen.app.core.notification.service.WenNotificationService;
import com.shenmo.wen.common.pojo.response.PageResult;
import com.shenmo.wen.common.pojo.response.ResponseData;

import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import cn.dev33.satoken.stp.StpUtil;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("notification")
@RequiredArgsConstructor
public class WenNotificationController {
    private final WenNotificationService service;

    @GetMapping
    public ResponseData<PageResult<WenNotificationVo>> load(@Validated WenNotificationLoadParam param) {
        return ResponseData.success(service.load(param));
    }

    @PutMapping("/read/{id}")
    public ResponseData<Void> read(@PathVariable("id") Long id) {
        service.read(id);
        return ResponseData.success();
    }

    @PutMapping("/read-all")
    public ResponseData<Void> readAll() {
        final long loginId = StpUtil.getLoginIdAsLong();
        service.readAll(loginId);
        return ResponseData.success();
    }

    @GetMapping("/total-unread")
    public ResponseData<Long> totalUnread() {
        return ResponseData.success(service.totalUnread());
    }
}
