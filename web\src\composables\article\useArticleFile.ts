import { ref } from 'vue'

import { ArticlePublishedScope } from '@/constants/article_published_scope.constants'
import type { TiptapEditorRef, ArticleFormRef } from '@/types/component-refs.types'
import logger from '@/utils/log'
import message from '@/utils/message'

export function useArticleFile() {
  const articleFileInputRef = ref<HTMLInputElement | null>(null)

  // 读取文件内容的工具函数
  const readFileContent = (
    file: File,
    articleForm: ArticleFormRef,
    articleTiptapEditorRef: { value: TiptapEditorRef },
  ) => {
    const reader = new FileReader()
    reader.readAsText(file, 'UTF-8')
    reader.onload = (e) => {
      const fileContent = e.target?.result as string
      const lines = fileContent.split('\n')
      logger.debug('import lines: ', lines)

      // 按需获取各部分内容
      const title = (lines[0] ? lines[0].substring(1).trim() : '') || ''
      const tags = lines[1]
        ? lines[1]
            .replace('>', '')
            .split(',')
            .filter(Boolean)
            .map((tag) => tag.trim())
        : []
      const levelScope = lines[9]?.split('|')
      const operationLevel = parseInt(levelScope![0]!.replace('>', '').trim()) || 0
      const publishedScope = parseInt(levelScope![1]!.trim()) || ArticlePublishedScope.PERSONAL
      const contentLines = lines.slice(12)
      const content = contentLines.join('\n')
      const editor = articleTiptapEditorRef.value.editor

      if (!editor) {
        message.error('编辑器尚未准备好')
        return
      }

      try {
        // 假设 Tiptap 配置了 Markdown 扩展
        if (editor.storage.markdown) {
          editor.commands.clearContent(false)
          const prosemirrorJSON = editor.storage.markdown.parser.parse(content)
          editor.commands.setContent(prosemirrorJSON || content, true)
        } else {
          editor.commands.setContent(content, true)
          message.warning('Markdown 格式可能无法完全解析')
        }

        articleForm.value = {
          ...articleForm.value,
          title,
          tags,
          operationLevel,
          publishedScope,
          contentObj: editor.getJSON(), // 更新 contentObj
        }
      } catch (error) {
        logger.error('Error parsing or setting markdown content:', error)
        message.error('解析 Markdown 内容时出错')
        editor.commands.setContent(content, true) // 尝试设置原始内容
        articleForm.value = {
          ...articleForm.value,
          title,
          tags,
          operationLevel,
          publishedScope,
          contentObj: editor.getJSON(), // 更新 contentObj
        }
      }
    }

    reader.onerror = () => {
      message.warning('文件貌似有问题~')
    }
  }

  // 处理文件点击
  const handleArticleFileClick = () => {
    articleFileInputRef.value?.click()
  }

  // 处理文件变化
  const handleArticleFileChange = (
    event: Event,
    articleForm: ArticleFormRef,
    articleTiptapEditorRef: { value: TiptapEditorRef },
  ) => {
    const file = (event.target as HTMLInputElement).files?.[0]
    if (!file) return

    readFileContent(file, articleForm, articleTiptapEditorRef)

    // 清空 input 的值，确保同一文件可以重复上传
    if (articleFileInputRef.value) {
      articleFileInputRef.value.value = ''
    }
  }

  return {
    // 状态
    articleFileInputRef,

    // 方法
    handleArticleFileClick,
    handleArticleFileChange,
  }
}
