/**
 * Comprehensive error handling for drag operations
 */
export enum DragErrorType {
  DOM_MANIPULATION = 'DOM_MANIPULATION',
  POSITION_CALCULATION = 'POSITION_CALCULATION',
  NODE_VALIDATION = 'NODE_VALIDATION',
  TRANSACTION_EXECUTION = 'TRANSACTION_EXECUTION',
  PERFORMANCE_DEGRADATION = 'PERFORMANCE_DEGRADATION',
  ACCESSIBILITY_FAILURE = 'ACCESSIBILITY_FAILURE'
}

export interface DragError {
  type: DragErrorType
  message: string
  context: Record<string, any>
  timestamp: number
  recoverable: boolean
  suggestedAction?: string
}

export class DragErrorHandler {
  private static errorLog: DragError[] = []
  private static maxLogSize = 100
  private static errorCallbacks: ((error: DragError) => void)[] = []

  static handleError(
    type: DragErrorType,
    error: Error,
    context: Record<string, any> = {},
    recoverable: boolean = true
  ): DragError {
    const dragError: DragError = {
      type,
      message: error.message,
      context: {
        ...context,
        stack: error.stack,
        userAgent: navigator.userAgent,
        timestamp: Date.now()
      },
      timestamp: Date.now(),
      recoverable,
      suggestedAction: this.getSuggestedAction(type)
    }

    this.logError(dragError)
    this.notifyCallbacks(dragError)
    
    if (!recoverable) {
      this.handleCriticalError(dragError)
    }

    return dragError
  }

  static handleDOMError(error: Error, element?: HTMLElement): DragError {
    return this.handleError(
      DragErrorType.DOM_MANIPULATION,
      error,
      {
        elementTag: element?.tagName,
        elementClass: element?.className,
        elementId: element?.id,
        parentElement: element?.parentElement?.tagName
      },
      true
    )
  }

  static handlePositionError(error: Error, position?: number): DragError {
    return this.handleError(
      DragErrorType.POSITION_CALCULATION,
      error,
      {
        position,
        documentLength: document.body.textContent?.length
      },
      true
    )
  }

  static handleTransactionError(error: Error, sourcePos?: number, targetPos?: number): DragError {
    return this.handleError(
      DragErrorType.TRANSACTION_EXECUTION,
      error,
      {
        sourcePos,
        targetPos,
        operation: 'drag-drop'
      },
      false // Transaction errors are usually not recoverable
    )
  }

  private static getSuggestedAction(type: DragErrorType): string {
    const suggestions: Record<DragErrorType, string> = {
      [DragErrorType.DOM_MANIPULATION]: 'Refresh drag handles and retry operation',
      [DragErrorType.POSITION_CALCULATION]: 'Recalculate positions and validate document structure',
      [DragErrorType.NODE_VALIDATION]: 'Check node type compatibility and nesting rules',
      [DragErrorType.TRANSACTION_EXECUTION]: 'Undo last operation and restore document state',
      [DragErrorType.PERFORMANCE_DEGRADATION]: 'Enable virtual scrolling and reduce update frequency',
      [DragErrorType.ACCESSIBILITY_FAILURE]: 'Fallback to keyboard navigation and announce error'
    }

    return suggestions[type] || 'Contact support if issue persists'
  }

  private static logError(error: DragError): void {
    this.errorLog.push(error)
    
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog.shift()
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error(`[DragHandle] ${error.type}:`, error.message, error.context)
    }
  }

  private static notifyCallbacks(error: DragError): void {
    this.errorCallbacks.forEach(callback => {
      try {
        callback(error)
      } catch (callbackError) {
        console.error('Error in error callback:', callbackError)
      }
    })
  }

  private static handleCriticalError(error: DragError): void {
    // For critical errors, we might want to disable the extension
    console.error('Critical drag handle error:', error)
    
    // Emit event to disable extension
    if (typeof window !== 'undefined') {
      window.dispatchEvent(new CustomEvent('dragHandleCriticalError', {
        detail: error
      }))
    }
  }

  static onError(callback: (error: DragError) => void): void {
    this.errorCallbacks.push(callback)
  }

  static getErrorLog(): DragError[] {
    return [...this.errorLog]
  }

  static clearErrorLog(): void {
    this.errorLog = []
  }

  static getErrorStats(): {
    totalErrors: number
    errorsByType: Record<DragErrorType, number>
    recoverableErrors: number
    criticalErrors: number
  } {
    const stats = {
      totalErrors: this.errorLog.length,
      errorsByType: {} as Record<DragErrorType, number>,
      recoverableErrors: 0,
      criticalErrors: 0
    }

    this.errorLog.forEach(error => {
      stats.errorsByType[error.type] = (stats.errorsByType[error.type] || 0) + 1
      
      if (error.recoverable) {
        stats.recoverableErrors++
      } else {
        stats.criticalErrors++
      }
    })

    return stats
  }
}