package com.shenmo.wen.app.core.comment.pojo.param;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class WenCommentSaveParam {

    @NotBlank(message = "评论内容不可为空")
    private String content;

    @NotNull(message = "文章id不可为空")
    private Long articleId;

    /**
     * 父评论ID，如果该评论是回复其他评论的，则记录被回复评论的ID，可为空，表示是直接对文章的评论，而非回复其他评论。
     */
    private Long parentCommentId;
}
