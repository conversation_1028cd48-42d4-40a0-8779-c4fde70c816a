package com.shenmo.wen.common.objectstorage.template;

import com.shenmo.wen.common.constant.StringConstant;
import com.shenmo.wen.common.objectstorage.response.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.tika.Tika;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.io.File;
import java.io.InputStream;
import java.util.List;

/**
 * 对象存储模板
 *
 * <AUTHOR>
 * @version 2.0.0
 */
public interface ObjectStorageTemplate {

    /**
     * 初始化对象zip包
     *
     * @param bucket         桶
     * @param objectsZipPath 对象zip包路径
     * <AUTHOR>
     */
    void initObjects(String bucket, String objectsZipPath);

    /**
     * 获取Tika文件元数据提取工具
     *
     * @return Tika文件元数据提取工具
     * <AUTHOR>
     */
    Tika tika();

    /**
     * 判断桶是否存在
     *
     * @param bucket 桶
     * @return 桶是否存在
     * <AUTHOR>
     */
    boolean bucketExists(String bucket);

    /**
     * 创建桶
     * <p>
     * 桶不存在才创建
     *
     * @param bucket 桶
     * <AUTHOR>
     */
    void makeBucket(String... bucket);

    /**
     * 获取所有桶
     *
     * @return 桶响应列表
     * <AUTHOR>
     */
    @NonNull
    List<? extends BucketResponse<?>> listBuckets();

    /**
     * 获取所有桶名称
     *
     * @return 桶列表
     * <AUTHOR>
     */
    @NonNull
    List<String> listBucketNames();

    /**
     * 获取公共可读的桶策略
     *
     * @param bucket 桶
     * @return 公共可读的桶策略
     * <AUTHOR>
     */
    String getPublicReadableBucketPolicy(String bucket);

    /**
     * 设置公共可读的桶策略
     *
     * @param bucket 桶
     * <AUTHOR>
     */
    void setPublicReadableBucketPolicy(String bucket);

    /**
     * 移除桶
     * <p>
     * 删除桶中文件在删桶
     *
     * @param bucket 桶
     * <AUTHOR>
     */
    void removeBucket(String... bucket);

    /**
     * 设置桶策略
     *
     * @param bucket       桶
     * @param policyConfig 策略配置
     * <AUTHOR>
     */
    void setBucketPolicy(String bucket, String policyConfig);

    /**
     * 将前缀进行格式化, 补充'/'
     *
     * @param prefix 对象前缀
     * @return 格式化前缀
     * <AUTHOR>
     */
    default String prefixFormatting(@Nullable String prefix) {
        if (StringUtils.isBlank(prefix)) {
            return StringConstant.EMPTY;
        }
        final String slash = StringConstant.SLASH;
        if (!prefix.endsWith(slash)) {
            prefix = prefix + slash;
        }
        return prefix;
    }

    /**
     * 判定对象是否存在
     *
     * @param bucket 桶名称
     * @param object 对象
     * @return 对象是否存在
     * <AUTHOR>
     */
    boolean existObject(String bucket, String object);

    /**
     * 判断对象前缀是否存在
     *
     * @param bucket 桶名称
     * @param prefix 对象前缀
     * @return 对象前缀是否存在
     * <AUTHOR>
     */
    boolean existPrefix(String bucket, String prefix);

    /**
     * 新增对象
     * <p>
     * 桶不存在时抛出异常
     *
     * @param bucket      桶
     * @param object      对象
     * @param inputStream 输入流
     * @param size        对象大小
     * @param contentType 对象内容类型
     * @return 存储对象响应
     * <AUTHOR>
     */
    @NonNull
    PutObjectResponse putObject(String bucket, String object, InputStream inputStream, long size, String contentType);

    /**
     * 新增对象
     * <p>
     * 桶不存在时抛出异常
     *
     * @param bucket      桶
     * @param object      对象
     * @param bytes       字节数组
     * @param size        对象大小
     * @param contentType 对象内容类型
     * @return 存储对象响应
     * <AUTHOR>
     */
    @NonNull
    PutObjectResponse putObject(String bucket, String object, byte[] bytes, long size, String contentType);

    /**
     * 上传对象
     * <p>
     * 桶不存在时抛出异常
     *
     * @param bucket   桶
     * @param object   对象
     * @param filePath 文件路径
     * @return 上传对象响应
     * <AUTHOR>
     */
    @NonNull
    UploadObjectResponse uploadObject(String bucket, String object, String filePath);

    /**
     * 上传对象列表
     * <p>
     * 桶不存在时抛出异常
     *
     * @param bucket       桶
     * @param prefix       对象前缀
     * @param rootDir      文件列表根目录
     * @param filePathList 文件路径列表
     * <AUTHOR>
     */
    void uploadObjects(String bucket, @Nullable String prefix, @Nullable File rootDir, List<String> filePathList);

    /**
     * 上传对象列表
     * <p>
     * 桶不存在时抛出异常
     *
     * @param bucket       桶
     * @param prefix       对象前缀
     * @param filePathList 文件路径列表
     * <AUTHOR>
     */
    void uploadObjects(String bucket, @Nullable String prefix, List<String> filePathList);


    /**
     * 上传对象zip包
     * <p>
     * 桶不存在时抛出异常
     *
     * @param bucket     桶
     * @param prefix     对象前缀
     * @param objectsZip 对象zip压缩包
     * <AUTHOR>
     */
    void uploadObjects(String bucket, @Nullable String prefix, File objectsZip);

    /**
     * 复制对象
     * <p>
     * 桶不存在时抛出异常
     *
     * @param originBucket 源桶
     * @param originObject 源对象
     * @param targetBucket 目标桶
     * @param targetObject 目标对象
     * @return 复制对象响应
     * <AUTHOR>
     */
    @NonNull
    CopyObjectResponse copyObject(String originBucket, String originObject, String targetBucket, String targetObject);

    /**
     * 复制对象列表
     * <p>
     * 桶不存在时抛出异常
     *
     * @param originBucket 源桶
     * @param originPrefix 源对象前缀
     * @param targetBucket 目标桶
     * @param targetPrefix 目标对象前缀
     * <AUTHOR>
     */
    void copyObjects(String originBucket, @Nullable String originPrefix, String targetBucket, @Nullable String targetPrefix);

    /**
     * 移动对象
     * <p>
     * 桶不存在时抛出异常
     *
     * @param originBucket 源桶
     * @param originObject 源对象
     * @param targetBucket 目标桶
     * @param targetObject 目标对象
     * @return 移动对象响应
     * <AUTHOR>
     */
    @NonNull
    MoveObjectResponse moveObject(String originBucket, String originObject, String targetBucket, String targetObject);

    /**
     * 移动对象列表
     * <p>
     * 桶不存在时抛出异常
     *
     * @param originBucket 源桶
     * @param originPrefix 源对象前缀
     * @param targetBucket 目标桶
     * @param targetPrefix 目标对象前缀
     * <AUTHOR>
     */
    void moveObjects(String originBucket, @Nullable String originPrefix, String targetBucket, @Nullable String targetPrefix);

    /**
     * 获取对象
     *
     * @param bucket 桶
     * @param object 对象
     * @return 获取对象响应
     * <AUTHOR>
     */
    @NonNull
    GetObjectResponse<?> getObject(String bucket, String object);

    /**
     * 获取某个桶下指定前缀与数量的对象
     *
     * @param bucket    桶
     * @param prefix    对象前缀
     * @param maxKeys   对象最大数量,1-1000
     * @param recursive 是否递归
     * @return 列举对象响应
     * <AUTHOR>
     */
    @NonNull
    ListObjectResponse<?> listObjects(String bucket, @Nullable String prefix, int maxKeys, boolean recursive);

    /**
     * 获取某个桶下指定前缀的所有对象
     *
     * @param bucket    桶
     * @param prefix    对象前缀
     * @param recursive 是否递归
     * @return 列举对象响应
     * <AUTHOR>
     */
    @NonNull
    ListObjectResponse<?> listObjects(String bucket, @Nullable String prefix, boolean recursive);

    /**
     * 递归获取某个桶下指定前缀的所有对象
     *
     * @param bucket 桶
     * @param prefix 对象前缀
     * @return 列举对象响应
     * <AUTHOR>
     */
    @NonNull
    ListObjectResponse<?> listObjects(String bucket, @Nullable String prefix);

    /**
     * 递归获取某个桶下的所有对象
     *
     * @param bucket 桶
     * @return 列举对象响应
     * <AUTHOR>
     */
    @NonNull
    ListObjectResponse<?> listObjects(String bucket);

    /**
     * 移除对象
     *
     * @param bucket 桶
     * @param object 对象
     * <AUTHOR>
     */
    void removeObject(String bucket, String... object);

    /**
     * 移除某个桶下指定前缀的所有对象
     *
     * @param bucket    桶
     * @param prefix    对象前缀
     * @param recursive 是否递归
     * <AUTHOR>
     */
    void removeObjects(String bucket, @Nullable String prefix, boolean recursive);

    /**
     * 移除某个桶下指定前缀的所有对象
     * <p>
     * 默认递归删除
     *
     * @param bucket 桶
     * @param prefix 对象前缀
     * <AUTHOR>
     */
    void removeObjects(String bucket, @Nullable String prefix);

    /**
     * 移除某个桶下的所有对象
     * <p>
     * 默认递归删除
     *
     * @param bucket 桶
     * <AUTHOR>
     */
    void removeObjects(String bucket);

    /**
     * 获取指定对象url
     *
     * @param bucket 桶
     * @param object 对象
     * @param expiry url过期时间, 天
     * @return 对象url
     * <AUTHOR>
     */
    @NonNull
    String getPresignedObjectUrl(String bucket, String object, int expiry);

    /**
     * 下载对象到本地
     *
     * @param bucket   桶
     * @param object   对象
     * @param filePath 文件路径
     * <AUTHOR>
     */
    void downloadObject(String bucket, String object, String filePath);

    /**
     * 批量下载对象到本地
     *
     * @param bucket        桶
     * @param prefix        对象前缀
     * @param fileDir       文件目录
     * @param prefixReserve 是否保留对象前缀
     * <AUTHOR>
     */
    void downloadObjects(String bucket, String prefix, String fileDir, boolean prefixReserve);

    /**
     * 批量下载对象到本地
     * <p>
     * 默认不保留对象前缀
     *
     * @param bucket  桶
     * @param prefix  对象前缀
     * @param fileDir 文件目录
     * <AUTHOR>
     */
    void downloadObjects(String bucket, String prefix, String fileDir);

    /**
     * 创建并设置桶策略
     *
     * @param bucket       桶
     * @param policyConfig 策略配置
     * <AUTHOR>
     */
    void makeSetBucketPolicy(String bucket, String policyConfig);

    /**
     * 创建并设置公共可读的桶策略
     *
     * @param bucket 桶
     * <AUTHOR>
     */
    void makeSetPublicReadableBucketPolicy(String bucket);

    /**
     * 判断创建桶并新增对象
     *
     * @param bucket      桶
     * @param object      对象
     * @param inputStream 输入流
     * @param size        对象大小
     * @param contentType 对象内容类型
     * @return 存储对象响应
     * <AUTHOR>
     */
    @NonNull
    PutObjectResponse makePutObject(String bucket, String object, InputStream inputStream, long size, String contentType);

    /**
     * 判断创建桶并新增对象
     *
     * @param bucket      桶
     * @param object      对象
     * @param bytes       字节数组
     * @param size        对象大小
     * @param contentType 对象内容类型
     * @return 存储对象响应
     * <AUTHOR>
     */
    @NonNull
    PutObjectResponse makePutObject(String bucket, String object, byte[] bytes, long size, String contentType);

    /**
     * 判断创建桶并上传对象
     *
     * @param bucket   桶
     * @param object   对象
     * @param filename 文件名
     * @return 上传对象响应
     * <AUTHOR>
     */
    @NonNull
    UploadObjectResponse makeUploadObject(String bucket, String object, String filename);

    /**
     * 判断创建桶并上传对象列表
     *
     * @param bucket       桶
     * @param prefix       对象前缀
     * @param rootDir      文件列表根目录
     * @param filePathList 文件路径列表
     * <AUTHOR>
     */
    void makeUploadObjects(String bucket, String prefix, File rootDir, List<String> filePathList);

    /**
     * 判断创建桶并上传对象列表
     *
     * @param bucket       桶
     * @param prefix       对象前缀
     * @param filePathList 文件路径列表
     * <AUTHOR>
     */
    void makeUploadObjects(String bucket, String prefix, List<String> filePathList);
}
