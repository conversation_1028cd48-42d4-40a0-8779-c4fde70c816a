// 组件引用类型定义
export interface CommentDanmakuRef {
  danmakuLoop: boolean
  pause: () => void
  play: () => void
  subscribeComment: () => void
  unsubscribeComment: () => void
  clearDanmaku: () => void
  resize: () => void
  addCommentList: (comments: unknown[]) => void
}

export interface ArticleListRef {
  resetList: () => void
  loadArticles: (loadMore: boolean, signal?: AbortSignal) => Promise<void>
}

export interface TiptapEditorRef {
  editor: {
    isEmpty: boolean
    getJSON: () => unknown
    commands: {
      clearContent: (emitUpdate?: boolean) => void
      setContent: (content: unknown, emitUpdate?: boolean) => void
    }
    storage: {
      markdown?: {
        parser: {
          parse: (content: string) => unknown
        }
      }
    }
  }
}

export interface ArticleFormRef {
  value: {
    id: string
    title: string
    tags: string[]
    operationLevel: number
    publishedScope: number
    contentObj: unknown
    shareUsers: unknown[]
  }
}
