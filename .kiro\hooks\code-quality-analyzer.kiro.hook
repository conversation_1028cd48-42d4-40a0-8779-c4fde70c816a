{"enabled": true, "name": "Code Quality Analyzer", "description": "Monitors source code files for changes and provides automated code quality analysis including code smells, design patterns, and best practices suggestions", "version": "1", "when": {"type": "fileEdited", "patterns": ["**/*.js", "**/*.ts", "**/*.vue", "**/*.java", "**/*.py", "**/*.jsx", "**/*.tsx"]}, "then": {"type": "askAgent", "prompt": "Analyze the modified code in the changed files for potential improvements. Focus on:\nCode Smells: Identify any code smells like long methods, large classes, duplicate code, or complex conditionals.\nEnsure that the number of lines of code in any function or method does not exceed 80 lines.\nEnsure that the total number of lines in any .ts, .scss, or .vue file does not exceed 300 lines; the total number of lines in any .java file does not exceed 1000 lines.\nDesign Patterns: Suggest appropriate design patterns that could improve the code structure\nBest Practices: Check adherence to language-specific and framework-specific best practices\nReadability: Suggest improvements for variable naming, code organization, and documentation\nMaintainability: Identify areas that could be refactored for better maintainability\nPerformance: Suggest optimizations that could improve performance without breaking functionality\nFor each suggestion, provide:\nClear explanation of the issue\nSpecific recommendation for improvement\nCode example if applicable\nRationale for why this change would be beneficial\nConsider the technology stack: Vue 3 with TypeScript, Java Spring Boot, and the existing project structure. Maintain compatibility with existing functionality while suggesting improvements."}}