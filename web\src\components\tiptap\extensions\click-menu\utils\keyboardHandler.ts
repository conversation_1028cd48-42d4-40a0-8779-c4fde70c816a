import type { Editor } from '@tiptap/core'
import { KeyboardAccessibilityUtils } from '../../shared/utils/keyboardAccessibility'
import { MENU_NAVIGATION_SHORTCUTS, QUICK_TRANSFORMATION_SHORTCUTS } from '../constants/shortcuts'
import type { MenuState } from '../types'

/**
 * Keyboard shortcut handler for ClickMenu extension
 */
export class ClickMenuKeyboardHandler {
  constructor(
    private editor: Editor,
    private storage: {
      isMenuVisible: boolean
      menuState: MenuState | null
      selectedIndex: number
    },
    private getVisibleMenuItems: () => any[],
    private navigateToFirst: () => boolean,
    private navigateToLast: () => boolean
  ) {}

  /**
   * Create keyboard shortcuts configuration
   */
  createShortcuts(enabled: boolean, keyboardNavigation: boolean): Record<string, () => boolean> {
    if (!enabled || !keyboardNavigation) return {}

    const enhancedShortcuts = KeyboardAccessibilityUtils.getEnhancedClickMenuKeyboardShortcuts()
    const shortcuts: Record<string, () => boolean> = {}

    Object.entries(enhancedShortcuts).forEach(([key, command]) => {
      shortcuts[key] = () => this.handleShortcut(key, command)
    })

    return shortcuts
  }

  /**
   * Handle individual keyboard shortcut
   */
  private handleShortcut(key: string, command: string): boolean {
    // Handle menu-specific shortcuts only when menu is visible
    if (MENU_NAVIGATION_SHORTCUTS.includes(key as any) && !this.storage.isMenuVisible) {
      return false
    }

    // Handle quick transformation shortcuts ONLY when menu is visible
    if (QUICK_TRANSFORMATION_SHORTCUTS.includes(key as any)) {
      return this.handleQuickTransformation(key, command)
    }

    // Handle standard menu navigation
    return this.handleMenuNavigation(command)
  }

  /**
   * Handle quick transformation shortcuts
   */
  private handleQuickTransformation(key: string, command: string): boolean {
    if (!this.storage.isMenuVisible) {
      return false
    }
    
    const commandFn = this.editor.commands[command as keyof typeof this.editor.commands]
    if (typeof commandFn === 'function') {
      return (commandFn as any)()
    }
    return false
  }

  /**
   * Handle menu navigation commands
   */
  private handleMenuNavigation(command: string): boolean {
    switch (command) {
      case 'navigateMenuUp':
        return this.storage.isMenuVisible ? (this.editor.commands as any).navigateClickMenu('up') : false
      case 'navigateMenuDown':
        return this.storage.isMenuVisible ? (this.editor.commands as any).navigateClickMenu('down') : false
      case 'navigateToFirstMenuItem':
        return this.storage.isMenuVisible ? this.navigateToFirst() : false
      case 'navigateToLastMenuItem':
        return this.storage.isMenuVisible ? this.navigateToLast() : false
      case 'selectMenuItem':
        return this.storage.isMenuVisible ? (this.editor.commands as any).selectClickMenuItem() : false
      case 'hideClickMenu':
        return this.storage.isMenuVisible ? (this.editor.commands as any).hideClickMenu() : false
      default:
        return false
    }
  }
}