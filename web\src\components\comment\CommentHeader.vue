<template>
  <div class="comment-title-container">
    <div class="comment-header-top">
      <NBreadcrumb>
        <NBreadcrumbItem
          v-for="(b, i) in breadcrumb"
          :key="i"
          @click="handleBreadcrumbClick(i)"
          class="breadcrumb-item"
        >
          <NGradientText class="breadcrumb-text cursor-pointer">
            {{ b.publisher }}
            <NAvatar
              v-if="b.id && b.publisherAvatar"
              object-fit="cover"
              :size="22"
              round
              :src="fileApi.getResourceURL(b.publisherAvatar)"
            />
          </NGradientText>
        </NBreadcrumbItem>
      </NBreadcrumb>
      <UserInfoGroup @locationComment="$emit('locationComment')" />
    </div>
    <div class="comment-header-bottom">
      <NRadioGroup size="small" :value="modelValue" @update:value="handleSortChange">
        <NRadioButton value="0">热评</NRadioButton>
        <NRadioButton value="1">最新</NRadioButton>
        <NRadioButton value="2">回复</NRadioButton>
      </NRadioGroup>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { NBreadcrumb, NBreadcrumbItem, NGradientText, NAvatar, NRadioGroup, NRadioButton } from 'naive-ui'

import fileApi from '@/api/file'
import UserInfoGroup from '@/components/UserInfoGroup.vue'
import type { Comment } from '@/types/comment.types'

const props = defineProps<{
  breadcrumb: Comment[]
  modelValue: string
}>()

const emit = defineEmits<{
  (e: 'breadcrumbClick', index: number): void
  (e: 'locationComment'): void
  (e: 'update:modelValue', value: string): void
}>()

const handleBreadcrumbClick = (index: number) => {
  emit('breadcrumbClick', index)
}

const handleSortChange = (value: string) => {
  emit('update:modelValue', value)
}
</script>

<style scoped lang="scss">
.comment-title-container {
  box-sizing: border-box;
  padding: 1.25rem 1.25rem 0.5rem 1.25rem;
  border-bottom: var(--border-1);
  display: flex;
  flex-direction: column;
  height: 176px;

  .comment-header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 7.5rem;
    flex-wrap: wrap;
  }

  .comment-header-bottom {
    display: flex;
    justify-content: flex-start;
    align-items: center;
  }

  :deep(.n-breadcrumb) {
    padding: 0.5rem 0;
    transition: all 0.2s ease;

    .n-breadcrumb-item {
      position: relative;

      &:last-child {
        font-weight: bold;
      }
    }
  }

  .breadcrumb-text {
    display: flex;
    align-items: center;
    font-size: 1rem;
    transition: all 0.25s ease;
    padding: 0.3rem 0;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      left: 0;
      bottom: 0;
      width: 0;
      height: 2px;
      background-color: var(--blue);
      transition: width 0.3s ease;
    }

    &:hover {
      &::before {
        width: 100%;
      }
    }

    .n-avatar {
      margin: 0 0.25rem;
      border: 1px solid rgba(200, 200, 200, 30%);
    }
  }
}

// 添加响应式设计，调整窄屏下的布局
@media (width <= 768px) {
  .comment-title-container {
    :deep(.n-breadcrumb) {
      padding: 0.3rem 0;
    }

    .breadcrumb-text {
      font-size: 0.9rem;
      padding: 0.2rem 0;
    }
  }
}
</style>
