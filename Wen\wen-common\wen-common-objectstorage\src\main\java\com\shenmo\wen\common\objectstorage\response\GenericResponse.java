package com.shenmo.wen.common.objectstorage.response;

import lombok.Getter;
import org.springframework.lang.NonNull;

/**
 * 通用响应
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Getter
public abstract class GenericResponse<O> {

    /**
     * 源对象
     */
    @NonNull
    protected O origin;

    /**
     * 构造方法
     *
     * @param origin 源对象
     * <AUTHOR>
     */
    protected GenericResponse(@NonNull O origin) {
        this.origin = origin;
    }
}
