package com.shenmo.wen.app.core.file;

import com.shenmo.wen.common.constant.StringConstant;
import com.shenmo.wen.common.objectstorage.properties.ObjectStorageProperties;
import com.shenmo.wen.common.objectstorage.template.ObjectStorageTemplate;
import com.shenmo.wen.common.util.spring.SpringRedisUtils;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Component
@RequiredArgsConstructor
public class BucketInit implements InitializingBean {
    private final ObjectStorageTemplate objectStorageTemplate;
    private final ObjectStorageProperties objectStorageProperties;

    @Override
    public void afterPropertiesSet() {
        for (String allowAccessBucket : objectStorageProperties.getAllowAccessBuckets()) {
            if (!SpringRedisUtils.hasKey(allowAccessBucket)) {
                objectStorageTemplate.makeSetPublicReadableBucketPolicy(allowAccessBucket);
                SpringRedisUtils.set(allowAccessBucket, StringConstant.EMPTY);
            }
        }
    }
}
