import { useMessage } from 'naive-ui'
import { ref, computed } from 'vue'

import commentApi from '@/api/comment'
import { HOME_SEARCH_CONDITION } from '@/constants/storage.constants'
import type { CommentDanmakuRef, ArticleListRef } from '@/types/component-refs.types'
import type { ResponseData } from '@/types/response_data.types'
import type { SearchCondition } from '@/types/search.types'
import frequencyLimit from '@/utils/frequency-limit'
import localStorage from '@/utils/local-storage'
import logger from '@/utils/log' // 引入日志工具

export function useHomeSearch() {
  const message = useMessage()

  // 搜索状态
  const isLoading = ref(false)
  const isSearching = ref(false)
  let currentRequestController: AbortController | null = null

  // 统一的搜索防抖key
  const UNIFIED_SEARCH_KEY = 'unified_search'

  // 搜索历史记录
  const searchHistory = ref<
    {
      timestamp: number
      condition: SearchCondition
      type: 'article' | 'comment'
    }[]
  >([])

  // 条件筛选
  const searchCondition = ref<SearchCondition>({
    searchKey: '',
    owner: false,
    interaction: false,
    favorite: false,
    tag: '',
  })

  // 搜索占位符
  const getSearchPlaceholder = (isCardVisible: boolean) => {
    return isCardVisible ? '感兴趣的文章' : '有意思的评论'
  }

  // 从localStorage加载搜索条件
  const loadSearchCondition = () => {
    const savedCondition = localStorage.get(HOME_SEARCH_CONDITION)
    if (savedCondition) {
      searchCondition.value = savedCondition as SearchCondition
      logger.debug('加载搜索条件:', savedCondition)
    }
  }

  // 保存搜索条件到localStorage
  const saveSearchCondition = () => {
    localStorage.set(HOME_SEARCH_CONDITION, searchCondition.value)
    logger.debug('保存搜索条件:', searchCondition.value)
  }

  // 检查是否有搜索条件
  const hasSearchCondition = computed(() => {
    return Object.entries(searchCondition.value).some(([key, value]) => {
      if (key === 'tag') return !!value
      if (typeof value === 'boolean') return value
      if (key === 'searchKey') return !!value
      return false
    })
  })

  // 搜索评论
  const searchComments = async (commentDanmakuRef: { value: CommentDanmakuRef }) => {
    if (!hasSearchCondition.value) {
      if (commentDanmakuRef.value) {
        commentDanmakuRef.value.danmakuLoop = false
      }
      return
    }

    isSearching.value = true
    isLoading.value = true

    try {
      const res: ResponseData = await commentApi.search(
        searchCondition.value,
        currentRequestController?.signal,
      )

      if (commentDanmakuRef.value) {
        commentDanmakuRef.value.addCommentList(res.data)
      }
    } catch (error: unknown) {
      if (!((error as Error).name === 'CanceledError' || (error as Error).message === 'canceled')) {
        message.error('加载评论失败，请稍后重试')
      }
    } finally {
      isLoading.value = false
      isSearching.value = false
      currentRequestController = null
    }
  }

  // 搜索文章
  const searchArticles = async (
    articleListRef: { value: ArticleListRef },
    loadMore: boolean = false,
  ) => {
    console.log('searchArticles called:', {
      loadMore,
      hasRef: !!articleListRef.value,
      isSearching: isSearching.value,
      isLoading: isLoading.value,
      searchCondition: searchCondition.value,
    })

    if (loadMore) return // 由ArticleList组件内部处理加载更多

    if (articleListRef.value) {
      isSearching.value = true
      isLoading.value = true

      try {
        // 强制重置列表并加载文章
        await articleListRef.value.resetList()
        console.log('loadArticles completed')
      } catch (error: unknown) {
        console.error('searchArticles error:', error)
        if (
          !((error as Error).name === 'CanceledError' || (error as Error).message === 'canceled')
        ) {
          message.error('加载文章失败，请稍后重试')
        }
      } finally {
        isLoading.value = false
        isSearching.value = false
        currentRequestController = null
      }
    } else {
      console.warn('articleListRef.value is null/undefined')
    }
  }

  // 统一搜索函数，增加更严格的状态管理
  const search = (
    isCardVisible: boolean,
    articleListRef: { value: ArticleListRef },
    commentDanmakuRef: { value: CommentDanmakuRef },
    loadMore: boolean = false,
  ) => {
    // 如果正在搜索中，直接返回
    if (isSearching.value) {
      logger.warn('搜索被阻止：正在进行其他搜索')
      return
    }

    // 如果正在加载中，取消之前的请求
    if (currentRequestController) {
      currentRequestController.abort()
      currentRequestController = null
    }

    // 创建新的请求控制器
    currentRequestController = new AbortController()

    // 记录搜索历史
    searchHistory.value.push({
      timestamp: Date.now(),
      condition: { ...searchCondition.value },
      type: isCardVisible ? 'article' : 'comment',
    })

    // 限制历史记录长度
    if (searchHistory.value.length > 10) {
      searchHistory.value.shift()
    }

    // 日志追踪
    logger.debug('触发搜索:', {
      isCardVisible,
      condition: searchCondition.value,
      loadMore,
    })

    // 使用统一的防抖，增加更精细的控制
    frequencyLimit.debounce(
      UNIFIED_SEARCH_KEY,
      () => {
        isSearching.value = true
        try {
          if (isCardVisible) {
            searchArticles(articleListRef, loadMore)
          } else {
            searchComments(commentDanmakuRef)
          }
        } catch (error) {
          logger.error('搜索过程中发生错误:', error)
          message.error('搜索失败，请稍后重试')
        } finally {
          // 确保在任何情况下都重置搜索状态
          setTimeout(() => {
            isSearching.value = false
          }, 500)
        }
      },
      200, // 适当增加防抖延迟，减少不必要的搜索
    )
  }

  // 处理标签选择，增加更严格的状态管理
  const handleTagSelected = (
    tagName: string,
    isCardVisible: boolean,
    articleListRef: { value: ArticleListRef },
    commentDanmakuRef: { value: CommentDanmakuRef },
  ) => {
    // 更新搜索条件的标签
    searchCondition.value.tag = tagName

    // 日志追踪
    logger.debug('标签选择:', {
      tagName,
      isCardVisible,
      currentCondition: searchCondition.value,
    })

    // 保存搜索条件到localStorage
    saveSearchCondition()

    // 强制触发搜索，确保文章列表更新
    search(isCardVisible, articleListRef, commentDanmakuRef)
  }

  // 清理函数
  const cleanup = () => {
    if (currentRequestController) {
      currentRequestController.abort()
      currentRequestController = null
    }
    // 清理搜索历史
    searchHistory.value = []
    logger.debug('清理搜索状态')
  }

  return {
    // 状态
    isLoading,
    isSearching,
    searchCondition,
    hasSearchCondition,
    searchHistory,

    // 方法
    getSearchPlaceholder,
    loadSearchCondition,
    saveSearchCondition,
    search,
    handleTagSelected,
    cleanup,
  }
}
