package com.shenmo.wen.common.objectstorage.properties;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.cloud.context.config.annotation.RefreshScope;

import java.util.ArrayList;
import java.util.List;

/**
 * 对象存储属性配置
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@RefreshScope
@ConfigurationProperties(ObjectStorageProperties.PREFIX)
public class ObjectStorageProperties {

    /**
     * 配置前缀
     */
    public static final String PREFIX = "server.object-storage";

    private List<String> allowAccessBuckets = new ArrayList<>();

    /**
     * 对象存储服务类型
     */
    private ServerType serverType = ServerType.MINIO;

    /**
     * 服务地址
     */
    private String endpoint;

    /**
     * 访问密钥
     */
    private String accessKey;

    /**
     * 签名密钥
     */
    private String secretKey;

    /**
     * 对象存储服务类型
     *
     * <AUTHOR>
     * @version 1.0.0
     */
    public enum ServerType {

        /**
         * minio
         */
        MINIO,

        /**
         * 阿里云oss
         */
        ALI_OSS
    }
}
