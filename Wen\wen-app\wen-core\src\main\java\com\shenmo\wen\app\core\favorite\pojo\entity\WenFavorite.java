package com.shenmo.wen.app.core.favorite.pojo.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shenmo.wen.common.mybatis.handler.TimestampToLongTypeHandler;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@TableName(value = "wen_favorite", autoResultMap = true)
public class WenFavorite {
    /**
     * 收藏ID，作为每条收藏记录的唯一标识，在数据库中充当主键，不能为空。
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 用户ID，标识进行收藏操作的用户，可为空（取决于具体业务场景，可能存在特殊情况的收藏记录）。
     */
    private Long userId;

    /**
     * 目标类型，用整数来表示被收藏对象的类型，0代表评论，1代表文章，可为空（按照业务规则来确定其取值情况）。
     */
    private Integer targetType;

    /**
     * 目标ID，依据目标类型的不同，对应文章的ID或者评论的ID，用于精准关联到具体的收藏目标，可为空（结合业务逻辑判断其合法性）。
     */
    private Long targetId;

    /**
     * 收藏时间，记录收藏操作发生的时间戳，默认取值为当前时间，在新增收藏记录时会自动赋值。
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long ctTm;
}
