import { ref, onMounted, watch, nextTick } from 'vue'
import { useRoute } from 'vue-router'

import articleApi from '@/api/article'
import router from '@/router'
import { useArticleStore } from '@/stores/index'
import { type Article } from '@/types/article.types'
import type { ResponseData } from '@/types/response_data.types'
import dateTime from '@/utils/date-time'
import logger from '@/utils/log'
import message from '@/utils/message'
import { activeTheme } from '@/utils/theme'
import tiptap from '@/utils/tiptap'

export function useArticleDetail() {
  const articleStore = useArticleStore()
  const route = useRoute()

  // 文章数据和加载状态
  const article = ref<Article>({} as Article)
  const articleLoading = ref(true)

  // 获取文章ID
  const getArticleId = () => {
    return articleStore.getId
  }

  // 加载文章详情
  const loadArticleDetail = () => {
    articleLoading.value = true
    const articleId = getArticleId()

    if (articleId) {
      articleApi
        .detail(articleId)
        .then((res: ResponseData) => {
          const detail = res?.data
          if (detail) {
            article.value = {
              ...detail,
              contentObj: tiptap.toJsonObject(detail.content),
              tags: detail.tag.split(',') as string[],
              isOwner: detail.isOwner === true,
              publishedAt: dateTime.getRelativeTime(detail.publishedAt),
              lastModified: dateTime.getRelativeTime(detail.lastModified),
              exactPublishedAt: dateTime.toTimeString(detail.publishedAt),
              exactLastModified: dateTime.toTimeString(detail.lastModified),
            }
            articleLoading.value = false
          }
          logger.debug('article detail: ', article.value)
        })
        .catch((error) => {
          articleLoading.value = false
          // 处理权限错误或不存在的文章
          if (error.response && error.response.status === 403) {
            message.error('哎呀，您没有权限查看这篇文章')
            router.push('/')
          } else {
            message.error('加载文章失败，请稍后重试')
          }
        })
    }
  }

  // 加载文章详情统计数据（评论数、点赞数等）
  const loadArticleDetailCount = () => {
    const articleId = getArticleId()
    if (articleId) {
      articleApi.detail(articleId).then((res: ResponseData) => {
        const detail: Article = res?.data
        if (detail) {
          article.value.likeCount = detail.likeCount
          article.value.dislikeCount = detail.dislikeCount
          article.value.favoriteCount = detail.favoriteCount
          article.value.commentCount = detail.commentCount
        }
      })
    }
  }

  // 返回首页
  const backHome = () => {
    router.push('/')
  }

  // 监听路由变化，重新加载文章
  const setupRouteWatch = () => {
    watch(route, (newRoute, oldRoute) => {
      // 只在articleId变化时重新加载文章内容
      if (newRoute.params.articleId !== oldRoute?.params?.articleId) {
        loadArticleDetail()
      }
    })
  }

  // 监听主题变化并优化文章编辑器刷新
  const setupThemeWatch = () => {
    watch(activeTheme, () => {
      // 等待DOM更新
      nextTick(() => {
        if (!articleLoading.value) {
          // 刷新文章内容编辑器
          const articleEditor = document.querySelector('.article-content .ProseMirror')
          if (articleEditor instanceof HTMLElement) {
            // 使用轻量级方式触发编辑器重绘
            articleEditor.classList.add('theme-priority')
            void articleEditor.offsetHeight
          }
        }
      })
    })
  }

  // 初始化
  const initialize = () => {
    onMounted(() => {
      loadArticleDetail()
    })
    setupRouteWatch()
    setupThemeWatch()
  }

  return {
    // 状态
    article,
    articleLoading,

    // 方法
    getArticleId,
    loadArticleDetail,
    loadArticleDetailCount,
    backHome,
    initialize,
  }
}
