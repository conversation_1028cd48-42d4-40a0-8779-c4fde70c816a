{
    "configurations": [
        {
            "type": "java",
            "name": "Spring Boot-WenCoreApp<wen-core>",
            "request": "launch",
            "cwd": "${workspaceFolder}",
            "mainClass": "com.shenmo.wen.app.core.WenCoreApp",
            "projectName": "wen-core",
            "args": "",
            "envFile": "${workspaceFolder}/.env",
            "vmArgs": " -Dcom.sun.management.jmxremote -Dfile.encoding=UTF-8 -Dspring.cloud.nacos.discovery.group=local",
        },
        {
            "type": "java",
            "name": "Spring Boot-WenAuthenticationApp<wen-authentication>",
            "request": "launch",
            "cwd": "${workspaceFolder}",
            "mainClass": "com.shenmo.wen.app.authentication.WenAuthenticationApp",
            "projectName": "wen-authentication",
            "args": "",
            "envFile": "${workspaceFolder}/.env",
            "vmArgs": " -Dcom.sun.management.jmxremote -Dfile.encoding=UTF-8 -Dspring.cloud.nacos.discovery.group=local -Dcloudflare.turnstile.enabled=false",
        },
        {
            "type": "java",
            "name": "Spring Boot-WenGatewayApp<wen-gateway>",
            "request": "launch",
            "cwd": "${workspaceFolder}",
            "mainClass": "com.shenmo.wen.app.gateway.WenGatewayApp",
            "projectName": "wen-gateway",
            "args": "",
            "envFile": "${workspaceFolder}/.env",
            "vmArgs": " -Dcom.sun.management.jmxremote -Dfile.encoding=UTF-8 -Dspring.cloud.nacos.discovery.group=local",
        }
    ]
}