import type { Editor } from '@tiptap/core'
import type { Node as ProseMirrorNode } from '@tiptap/pm/model'

export interface NodeTypeConfig {
  name: string
  selector: string
  canDragInto: string[]
  canReceiveFrom: string[]
  preserveAttributes: string[]
  nestingRules: {
    maxDepth?: number
    allowedParents?: string[]
    allowedChildren?: string[]
  }
}

export interface DragHandleOptions {
  enabled: boolean
  supportedNodes: readonly string[]
  nodeTypeConfigs: Readonly<Record<string, NodeTypeConfig>>
  dragHandleSelector: string
  ghostImageOpacity: number
  animationDuration: number
  keyboardShortcuts: Readonly<Record<string, string>>
  announceToScreenReader: boolean
  respectReducedMotion: boolean
  ariaLabels: Readonly<Record<string, string>>
  keyboardHelp: boolean
}

export interface DragOperation {
  sourceNode: ProseMirrorNode
  sourcePos: number
  targetPos: number | null
  dragElement: HTMLElement
  ghostImage: HTMLElement | null
  isActive: boolean
  nodeType: string
  preservedAttributes: Record<string, any>
  nestingContext: {
    sourceParent: ProseMirrorNode | null
    sourceDepth: number
    targetParent?: ProseMirrorNode | null
    targetDepth?: number
  }
}

export interface DropZone {
  element: HTMLElement
  position: number
  isValid: boolean
  visualIndicator: HTMLElement | null
  nodeType: string
  canAcceptNodeTypes: string[]
  nestingLevel: number
  validationReason?: string
}

export interface DragState {
  operation: DragOperation | null
  dropZones: DropZone[]
  visualFeedback: {
    ghostImage: HTMLElement | null
    dropIndicators: HTMLElement[]
    dragPreview: HTMLElement | null
  }
}

export interface NodePosition {
  node: ProseMirrorNode
  pos: number
  depth: number
  parent: ProseMirrorNode | null
  index: number
  element: HTMLElement
}

export interface NodeValidationResult {
  isValid: boolean
  reason?: string
  suggestedPosition?: number
  requiresTransformation?: boolean
}

export interface ContentPreservation {
  attributes: Record<string, any>
  marks: any[]
  content: any[]
  metadata: Record<string, any>
}

export interface NestingValidation {
  canNest: boolean
  maxDepthExceeded: boolean
  invalidParent: boolean
  invalidChild: boolean
  suggestedAlternative?: {
    position: number
    transformation?: string
  }
}

export interface DragHandleCommands {
  moveParagraphUp: () => boolean
  moveParagraphDown: () => boolean
  moveNodeUp: (nodeType?: string) => boolean
  moveNodeDown: (nodeType?: string) => boolean
  moveToTop: (nodeType?: string) => boolean
  moveToBottom: (nodeType?: string) => boolean
}

declare module '@tiptap/core' {
  interface Commands<ReturnType = any> {
    dragHandle: DragHandleCommands
  }
}