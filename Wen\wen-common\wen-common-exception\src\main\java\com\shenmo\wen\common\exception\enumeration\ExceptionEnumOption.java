package com.shenmo.wen.common.exception.enumeration;


import lombok.Data;
import org.springframework.http.HttpStatus;

import java.util.Objects;
import java.util.Optional;

import static com.shenmo.wen.common.exception.enumeration.ExceptionEnumOption.*;

/**
 * 异常枚举选项
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@ExceptionType(module = MODULE)
public class ExceptionEnumOption implements ExceptionEnum {

    public static final String MODULE = "000";



    /**
     * http 状态
     */
    private final HttpStatus httpStatus;

    /**
     * 异常信息
     */
    private final String message;

    /**
     * 异常编码
     */
    private Integer code;

    /**
     * 构造方法
     *
     * @param httpStatus http 状态
     * @param message    异常信息
     * <AUTHOR>
     */
    private ExceptionEnumOption(HttpStatus httpStatus, String message) {

        this(httpStatus, message, null);
    }

    /**
     * 构造方法
     *
     * @param httpStatus http 状态
     * @param message    异常信息
     * @param code       异常编码
     * <AUTHOR>
     */
    public ExceptionEnumOption(HttpStatus httpStatus, String message, Integer code) {

        this.httpStatus = Objects.requireNonNull(httpStatus, "httpStatus requireNonNull");
        this.message = Objects.requireNonNull(message, "message requireNonNull");
        this.code = code;
    }

    /**
     * 根据{@link HttpStatus}以及异常信息构建出异常枚举选项
     *
     * @param httpStatus {@link HttpStatus}
     * @param message    异常信息
     * @return 异常枚举选项
     * <AUTHOR>
     */
    public static ExceptionEnumOption of(HttpStatus httpStatus, String message) {

        return new ExceptionEnumOption(httpStatus, message);
    }

    public int getStatusValue() {

        return httpStatus.value();
    }

    @Override
    public ExceptionEnumOption getOption() {

        return this;
    }

    @Override
    public Integer getCode() {

        return Optional.ofNullable(this.code).orElse(ExceptionEnum.super.getCode());
    }
}
