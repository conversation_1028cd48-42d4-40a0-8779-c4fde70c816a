package com.shenmo.wen.app.authentication.exception;

import com.shenmo.wen.common.exception.BaseException;
import com.shenmo.wen.common.exception.enumeration.ExceptionEnum;
import org.springframework.http.HttpStatus;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class AuthenticationException extends BaseException {
    public AuthenticationException(ExceptionEnum exceptionEnum) {
        super(exceptionEnum);
    }

    public AuthenticationException(HttpStatus httpStatus, String message) {
        super(httpStatus, message);
    }

    public AuthenticationException(HttpStatus httpStatus, String description, Throwable throwable) {
        super(httpStatus, description, throwable);
    }

    public AuthenticationException(HttpStatus httpStatus, String description, String message) {
        super(httpStatus, description, message);
    }
}
