import type { Editor } from '@tiptap/core'
import type { Node as ProseMirrorNode } from '@tiptap/pm/model'
import type { ClickMenuItem } from '../types'

/**
 * Dynamic menu item generator for contextual menu items
 */
export class DynamicMenuItemGenerator {
  /**
   * Generate contextual menu items based on node type and editor state
   */
  static generateContextualItems(
    node: ProseMirrorNode,
    editor: Editor,
    pos: number
  ): ClickMenuItem[] {
    const contextualItems: ClickMenuItem[] = []

    // Add node-specific items based on type
    switch (node.type.name) {
      case 'paragraph':
        contextualItems.push(...this.generateParagraphItems(node, editor, pos))
        break
      case 'heading':
        contextualItems.push(...this.generateHeadingItems(node, editor, pos))
        break
      case 'listItem':
        contextualItems.push(...this.generateListItems(node, editor, pos))
        break
      case 'codeBlock':
        contextualItems.push(...this.generateCodeBlockItems(node, editor, pos))
        break
    }

    // Add universal items that work for all nodes
    contextualItems.push(...this.generateUniversalItems(node, editor, pos))

    return contextualItems
  }

  /**
   * Filter items by current context and availability
   */
  static filterItemsByContext(
    items: ClickMenuItem[],
    node: ProseMirrorNode,
    editor: Editor,
    pos: number
  ): ClickMenuItem[] {
    return items.filter(item => {
      try {
        return item.isVisible(node) && item.isEnabled(node)
      } catch (error) {
        console.warn(`Error checking item availability for ${item.id}:`, error)
        return false
      }
    })
  }

  /**
   * Sort items by priority and group
   */
  static sortItems(items: ClickMenuItem[]): ClickMenuItem[] {
    const groupPriority = {
      'edit': 1,
      'format': 2,
      'insert': 3,
      'transform': 4,
      'default': 99
    }

    return items.sort((a, b) => {
      const aGroup = a.group || 'default'
      const bGroup = b.group || 'default'
      const aPriority = groupPriority[aGroup as keyof typeof groupPriority] || 99
      const bPriority = groupPriority[bGroup as keyof typeof groupPriority] || 99

      if (aPriority !== bPriority) {
        return aPriority - bPriority
      }

      // Within same group, sort alphabetically
      return a.label.localeCompare(b.label)
    })
  }

  /**
   * Generate paragraph-specific menu items
   */
  private static generateParagraphItems(
    node: ProseMirrorNode,
    editor: Editor,
    pos: number
  ): ClickMenuItem[] {
    return [
      {
        id: 'convert-to-heading',
        label: '转换为标题',
        icon: '<svg>...</svg>',
        group: 'transform',
        action: (editor) => {
          editor.chain().focus().toggleHeading({ level: 1 }).run()
        },
        isVisible: () => true,
        isEnabled: () => editor.can().toggleHeading({ level: 1 })
      },
      {
        id: 'convert-to-list',
        label: '转换为列表',
        icon: '<svg>...</svg>',
        group: 'transform',
        action: (editor) => {
          editor.chain().focus().toggleBulletList().run()
        },
        isVisible: () => true,
        isEnabled: () => editor.can().toggleBulletList()
      }
    ]
  }

  /**
   * Generate heading-specific menu items
   */
  private static generateHeadingItems(
    node: ProseMirrorNode,
    editor: Editor,
    pos: number
  ): ClickMenuItem[] {
    const level = node.attrs.level || 1

    return [
      {
        id: 'increase-heading-level',
        label: '提升标题级别',
        icon: '<svg>...</svg>',
        group: 'format',
        action: (editor) => {
          const newLevel = Math.max(1, level - 1) as 1 | 2 | 3 | 4 | 5 | 6
          editor.chain().focus().toggleHeading({ level: newLevel }).run()
        },
        isVisible: () => level > 1,
        isEnabled: () => level > 1
      },
      {
        id: 'decrease-heading-level',
        label: '降低标题级别',
        icon: '<svg>...</svg>',
        group: 'format',
        action: (editor) => {
          const newLevel = Math.min(6, level + 1) as 1 | 2 | 3 | 4 | 5 | 6
          editor.chain().focus().toggleHeading({ level: newLevel }).run()
        },
        isVisible: () => level < 6,
        isEnabled: () => level < 6
      }
    ]
  }

  /**
   * Generate list-specific menu items
   */
  private static generateListItems(
    node: ProseMirrorNode,
    editor: Editor,
    pos: number
  ): ClickMenuItem[] {
    return [
      {
        id: 'indent-list-item',
        label: '增加缩进',
        icon: '<svg>...</svg>',
        group: 'format',
        action: (editor) => {
          editor.chain().focus().sinkListItem('listItem').run()
        },
        isVisible: () => true,
        isEnabled: () => editor.can().sinkListItem('listItem')
      },
      {
        id: 'outdent-list-item',
        label: '减少缩进',
        icon: '<svg>...</svg>',
        group: 'format',
        action: (editor) => {
          editor.chain().focus().liftListItem('listItem').run()
        },
        isVisible: () => true,
        isEnabled: () => editor.can().liftListItem('listItem')
      }
    ]
  }

  /**
   * Generate code block-specific menu items
   */
  private static generateCodeBlockItems(
    node: ProseMirrorNode,
    editor: Editor,
    pos: number
  ): ClickMenuItem[] {
    return [
      {
        id: 'change-language',
        label: '更改语言',
        icon: '<svg>...</svg>',
        group: 'format',
        action: (editor) => {
          // This would typically open a language selector
          console.log('Change code block language')
        },
        isVisible: () => true,
        isEnabled: () => true
      }
    ]
  }

  /**
   * Generate universal menu items that work for all node types
   */
  private static generateUniversalItems(
    node: ProseMirrorNode,
    editor: Editor,
    pos: number
  ): ClickMenuItem[] {
    return [
      {
        id: 'duplicate-node',
        label: '复制节点',
        icon: '<svg>...</svg>',
        group: 'edit',
        action: (editor, node, pos) => {
          const tr = editor.state.tr
          tr.insert(pos + node.nodeSize, node)
          editor.view.dispatch(tr)
        },
        isVisible: () => true,
        isEnabled: () => true
      },
      {
        id: 'delete-node',
        label: '删除节点',
        icon: '<svg>...</svg>',
        group: 'edit',
        action: (editor, node, pos) => {
          const tr = editor.state.tr
          tr.delete(pos, pos + node.nodeSize)
          editor.view.dispatch(tr)
        },
        isVisible: () => true,
        isEnabled: () => true
      }
    ]
  }
}