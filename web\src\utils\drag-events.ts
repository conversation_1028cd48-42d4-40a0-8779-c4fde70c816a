// 拖拽事件处理工具函数

interface DragEventHandlers {
  onMove: (event: MouseEvent | TouchEvent) => void
  onEnd: () => void
}

export function setupDragEvents(handlers: DragEventHandlers): () => void {
  const { onMove, onEnd } = handlers
  
  // 创建事件处理器
  const moveHandler = (event: MouseEvent | TouchEvent) => {
    onMove(event)
  }
  
  const endHandler = () => {
    onEnd()
    cleanup()
  }
  
  // 添加事件监听器
  document.addEventListener('mousemove', moveHandler, { passive: false })
  document.addEventListener('mouseup', endHandler)
  document.addEventListener('touchmove', moveHandler, { passive: false })
  document.addEventListener('touchend', endHandler)
  document.addEventListener('touchcancel', endHandler)
  
  console.log('事件监听器已添加')
  
  // 返回清理函数
  const cleanup = () => {
    document.removeEventListener('mousemove', moveHandler)
    document.removeEventListener('mouseup', endHandler)
    document.removeEventListener('touchmove', moveHandler)
    document.removeEventListener('touchend', endHandler)
    document.removeEventListener('touchcancel', endHandler)
    console.log('事件监听器已清理')
  }
  
  return cleanup
}

export function cleanupDragEvents(): void {
  // 这个函数保留用于向后兼容，实际清理由setupDragEvents返回的函数处理
  console.log('拖拽事件清理完成')
} 