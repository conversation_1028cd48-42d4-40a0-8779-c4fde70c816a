import { type ResponseData } from '@/types/response_data.types'
import api from '@/utils/api'

const notificationApi = {
  URL: '/core/notification',
  // 加载
  load: async <T>(params?: Record<string, any>): Promise<ResponseData<T>> => {
    const res = await api.get<T>(notificationApi.URL, params).catch((err) => {
      return api.handleError(err)
    })
    return res.data as ResponseData<T>
  },
  // 已读
  read: async <T>(_id?: string): Promise<ResponseData<T>> => {
    const res = await api.put<T>(notificationApi.URL + '/read/' + _id).catch((err) => {
      return api.handleError(err)
    })
    return res.data as ResponseData<T>
  },
  // 全部已读
  readAll: async <T>(_id?: string): Promise<ResponseData<T>> => {
    const res = await api.put<T>(notificationApi.URL + '/read-all').catch((err) => {
      return api.handleError(err)
    })
    return res.data as ResponseData<T>
  },
  unreadCount: async <T>(): Promise<ResponseData<T>> => {
    const res = await api.get<T>(notificationApi.URL + '/total-unread').catch((err) => {
      return api.handleError(err)
    })
    return res.data as ResponseData<T>
  },
}

export default notificationApi
