import TaskItem from '@tiptap/extension-task-item'

// 扩展TaskItem，添加自定义的nodeView处理
const taskItem = TaskItem.extend({
  addNodeView() {
    return (props) => {
      // 创建基本的DOM结构
      const dom = document.createElement('li')
      dom.setAttribute('data-type', 'taskItem')

      // 设置checked属性
      dom.dataset.checked = props.node.attrs.checked

      // 创建标签元素包装复选框
      const label = document.createElement('label')
      label.className = 'cst-task-label'
      label.style.display = 'flex' // 使用flex布局
      label.style.alignItems = 'flex-start' // 顶部对齐
      label.style.marginTop = '0.2rem' // 添加顶部边距，微调位置

      // 创建复选框
      const checkbox = document.createElement('input')
      checkbox.type = 'checkbox'
      checkbox.checked = props.node.attrs.checked
      checkbox.style.display = 'none' // 隐藏原始复选框
      checkbox.className = 'cst-task-checkbox'

      // 创建自定义复选框
      const cstCheckbox = document.createElement('span')
      cstCheckbox.className = 'cst-task-checkbox-wrapper'

      // 设置固定样式，避免选中/未选中状态切换时的抖动
      cstCheckbox.style.display = 'inline-flex'
      cstCheckbox.style.justifyContent = 'center'
      cstCheckbox.style.alignItems = 'center'
      cstCheckbox.style.width = '1rem'
      cstCheckbox.style.height = '1rem'
      cstCheckbox.style.border = '1px solid #ccc'
      cstCheckbox.style.borderRadius = '0.25rem'
      cstCheckbox.style.marginRight = '0.5rem'
      cstCheckbox.style.position = 'relative'
      cstCheckbox.style.cursor = props.editor.isEditable ? 'pointer' : 'default'
      cstCheckbox.style.transition = props.editor.isEditable ? 'background-color 0.2s ease' : 'none'
      cstCheckbox.style.flexShrink = '0' // 防止复选框被压缩
      cstCheckbox.style.marginTop = '0.2rem' // 微调垂直位置

      // 为非编辑状态添加禁用样式
      if (!props.editor.isEditable) {
        cstCheckbox.style.cursor = 'not-allowed'
        cstCheckbox.setAttribute('aria-disabled', 'true')
        cstCheckbox.title = '只读模式下不可更改'
        // 禁用动画效果
        label.style.transition = 'none'
        label.style.transform = 'none'
      }

      // 创建勾选标记元素，保持固定位置
      const checkmark = document.createElement('span')
      checkmark.className = 'cst-task-checkmark'
      checkmark.textContent = '✓'
      checkmark.style.color = 'white'
      checkmark.style.fontSize = '0.75rem'
      checkmark.style.opacity = checkbox.checked ? '1' : '0'
      checkmark.style.transition = props.editor.isEditable ? 'opacity 0.2s ease' : 'none'

      // 更新复选框状态
      updateCstCheckbox()

      // 将勾选标记添加到自定义复选框中
      cstCheckbox.appendChild(checkmark)

      // 添加点击事件
      cstCheckbox.addEventListener('mousedown', (e) => {
        e.preventDefault()
        e.stopPropagation()

        // 检查编辑器是否可编辑
        if (!props.editor.isEditable) {
          // 如果配置了onReadOnlyChecked选项，则调用它
          if (this.options.onReadOnlyChecked) {
            const checked = !checkbox.checked
            // 调用onReadOnlyChecked并检查返回值
            const shouldUpdate = this.options.onReadOnlyChecked(props.node, checked)

            // 如果返回false，则不更新勾选状态
            if (shouldUpdate === false) {
              return
            }

            // 更新状态
            checkbox.checked = checked
            updateCstCheckbox()

            // 更新节点属性
            if (typeof props.getPos === 'function') {
              props.editor.commands.command(({ tr }) => {
                tr.setNodeMarkup(props.getPos(), undefined, {
                  ...props.node.attrs,
                  checked,
                })
                return true
              })
            }

            // 更新data-checked属性
            dom.dataset.checked = checked.toString()
          }
          return
        }

        // 编辑器可编辑状态下的处理
        checkbox.checked = !checkbox.checked
        updateCstCheckbox()

        if (typeof props.getPos === 'function') {
          props.editor.commands.command(({ tr }) => {
            tr.setNodeMarkup(props.getPos(), undefined, {
              ...props.node.attrs,
              checked: checkbox.checked,
            })
            return true
          })
        }

        // 更新data-checked属性
        dom.dataset.checked = checkbox.checked.toString()
      })

      // 更新自定义复选框的函数
      function updateCstCheckbox() {
        cstCheckbox.style.backgroundColor = checkbox.checked ? 'var(--purple-contrast)' : 'white'
        checkmark.style.opacity = checkbox.checked ? '1' : '0'

        // 为非编辑状态的选中复选框添加禁用样式
        if (!props.editor.isEditable && checkbox.checked) {
          cstCheckbox.style.backgroundColor = 'var(--purple-contrast-disabled, #a095c3)'
        }
      }

      // 创建内容容器
      const contentDOM = document.createElement('div')
      contentDOM.className = 'cst-task-content'
      contentDOM.style.flex = '1' // 让内容区域占据剩余空间

      // 按照原始TaskItem的DOM结构组装
      label.appendChild(checkbox)
      label.appendChild(cstCheckbox)
      dom.appendChild(label)
      dom.appendChild(contentDOM)

      return {
        dom,
        contentDOM,
      }
    }
  },
})

// 导出自定义的TaskItem
export default taskItem
