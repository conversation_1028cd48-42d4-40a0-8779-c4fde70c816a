package com.shenmo.wen.app.core.favorite.mapper;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.shenmo.wen.app.core.favorite.pojo.entity.WenFavorite;
import com.shenmo.wen.common.enumeration.InteractionTargetEnum;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface WenFavoriteMapper extends BaseMapper<WenFavorite> {
    default List<Long> listArticleIdByUserId(Long userId) {
        return listTargetIdByUserAndTarget(userId, InteractionTargetEnum.ARTICLE.getCode());
    }

    default List<Long> listCommentIdByUserId(Long userId) {
        return listTargetIdByUserAndTarget(userId, InteractionTargetEnum.COMMENT.getCode());
    }

    default List<Long> listTargetIdByUserAndTarget(Long userId, Integer targetType) {
        return selectList(new LambdaQueryWrapper<WenFavorite>()
                .select(WenFavorite::getTargetId)
                .eq(WenFavorite::getUserId, userId)
                .eq(WenFavorite::getTargetType, targetType))
                .stream().map(WenFavorite::getTargetId).toList();
    }

    default WenFavorite targetById(long userId, int targetType, Long targetId) {
        return selectOne(Wrappers.<WenFavorite>lambdaQuery().
                eq(WenFavorite::getUserId, userId)
                .eq(WenFavorite::getTargetType, targetType)
                .eq(WenFavorite::getTargetId, targetId));
    }
    
    /**
     * 查询收藏了特定目标的用户ID列表
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return 用户ID列表
     */
    default List<Long> listUserIdByTargetId(Integer targetType, Long targetId) {
        return selectList(Wrappers.<WenFavorite>lambdaQuery()
                .select(WenFavorite::getUserId)
                .eq(WenFavorite::getTargetType, targetType)
                .eq(WenFavorite::getTargetId, targetId))
                .stream().map(WenFavorite::getUserId).toList();
    }

    /**
     * 删除目标的所有收藏记录
     * 
     * @param targetType 目标类型
     * @param targetId 目标ID
     */
    default void deleteByTargetId(Integer targetType, Long targetId) {
        delete(Wrappers.<WenFavorite>lambdaQuery()
                .eq(WenFavorite::getTargetType, targetType)
                .eq(WenFavorite::getTargetId, targetId));
    }
}
