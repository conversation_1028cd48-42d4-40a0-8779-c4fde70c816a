import { type Ref } from 'vue'

import favoriteApi from '@/api/favorite'
import interactionApi from '@/api/interaction'
import { type Article } from '@/types/article.types'
import type { ResponseData } from '@/types/response_data.types'
import message from '@/utils/message'

export function useArticleInteraction(article: Ref<Article>) {
  // 处理互动按钮（点赞/踩）
  const interactionBtn = (articleId: string, actionType: number) => {
    const reqParam = {
      targetType: 1,
      targetId: articleId,
      actionType: actionType,
    }

    interactionApi.save(reqParam).then((res: ResponseData) => {
      const data = res?.data
      if (data) {
        article.value.likeCount = data.likeCount
        article.value.dislikeCount = data.dislikeCount
        const like = actionType === 1

        // 取消互动
        if (data.cancel) {
          if (like) {
            message.info('赞取消')
            article.value.isLike = false
          } else {
            message.info('踩取消')
            article.value.isDislike = false
          }
        } else {
          if (like) {
            message.success('赞 :)')
            article.value.isLike = true
          } else {
            message.warning('踩 :(')
            article.value.isDislike = true
          }
        }
      }
    })
  }

  // 处理收藏按钮
  const favoriteBtn = (articleId: string) => {
    const reqParam = {
      targetType: 1,
      targetId: articleId,
    }

    favoriteApi.save(reqParam).then((res: ResponseData) => {
      const data = res?.data
      if (data) {
        article.value.favoriteCount = data.count
        if (data.cancel) {
          message.info('取消收藏')
          article.value.isFavorite = false
        } else {
          message.success('已收藏')
          article.value.isFavorite = true
        }
      }
    })
  }

  return {
    interactionBtn,
    favoriteBtn,
  }
}
