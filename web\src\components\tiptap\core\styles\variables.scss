/**
 * Tiptap 编辑器样式变量
 * 统一管理编辑器相关的颜色、尺寸、动画等样式变量
 */

// 颜色变量
:root {
  // 主色调
  --tiptap-primary-color: #2d8cf0;
  --tiptap-primary-hover: #57a3f3;
  --tiptap-primary-active: #2b85e4;

  // 背景色
  --tiptap-bg-primary: #fff;
  --tiptap-bg-secondary: #f8f9fa;
  --tiptap-bg-tertiary: #e9ecef;
  --tiptap-bg-code: var(--creamy-white-1, #eeece4);
  --tiptap-bg-code-dark: #2d3748;

  // 文字颜色
  --tiptap-text-primary: #1a202c;
  --tiptap-text-secondary: #4a5568;
  --tiptap-text-muted: #718096;
  --tiptap-text-inverse: #fff;

  // 边框颜色
  --tiptap-border-light: #e2e8f0;
  --tiptap-border-medium: #cbd5e0;
  --tiptap-border-dark: #a0aec0;
  --tiptap-border-code: var(--creamy-white-3, #dcd8ca);

  // 状态颜色
  --tiptap-success: #48bb78;
  --tiptap-warning: #ed8936;
  --tiptap-error: #f56565;
  --tiptap-info: #4299e1;

  // 阴影
  --tiptap-shadow-sm: 0 1px 3px rgba(0, 0, 0, 10%);
  --tiptap-shadow-md: 0 4px 6px rgba(0, 0, 0, 10%);
  --tiptap-shadow-lg: 0 10px 15px rgba(0, 0, 0, 10%);
  --tiptap-shadow-code: 0 2px 6px rgba(0, 0, 0, 5%);

  // 圆角
  --tiptap-radius-sm: 2px;
  --tiptap-radius-md: 4px;
  --tiptap-radius-lg: 6px;
  --tiptap-radius-xl: 8px;

  // 间距
  --tiptap-spacing-xs: 0.25rem;
  --tiptap-spacing-sm: 0.5rem;
  --tiptap-spacing-md: 0.75rem;
  --tiptap-spacing-lg: 1rem;
  --tiptap-spacing-xl: 1.5rem;
  --tiptap-spacing-2xl: 2rem;

  // 字体
  --tiptap-font-family: -apple-system, blinkmacsystemfont, 'Segoe UI', roboto, sans-serif;
  --tiptap-font-family-mono: consolas, 'Source Code Pro', 'Courier New', monospace;
  --tiptap-font-size-xs: 0.75rem;
  --tiptap-font-size-sm: 0.875rem;
  --tiptap-font-size-md: 1rem;
  --tiptap-font-size-lg: 1.125rem;
  --tiptap-font-size-xl: 1.25rem;

  // 行高
  --tiptap-line-height-tight: 1.25;
  --tiptap-line-height-normal: 1.5;
  --tiptap-line-height-relaxed: 1.75;

  // 过渡动画
  --tiptap-transition-fast: 0.15s ease;
  --tiptap-transition-normal: 0.2s ease;
  --tiptap-transition-slow: 0.3s ease;

  // Z-index 层级
  --tiptap-z-dropdown: 1000;
  --tiptap-z-sticky: 1020;
  --tiptap-z-fixed: 1030;
  --tiptap-z-modal-backdrop: 1040;
  --tiptap-z-modal: 1050;
  --tiptap-z-popover: 1060;
  --tiptap-z-tooltip: 1070;

  // 图片相关
  --tiptap-image-border-selected: 2px solid var(--tiptap-primary-color);
  --tiptap-image-shadow-selected: 0 0 0 3px rgba(45, 140, 240, 20%);
  --tiptap-image-handle-size: 8px;
  --tiptap-image-handle-color: var(--tiptap-primary-color);

  // 代码块相关
  --tiptap-code-bg: var(--tiptap-bg-code);
  --tiptap-code-border: var(--tiptap-border-code);
  --tiptap-code-font-size: 0.9rem;
  --tiptap-code-line-height: 1.5;
  --tiptap-code-padding: 0.8rem 1rem;

  // 工具栏相关
  --tiptap-toolbar-bg: var(--tiptap-bg-primary);
  --tiptap-toolbar-border: var(--tiptap-border-light);
  --tiptap-toolbar-button-size: 32px;
  --tiptap-toolbar-button-radius: var(--tiptap-radius-md);

  // 菜单相关
  --tiptap-menu-bg: var(--tiptap-bg-primary);
  --tiptap-menu-border: var(--tiptap-border-light);
  --tiptap-menu-shadow: var(--tiptap-shadow-lg);
  --tiptap-menu-radius: var(--tiptap-radius-lg);

  // 滚动条相关
  --tiptap-scrollbar-size: 8px;
  --tiptap-scrollbar-track: transparent;
  --tiptap-scrollbar-thumb: var(--gray-4, rgba(53, 38, 28, 40%));
  --tiptap-scrollbar-thumb-hover: var(--gray-5, rgba(28, 25, 23, 60%));
}

// 暗色主题变量
[data-theme='dark'] {
  --tiptap-bg-primary: #1a202c;
  --tiptap-bg-secondary: #2d3748;
  --tiptap-bg-tertiary: #4a5568;
  --tiptap-bg-code: #2d3748;
  --tiptap-text-primary: #f7fafc;
  --tiptap-text-secondary: #e2e8f0;
  --tiptap-text-muted: #a0aec0;
  --tiptap-border-light: #4a5568;
  --tiptap-border-medium: #718096;
  --tiptap-border-dark: #a0aec0;
  --tiptap-toolbar-bg: #2d3748;
  --tiptap-menu-bg: #2d3748;
}

// 响应式断点
$tiptap-breakpoint-sm: 640px;
$tiptap-breakpoint-md: 768px;
$tiptap-breakpoint-lg: 1024px;
$tiptap-breakpoint-xl: 1280px;

// Mixins
@mixin tiptap-button-base {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--tiptap-spacing-xs) var(--tiptap-spacing-sm);
  border: 1px solid var(--tiptap-border-light);
  border-radius: var(--tiptap-toolbar-button-radius);
  background: var(--tiptap-bg-primary);
  color: var(--tiptap-text-primary);
  font-size: var(--tiptap-font-size-sm);
  cursor: pointer;
  transition: all var(--tiptap-transition-fast);

  &:hover {
    background: var(--tiptap-bg-secondary);
    border-color: var(--tiptap-border-medium);
  }

  &:active,
  &.active {
    background: var(--tiptap-primary-color);
    border-color: var(--tiptap-primary-color);
    color: var(--tiptap-text-inverse);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

@mixin tiptap-scrollbar {
  &::-webkit-scrollbar {
    width: var(--tiptap-scrollbar-size);
    height: var(--tiptap-scrollbar-size);
  }

  &::-webkit-scrollbar-track {
    background: var(--tiptap-scrollbar-track);
  }

  &::-webkit-scrollbar-thumb {
    background: var(--tiptap-scrollbar-thumb);
    border-radius: var(--tiptap-radius-md);

    &:hover {
      background: var(--tiptap-scrollbar-thumb-hover);
    }
  }
}

@mixin tiptap-focus-ring {
  outline: 2px solid var(--tiptap-primary-color);
  outline-offset: 2px;
}

@mixin tiptap-card {
  background: var(--tiptap-bg-primary);
  border: 1px solid var(--tiptap-border-light);
  border-radius: var(--tiptap-radius-lg);
  box-shadow: var(--tiptap-shadow-md);
}

@mixin tiptap-text-truncate {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}
