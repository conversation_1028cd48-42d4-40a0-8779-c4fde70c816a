package com.shenmo.wen.app.core.file.controller;

import com.shenmo.wen.app.core.file.controller.service.WenFileService;
import com.shenmo.wen.common.pojo.response.ResponseData;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/files")
@RequiredArgsConstructor
public class WenFileController {
    private final WenFileService service;


    @PostMapping
    public ResponseData<String> upload(@RequestParam("bucket") String bucket, @RequestParam("file") MultipartFile file) throws IOException {
        return ResponseData.success(service.save(bucket, file));
    }
}
