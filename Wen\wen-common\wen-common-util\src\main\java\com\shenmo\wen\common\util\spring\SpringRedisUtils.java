package com.shenmo.wen.common.util.spring;

import com.shenmo.wen.common.exception.BaseException;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.core.ListOperations;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.SetOperations;
import org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer;
import org.springframework.data.redis.serializer.StringRedisSerializer;
import org.springframework.http.HttpStatus;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
public class SpringRedisUtils {

    /**
     * spring data redis操作模板类
     */
    private static volatile SpringRedisUtils INSTANCE;
    private final RedisTemplate<String, Object> redisTemplate;

    public SpringRedisUtils(RedisTemplate<String, Object> redisTemplate) {
        this.redisTemplate = redisTemplate;
    }

    public static SpringRedisUtils getInstance() {
        if (INSTANCE == null) {
            synchronized (SpringRedisUtils.class) {
                if (INSTANCE == null) {
                    RedisConnectionFactory factory = SpringIocUtils.mustGetBean(RedisConnectionFactory.class);
                    INSTANCE = new SpringRedisUtils(createRedisTemplate(factory));
                }
            }
        }
        return INSTANCE;
    }

    public static RedisTemplate<String, Object> getRedisTemplate() {
        return getInstance().redisTemplate;
    }

    public static <K, V> RedisTemplate<K, V> createRedisTemplate(RedisConnectionFactory factory) {
        final RedisTemplate<K, V> redisTemplate = new RedisTemplate<>();
        final GenericJackson2JsonRedisSerializer genericJackson2JsonRedisSerializer = new GenericJackson2JsonRedisSerializer();
        redisTemplate.setDefaultSerializer(genericJackson2JsonRedisSerializer);
        redisTemplate.setValueSerializer(genericJackson2JsonRedisSerializer);
        redisTemplate.setHashValueSerializer(genericJackson2JsonRedisSerializer);
        redisTemplate.setKeySerializer(new StringRedisSerializer());
        redisTemplate.setHashKeySerializer(new StringRedisSerializer());
        redisTemplate.setConnectionFactory(factory);
        redisTemplate.afterPropertiesSet();
        return redisTemplate;
    }

    public static Boolean hasKey(String key) {
        return getRedisTemplate().hasKey(key);
    }

    public static void set(String key, Object value, long timeout, TimeUnit unit) {
        getRedisTemplate().opsForValue().set(key, value, timeout, unit);
    }

    public static Object get(String key) {
        return getRedisTemplate().opsForValue().get(key);
    }

    public static List<Object> multiGet(Collection<String> keys) {
        return getRedisTemplate().opsForValue().multiGet(keys);
    }

    public static Boolean delete(String key) {
        return getRedisTemplate().delete(key);
    }

    public static void set(String key, Object value) {
        getRedisTemplate().opsForValue().set(key, value);
    }

    public static Long getExpire(String key) {
        return getRedisTemplate().getExpire(key);
    }

    public static Long getExpire(String key, TimeUnit unit) {
        return getRedisTemplate().getExpire(key, unit);
    }

    public static Long delete(Collection<String> keys) {
        return getRedisTemplate().delete(keys);
    }

    public static Set<String> keys(String pattern) {
        return getRedisTemplate().keys(pattern);
    }

    public static Long incr(String key, long delta) {
        if (delta < 0) {
            throw new BaseException(HttpStatus.INTERNAL_SERVER_ERROR, "递增因子必须大于0");
        }
        return getRedisTemplate().opsForValue().increment(key, delta);
    }

    public static Long incr(String key, long delta, long second) {
        if (delta < 0) {
            throw new BaseException(HttpStatus.INTERNAL_SERVER_ERROR, "递增因子必须大于0");
        }
        Long increment = getRedisTemplate().opsForValue().increment(key, delta);
        // 设置过期时间
        getRedisTemplate().expire(key, second, TimeUnit.SECONDS);
        return increment;
    }

    public static Long decr(String key, long delta) {
        if (delta < 0) {
            throw new BaseException(HttpStatus.INTERNAL_SERVER_ERROR, "递减因子必须大于0");
        }
        return getRedisTemplate().opsForValue().decrement(key, delta);
    }

    public static Long decr(String key, long delta, long second) {
        if (delta < 0) {
            throw new BaseException(HttpStatus.INTERNAL_SERVER_ERROR, "递减因子必须大于0");
        }
        getRedisTemplate().expire(key, second, TimeUnit.SECONDS);
        return getRedisTemplate().opsForValue().decrement(key, delta);
    }

    public static Long hashLength(String key) {
        return getRedisTemplate().opsForHash().size(key);
    }


    public static ListOperations<String, Object> forList() {
        return getRedisTemplate().opsForList();
    }

    public static SetOperations<String, Object> forSet() {
        return getRedisTemplate().opsForSet();
    }

    public static Boolean hashHasKey(String key, String hashKey) {
        return getRedisTemplate().opsForHash().hasKey(key, hashKey);
    }

    public static Map<Object, Object> hashEntries(String key) {
        return getRedisTemplate().opsForHash().entries(key);
    }

    public static Object hashGet(String key, String hashKey) {
        return getRedisTemplate().opsForHash().get(key, hashKey);
    }

    public static void hashPut(String key, String hashKey, Object hashValue) {
        getRedisTemplate().opsForHash().put(key, hashKey, hashValue);
    }

    public static void hashPut(String key, int minutes, String hashKey, Object hashValue) {
        getRedisTemplate().opsForHash().put(key, hashKey, hashValue);
        getRedisTemplate().expire(key, minutes, TimeUnit.MINUTES);
    }

    public static Set<Object> hashKeys(String key) {
        return getRedisTemplate().opsForHash().keys(key);
    }

    public static List<Object> hashValues(String key) {
        return getRedisTemplate().opsForHash().values(key);
    }

    public static Object execute(RedisCallback<Object> redisCallback) {
        return getRedisTemplate().execute(redisCallback);
    }
}
