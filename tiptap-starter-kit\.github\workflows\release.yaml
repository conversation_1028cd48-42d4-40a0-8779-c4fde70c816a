name: Release
on:
  workflow_dispatch:
  push:
    branches:
      - master
jobs:
  publish-npm:
    name: Publish packages
    runs-on: ubuntu-latest
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setup PNPM
        uses: pnpm/action-setup@v4.0.0
        with:
          version: 9.1.4
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          cache: pnpm
          node-version: 20
          registry-url: https://registry.npmjs.org
      - name: Install dependencies
        run: pnpm install --frozen-lockfile
      - name: Build
        run: pnpm build
      - name: Publish
        run: pnpm dlx semantic-release --branches master
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.NPM_TOKEN }}
