package com.shenmo.wen.common.objectstorage.response;

import com.shenmo.wen.common.constant.StringConstant;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.io.InputStream;

/**
 * 获取对象响应
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public abstract class GetObjectResponse<O> extends GenericResponse<O> {

    /**
     * 构造方法
     *
     * @param origin 源对象
     * <AUTHOR>
     */
    public GetObjectResponse(@NonNull O origin) {
        super(origin);
    }

    /**
     * 获取对象流
     *
     * @return 响应流
     * <AUTHOR>
     */
    @Nullable
    public abstract InputStream getInputStream();

    /**
     * 获取对象名
     *
     * @return 响应流
     * <AUTHOR>
     */
    public abstract String getName();

    /**
     * 判断当前对象是否是对象前缀
     *
     * @return 当前对象是否是对象前缀
     * <AUTHOR>
     */
    public boolean isPrefix() {
        return getName().endsWith(StringConstant.SLASH);
    }
}
