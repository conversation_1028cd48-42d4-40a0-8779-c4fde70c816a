package com.shenmo.wen.app.core.notification.pojo.entity;

import org.apache.ibatis.type.JdbcType;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.shenmo.wen.common.mybatis.handler.TimestampToLongTypeHandler;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@TableName(value = "wen_notification", autoResultMap = true)
public class WenNotification {
    /**
     * 通知ID，是每条通知的唯一标识符，在数据库中作为主键存在，不能为空。
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 发布的用户ID
     */
    private Long userId;

    /**
     * 发布的文章ID
     */
    private Long articleId;

    /**
     * 发布的评论ID
     */
    private Long commentId;

    /**
     * 评论通知人ID
     */
    private String commentNotificationUserId;

    /**
     * 通知内容，以文本形式详细描述具体发生的操作等相关情况，可为空（根据不同通知场景有的可能无具体内容描述）。
     */
    private String content;

    /**
     * 通知类型：0-系统，1-发布文章，2-修改文章，3-发布评论
     */
    private Integer type = 0;

    /**
     * 全局已读标志，表示是否所有相关用户都已读该通知，0-未全部已读，1-全部已读。
     */
    private Integer isRead = 0;

    /**
     * 通知时间，记录通知生成的时间戳，默认取当前时间，在创建通知记录时自动赋值。
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long ctTm;
}
