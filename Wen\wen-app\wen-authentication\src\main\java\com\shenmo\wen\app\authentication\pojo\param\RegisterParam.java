package com.shenmo.wen.app.authentication.pojo.param;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Pattern;
import lombok.Data;
import org.hibernate.validator.constraints.Length;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
public class RegisterParam {

    @NotBlank(message = "用户名不能为空")
    @Length(min = 3, max = 10, message = "用户名长度在 3 到 10 个字符之间")
    private String username;

    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "手机号格式错误")
    private String phone;

    @NotBlank(message = "密码不能为空")
    @Length(min = 6, message = "密码长度至少为 6 个字符")
    private String password;

    @NotBlank(message = "职业不能为空")
    @Length(min = 2, max = 8, message = "职业名长度在 2 到 8 个字符之间")
    private String job;

    @NotBlank(message = "验证码不能为空")
    private String cftt;
}
