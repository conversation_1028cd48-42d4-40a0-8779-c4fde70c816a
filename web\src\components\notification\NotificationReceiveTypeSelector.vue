<template>
  <NPopselect
    class="notification-popselect"
    :value="modelValue"
    :options="notificationReceiveOptions"
    @update:value="handleUpdate"
    trigger="click"
  >
    <NButton text size="small">
      接收类型：
      {{ notificationReceiveOptions.find((item) => item.value === modelValue)?.label }}
    </NButton>
  </NPopselect>
</template>

<script setup lang="ts">
import { NPopselect, NButton } from 'naive-ui'

import {
  NotificationReceiveType,
  NOTIFICATION_RECEIVE_TYPE_LABEL,
} from '@/constants/notification_receive_type.constants'

defineProps({
  modelValue: {
    type: Number,
    required: true,
  },
})

const emit = defineEmits(['update:modelValue'])

// 通知接收类型选项
const notificationReceiveOptions = [
  {
    label: NOTIFICATION_RECEIVE_TYPE_LABEL[NotificationReceiveType.ALL],
    value: NotificationReceiveType.ALL,
  },
  {
    label: NOTIFICATION_RECEIVE_TYPE_LABEL[NotificationReceiveType.PUBLISH],
    value: NotificationReceiveType.PUBLISH,
  },
  {
    label: NOTIFICATION_RECEIVE_TYPE_LABEL[NotificationReceiveType.MODIFY],
    value: NotificationReceiveType.MODIFY,
  },
  {
    label: NOTIFICATION_RECEIVE_TYPE_LABEL[NotificationReceiveType.FAVORITE],
    value: NotificationReceiveType.FAVORITE,
  },
  {
    label: NOTIFICATION_RECEIVE_TYPE_LABEL[NotificationReceiveType.SHARE],
    value: NotificationReceiveType.SHARE,
  },
  {
    label: NOTIFICATION_RECEIVE_TYPE_LABEL[NotificationReceiveType.CLOSE],
    value: NotificationReceiveType.CLOSE,
  },
]

// 处理通知类型更新
const handleUpdate = (value: number) => {
  emit('update:modelValue', value)
}
</script>

<style scoped>
.notification-popselect {
  margin-left: 8px;
}
</style>
