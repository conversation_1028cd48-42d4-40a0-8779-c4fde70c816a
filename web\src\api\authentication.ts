import type { RequestParams } from '@/types/request.types'
import { type ResponseData } from '@/types/response_data.types'
import api from '@/utils/api'

import type { AxiosError } from 'axios'

const authenticationApi = {
  URL: '/authentication',
  // 登录
  login: async <T>(params: RequestParams): Promise<ResponseData<T>> => {
    const res = await api
      .post<T>(authenticationApi.URL + '/login', params)
      .catch((err: AxiosError) => {
        return api.handleError(err)
      })
    return res.data as ResponseData<T>
  },

  // 注册
  register: async <T>(params: RequestParams): Promise<ResponseData<T>> => {
    const res = await api
      .post<T>(authenticationApi.URL + '/register', params)
      .catch((err: AxiosError) => {
        return api.handleError(err)
      })
    return res.data as ResponseData<T>
  },

  // 登出
  logout: async <T>(): Promise<ResponseData<T>> => {
    const res = await api.del<T>(authenticationApi.URL + '/logout').catch((err: AxiosError) => {
      return api.handleError(err)
    })
    return res.data as ResponseData<T>
  },
}

export default authenticationApi
