@import (inline) "plyr/dist/plyr.css";
@import (inline) "katex/dist/katex.css";
@import (inline) "tippy.js/dist/tippy.css";
@import (inline) "tippy.js/animations/shift-away.css";

:root {
  // font
  --tiptap-font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Open Sans",
  "Helvetica Neue", sans-serif;
  --tiptap-font-family-mono: "Fira Code", "JetBrains Mono", ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas,
  "Liberation Mono", "Courier New", monospace;
  --tiptap-font-weight: 400;
  --tiptap-font-size: 1rem;
  --tiptap-line-height: 1.7;
  // size
  --tiptap-radius: calc(var(--radius, 0.5rem) - 2px);
  // color
  --tiptap-ring: hsl(var(--ring, 240 5% 64.9%));
  --tiptap-input: hsl(var(--input, 240 5.9% 90%));
  --tiptap-border: hsl(var(--border, 240 5.9% 90%));
  --tiptap-selected: hsla(207, 100%, 50%, 0.05);
  --tiptap-foreground: hsl(var(--foreground, 240 10% 3.9%));
  --tiptap-background: hsl(var(--background, 0 0% 100%));
  --tiptap-primary-foreground: hsl(var(--primary-foreground, 210 40% 98%));
  --tiptap-primary-background: hsl(var(--primary, 221.2 83.2% 53.3%));
  --tiptap-muted-foreground: hsl(var(--muted-foreground, 240 3.8% 46.1%));
  --tiptap-muted-background: hsl(var(--muted, 240 4.8% 95.9%));
  --tiptap-accent-foreground: hsl(var(--accent-foreground, 240 5.9% 10%));
  --tiptap-accent-background: hsl(var(--accent, 240 4.8% 95.9%));
  --tiptap-card-foreground: hsl(var(--accent-foreground, 240 5.9% 10%));
  --tiptap-card-background: hsl(var(--accent, 240 4.8% 95.9%) / 20%);
  --tiptap-popover-foreground: hsl(var(--popover-foreground, 240 10% 3.9%));
  --tiptap-popover-background: hsl(var(--popover, 0 0% 100%));
  --tiptap-info-foreground: hsl(220, 6%, 40%);
  --tiptap-info-background: hsl(240, 11%, 95%);
  --tiptap-warn-foreground: hsl(0, 0%, 0%);
  --tiptap-warn-background: hsl(56, 100%, 86%);
  --tiptap-error-foreground: hsl(358, 75%, 59%);
  --tiptap-error-background: hsl(357, 90%, 96%);
}

// region scrollbar

::-webkit-scrollbar {
  .ProseMirror-bm& {
    width: 5px;
  }
}

::-webkit-scrollbar-track {
  .ProseMirror-bm& {
    background: transparent;
  }
}

::-webkit-scrollbar-thumb {
  .ProseMirror-bm& {
    background: var(--tiptap-border);
    border-radius: 5px;
  }
}

.ProseMirror-bm * {
  scrollbar-width: thin;
  scrollbar-color: var(--tiptap-border) transparent;
}

// endregion

// region keyframes

@keyframes ProseMirror-cursor-blink {
  to {
    visibility: hidden;
  }
}

@keyframes ProseMirror-spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// endregion

// region icons

.ProseMirror-icon {
  display: inline-block;
  width: 1em;
  height: 1em;
  mask-image: var(--svg);
  mask-repeat: no-repeat;
  mask-size: 100% 100%;
  background-color: currentColor;

  &-check {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M20 6L9 17l-5-5'/%3E%3C/svg%3E")
  }

  &-empty {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M13.73 4a2 2 0 0 0-3.46 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3Z'/%3E%3C/svg%3E");
  }

  &-error {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m21.73 18l-8-14a2 2 0 0 0-3.48 0l-8 14A2 2 0 0 0 4 21h16a2 2 0 0 0 1.73-3M12 9v4m0 4h.01'/%3E%3C/svg%3E");
  }

  &-loading {
    animation: spin 1s linear infinite;
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M21 12a9 9 0 1 1-6.219-8.56'/%3E%3C/svg%3E");
  }

  &-open {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M7 7h10v10M7 17L17 7'/%3E%3C/svg%3E");
  }

  &-upload {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cg fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'%3E%3Cpath d='M12 13v8m-8-6.101A7 7 0 1 1 15.71 8h1.79a4.5 4.5 0 0 1 2.5 8.242'/%3E%3Cpath d='m8 17l4-4l4 4'/%3E%3C/g%3E%3C/svg%3E");
  }

  &-remove {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M10 5a2 2 0 0 0-1.344.519l-6.328 5.74a1 1 0 0 0 0 1.481l6.328 5.741A2 2 0 0 0 10 19h10a2 2 0 0 0 2-2V7a2 2 0 0 0-2-2zm2 4l6 6m0-6l-6 6'/%3E%3C/svg%3E");
  }

  &-bold {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 12h9a4 4 0 0 1 0 8H7a1 1 0 0 1-1-1V5a1 1 0 0 1 1-1h7a4 4 0 0 1 0 8'/%3E%3C/svg%3E");
  }

  &-italic {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M19 4h-9m4 16H5M15 4L9 20'/%3E%3C/svg%3E");
  }

  &-code {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m18 16l4-4l-4-4M6 8l-4 4l4 4m8.5-12l-5 16'/%3E%3C/svg%3E");
  }

  &-strike {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M16 4H9a3 3 0 0 0-2.83 4M14 12a4 4 0 0 1 0 8H6m-2-8h16'/%3E%3C/svg%3E");
  }

  &-highlight {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cg fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'%3E%3Cpath d='m9 11l-6 6v3h9l3-3'/%3E%3Cpath d='m22 12l-4.6 4.6a2 2 0 0 1-2.8 0l-5.2-5.2a2 2 0 0 1 0-2.8L14 4'/%3E%3C/g%3E%3C/svg%3E");
  }

  &-link {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M9 17H7A5 5 0 0 1 7 7h2m6 0h2a5 5 0 1 1 0 10h-2m-7-5h8'/%3E%3C/svg%3E");
  }

  &-underline {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M6 4v6a6 6 0 0 0 12 0V4M4 20h16'/%3E%3C/svg%3E");
  }

  &-sup {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m4 19l8-8m0 8l-8-8m16 1h-4c0-1.5.442-2 1.5-2.5S20 8.334 20 7.002c0-.472-.17-.93-.484-1.29a2.105 2.105 0 0 0-2.617-.436c-.42.239-.738.614-.899 1.06'/%3E%3C/svg%3E");
  }

  &-sub {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m4 5l8 8m0-8l-8 8m16 6h-4c0-1.5.44-2 1.5-2.5S20 15.33 20 14c0-.47-.17-.93-.48-1.29a2.11 2.11 0 0 0-2.62-.44c-.42.24-.74.62-.9 1.07'/%3E%3C/svg%3E");
  }

  &-blockquote {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M16 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1a6 6 0 0 0 6-6V5a2 2 0 0 0-2-2zM5 3a2 2 0 0 0-2 2v6a2 2 0 0 0 2 2a1 1 0 0 1 1 1v1a2 2 0 0 1-2 2a1 1 0 0 0-1 1v2a1 1 0 0 0 1 1a6 6 0 0 0 6-6V5a2 2 0 0 0-2-2z'/%3E%3C/svg%3E");
  }

  &-ul {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M3 12h.01M3 18h.01M3 6h.01M8 12h13M8 18h13M8 6h13'/%3E%3C/svg%3E");
  }

  &-ol {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M10 12h11m-11 6h11M10 6h11M4 10h2M4 6h1v4m1 8H4c0-1 2-2 2-3s-1-1.5-2-1'/%3E%3C/svg%3E");
  }

  &-tl {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m3 17l2 2l4-4M3 7l2 2l4-4m4 1h8m-8 6h8m-8 6h8'/%3E%3C/svg%3E");
  }

  &-h1 {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 12h8m-8 6V6m8 12V6m5 6l3-2v8'/%3E%3C/svg%3E");
  }

  &-h2 {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 12h8m-8 6V6m8 12V6m9 12h-4c0-4 4-3 4-6c0-1.5-2-2.5-4-1'/%3E%3C/svg%3E");
  }

  &-h3 {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 12h8m-8 6V6m8 12V6m5.5 4.5c1.7-1 3.5 0 3.5 1.5a2 2 0 0 1-2 2m-2 3.5c2 1.5 4 .3 4-1.5a2 2 0 0 0-2-2'/%3E%3C/svg%3E");
  }

  &-h4 {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M12 18V6m5 4v3a1 1 0 0 0 1 1h3m0-4v8M4 12h8m-8 6V6'/%3E%3C/svg%3E");
  }

  &-h5 {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 12h8m-8 6V6m8 12V6m5 7v-3h4m-4 7.7c.4.2.8.3 1.3.3c1.5 0 2.7-1.1 2.7-2.5S19.8 13 18.3 13H17'/%3E%3C/svg%3E");
  }

  &-h6 {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cg fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'%3E%3Cpath d='M4 12h8m-8 6V6m8 12V6'/%3E%3Ccircle cx='19' cy='16' r='2'/%3E%3Cpath d='M20 10c-2 2-3 3.5-3 6'/%3E%3C/g%3E%3C/svg%3E");
  }

  &-hr {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M3 12h18M8 8l4-4l4 4m0 8l-4 4l-4-4'/%3E%3C/svg%3E");
  }

  &-image {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cg fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'%3E%3Crect width='18' height='18' x='3' y='3' rx='2' ry='2'/%3E%3Ccircle cx='9' cy='9' r='2'/%3E%3Cpath d='m21 15l-3.086-3.086a2 2 0 0 0-2.828 0L6 21'/%3E%3C/g%3E%3C/svg%3E");
  }

  &-plus {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M5 12h14m-7-7v14'/%3E%3C/svg%3E");
  }

  &-drag {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M8.5 7a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m0 6.5a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m1.5 5a1.5 1.5 0 1 1-3 0a1.5 1.5 0 0 1 3 0M15.5 7a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3m1.5 5a1.5 1.5 0 1 1-3 0a1.5 1.5 0 0 1 3 0m-1.5 8a1.5 1.5 0 1 0 0-3a1.5 1.5 0 0 0 0 3'/%3E%3C/svg%3E");
  }

  &-left {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m15 18l-6-6l6-6'/%3E%3C/svg%3E");
  }

  &-right {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m9 18l6-6l-6-6'/%3E%3C/svg%3E");
  }

  &-up {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m18 15l-6-6l-6 6'/%3E%3C/svg%3E");
  }

  &-down {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 9l6 6l6-6'/%3E%3C/svg%3E");
  }

  &-table {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cg fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'%3E%3Cpath d='M12 3v18'/%3E%3Crect width='18' height='18' x='3' y='3' rx='2'/%3E%3Cpath d='M3 9h18M3 15h18'/%3E%3C/g%3E%3C/svg%3E");
  }

  &-align-left {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M15 12H3m14 6H3M21 6H3'/%3E%3C/svg%3E");
  }

  &-align-center {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M17 12H7m12 6H5M21 6H3'/%3E%3C/svg%3E");
  }

  &-align-right {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M21 12H9m12 6H7M21 6H3'/%3E%3C/svg%3E");
  }

  &-merge-cells {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cg fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'%3E%3Cpath d='M12 21v-6m0-6V3M3 15h18M3 9h18'/%3E%3Crect width='18' height='18' x='3' y='3' rx='2'/%3E%3C/g%3E%3C/svg%3E");
  }

  &-split-cells {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cg fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'%3E%3Cpath d='M12 15V9m-9 6h18M3 9h18'/%3E%3Crect width='18' height='18' x='3' y='3' rx='2'/%3E%3C/g%3E%3C/svg%3E");
  }

  &-header-row {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M19 12H5v7h14zM4 3h16a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1'/%3E%3C/svg%3E");
  }

  &-header-col {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M12 5v14h7V5zM4 3h16a1 1 0 0 1 1 1v16a1 1 0 0 1-1 1H4a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1'/%3E%3C/svg%3E");
  }

  &-help {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cg fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'%3E%3Ccircle cx='12' cy='12' r='10'/%3E%3Cpath d='M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3m.08 4h.01'/%3E%3C/g%3E%3C/svg%3E");
  }

  &-math {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 24 24'%3E%3Cpath fill='%23000' d='M10 2a4 4 0 0 0-4 4v3H3v2h3v7a2 2 0 0 1-2 2H3v2h1a4 4 0 0 0 4-4v-7h3V9H8V6a2 2 0 0 1 2-2h1V2zm5.202 14.997L11.891 21h2.595l2.014-2.434L18.514 21h2.595l-3.311-4.003L21.105 13h-2.596L16.5 15.428L14.491 13h-2.595z'/%3E%3C/svg%3E");
  }

  &-audio {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 13a2 2 0 0 0 2-2V7a2 2 0 0 1 4 0v13a2 2 0 0 0 4 0V4a2 2 0 0 1 4 0v13a2 2 0 0 0 4 0v-4a2 2 0 0 1 2-2'/%3E%3C/svg%3E");
  }

  &-video {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cg fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'%3E%3Cpath d='m16 13l5.223 3.482a.5.5 0 0 0 .777-.416V7.87a.5.5 0 0 0-.752-.432L16 10.5'/%3E%3Crect width='14' height='12' x='2' y='6' rx='2'/%3E%3C/g%3E%3C/svg%3E");
  }

  &-embed {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cg fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2'%3E%3Crect width='20' height='16' x='2' y='4' rx='2'/%3E%3Cpath d='M10 4v4M2 8h20M6 4v4'/%3E%3C/g%3E%3C/svg%3E");
  }

  &-details {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24'%3E%3Cpath fill='none' stroke='%23000' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='m6 14l1.5-2.9A2 2 0 0 1 9.24 10H20a2 2 0 0 1 1.94 2.5l-1.54 6a2 2 0 0 1-1.95 1.5H4a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h3.9a2 2 0 0 1 1.69.9l.81 1.2a2 2 0 0 0 1.67.9H18a2 2 0 0 1 2 2v2'/%3E%3C/svg%3E");
  }

  &-mermaid {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 16 16'%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M11.5 12.5A1.5 1.5 0 0 1 10 14H6a1.5 1.5 0 0 1-1.5-1.5v-.823a3.114 3.114 0 0 0-1.35-2.566A6.114 6.114 0 0 1 .5 4.073V3A1.5 1.5 0 0 1 2 1.5h.666A6.425 6.425 0 0 1 8 4.343A6.425 6.425 0 0 1 13.334 1.5H14A1.5 1.5 0 0 1 15.5 3v1.073a6.114 6.114 0 0 1-2.65 5.038a3.114 3.114 0 0 0-1.35 2.566zm-8-9.43a4.924 4.924 0 0 1 3.738 3.025c.275.688 1.249.688 1.524 0A4.924 4.924 0 0 1 13.334 3H14v1.073a4.614 4.614 0 0 1-2 3.802c-1.252.86-2 2.283-2 3.802v.823H6v-.823c0-1.52-.748-2.941-2-3.802a4.614 4.614 0 0 1-2-3.802V3h.666c.283 0 .562.024.834.07' clip-rule='evenodd'/%3E%3C/svg%3E");
  }

  &-plantuml {
    --svg: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='1em' height='1em' viewBox='0 0 16 16'%3E%3Cpath fill='%23000' fill-rule='evenodd' d='M5.75 2.5h4.5a1.5 1.5 0 0 1 1.269.7a2.5 2.5 0 0 0 .231 4.686V12a1.5 1.5 0 0 1-1.5 1.5h-4.5c-.534 0-1.003-.28-1.269-.7a2.5 2.5 0 0 0-.231-4.686v-.228A2.501 2.501 0 0 0 4.481 3.2c.266-.42.735-.7 1.269-.7m-3 5.614v-.228a2.501 2.501 0 0 1 .146-4.813A3.001 3.001 0 0 1 5.75 1h4.5a3 3 0 0 1 2.854 2.074a2.501 2.501 0 0 1 .146 4.812V12a3 3 0 0 1-3 3h-4.5a3.001 3.001 0 0 1-2.854-2.073a2.501 2.501 0 0 1-.146-4.813M3.5 11.5a1 1 0 1 0 0-2a1 1 0 0 0 0 2m-1-6a1 1 0 1 1 2 0a1 1 0 0 1-2 0m10-1a1 1 0 1 0 0 2a1 1 0 0 0 0-2' clip-rule='evenodd'/%3E%3C/svg%3E");
  }
}

// endregion

// region popover

.tippy-box[data-theme="ProseMirror"],
.tippy-box[data-theme="ProseMirror-dark"],
.tippy-box[data-theme="ProseMirror-none"] {
  font-size: var(--tiptap-font-size);
  font-family: var(--tiptap-font-family);
  font-weight: var(--tiptap-font-weight);
  line-height: var(--tiptap-line-height);
  color: var(--tiptap-popover-foreground);
  background-color: transparent;

  .tippy-content {
    padding: 0;
  }
}

.tippy-box[data-theme="ProseMirror"] {
  color: var(--tiptap-popover-foreground);
  background-color: var(--tiptap-popover-background);
  border: 1px solid var(--tiptap-border);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1);
}

.tippy-box[data-theme="ProseMirror-dark"] {
  color: var(--tiptap-popover-background);
  background-color: var(--tiptap-popover-foreground);
  border: 1px solid var(--tiptap-border);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, .1), 0 2px 4px -2px rgba(0, 0, 0, .1);
}

// endregion

// region colors

.ProseMirror,
.tippy-box[data-theme="ProseMirror"],
.tippy-box[data-theme="ProseMirror-dark"],
.tippy-box[data-theme="ProseMirror-none"] {
  @colors: {
    none: var(--tiptap-foreground);
    gray: #8d8d8d;
    tomato: #e54d2e;
    red: #e5484d;
    ruby: #e54666;
    crimson: #e93d82;
    pink: #d6409f;
    plum: #ab4aba;
    purple: #8e4ec6;
    violet: #6e56cf;
    iris: #5b5bd6;
    indigo: #3e63dd;
    blue: #0090ff;
    cyan: #00a2c7;
    teal: #12a594;
    jade: #29a383;
    green: #30a46c;
    bronze: #a18072;
    gold: #978365;
    brown: #ad7f58;
    orange: #f76b15;
    amber: #ffc53d;
    yellow: #ffe629;
    lime: #bdee63;
    mint: #86ead4;
    sky: #7ce2fe;
  };

  @backgrounds: {
    none: var(--tiptap-background);
    gray: #f0f0f0;
    tomato: #feebe7;
    red: #feebec;
    ruby: #feeaed;
    crimson: #ffe9f0;
    pink: #fee9f5;
    plum: #fbebfb;
    purple: #f7edfe;
    violet: #f4f0fe;
    iris: #f0f1fe;
    indigo: #e1e9ff;
    blue: #e6f4fe;
    cyan: #def7f9;
    teal: #e0f8f3;
    jade: #e6f7ed;
    green: #e6f6eb;
    bronze: #f6edea;
    gold: #f2f0e7;
    brown: #f6eee7;
    orange: #ffefd6;
    amber: #fff7c2;
    yellow: #fffab8;
    lime: #eef6d6;
    mint: #ddf9f2;
    sky: #e1f6fd;
  };

  each(@colors, .(@v, @k) {
    [data-color="@{k}"] {
      color: @v;
      fill: @v;
      background-color: inherit;
    }
  });

  each(@backgrounds, .(@v, @k) {
    [data-color="b-@{k}"] {
      background-color: @v;
    }
  });
}

// endregion

// region block-menu

.ProseMirror-bm {
  white-space: nowrap;
  pointer-events: all;
  width: 20rem;
  max-height: 15rem;
  padding: 0.25rem;
  overflow-x: hidden;
  overflow-y: auto;
  border-width: 1px;
  border-radius: var(--tiptap-radius);

  &-empty {
    display: flex;
    align-items: center;
    font-weight: 500;
    font-size: 0.875rem;
    height: 2.25rem;
    padding: 0 0.75rem;
  }

  &-divider {
    display: block;
    height: 1px;
    margin: 0.25rem -0.25rem;
    background-color: var(--tiptap-muted-background);
  }

  &-button {
    appearance: none;
    user-select: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    line-height: 1;
    font-weight: 500;
    font-size: 0.875rem;
    border: none;
    outline: none;
    width: 100%;
    height: 2.25rem;
    padding: 0.375rem 0.5rem;
    border-radius: var(--tiptap-radius);
    background-color: transparent;
    transition-duration: 0.15s;
    transition-property: color, background-color, box-shadow;

    &:hover,
    &:focus,
    &[data-active] {
      color: var(--tiptap-accent-foreground);
      background-color: var(--tiptap-accent-background);
    }

    &-icon {
      margin-left: 0.25rem;
      margin-right: 0.75rem;
      width: 1rem;
      height: 1rem;

      .ProseMirror-icon {
        width: 1rem;
        height: 1rem;
      }
    }

    &-name {
      flex-grow: 1;
      text-align: start;
    }

    &-shortcut {
      color: var(--tiptap-muted-foreground);
      font-family: var(--tiptap-font-family-mono);
      font-size: 0.625rem;
      letter-spacing: 0.2em;
      text-transform: uppercase;
    }
  }

  &-placeholder::before {
    content: attr(data-empty);
    display: block;
    pointer-events: none;
    padding-left: 0.125rem;
    height: 0;
    opacity: 0;
    color: var(--tiptap-muted-foreground);
    font-size: calc(var(--tiptap-font-size) - 0.1em);
    transition-duration: 0.15s;
    transition-property: opacity;
  }

  .ProseMirror-focused &-placeholder::before {
    opacity: 1;
  }

  .ProseMirror[contenteditable="false"] &-placeholder::before {
    opacity: 0;
  }
}

// endregion

// region float-menu

.ProseMirror-fm {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0.375rem 0.5rem;

  &-divider {
    display: block;
    width: 1px;
    height: 1.3rem;
    background-color: var(--tiptap-muted-background);
  }

  &-button {
    appearance: none;
    display: flex;
    justify-content: center;
    align-items: center;
    border: none;
    outline: none;
    cursor: pointer;
    width: 2rem;
    height: 2rem;
    margin: 0 0.1rem;
    border-radius: var(--tiptap-radius);
    background-color: transparent;
    transition-duration: 0.15s;
    transition-property: color, background-color, box-shadow;

    &:first-child {
      margin-left: 0;
    }

    &:last-child {
      margin-right: 0;
    }

    &:hover,
    &:focus {
      color: var(--tiptap-accent-foreground);
      background-color: var(--tiptap-accent-background);
    }

    &[data-active] {
      color: var(--tiptap-primary-background);
      background-color: var(--tiptap-accent-background);
    }

    &-popover {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 0.25rem 0.5rem;
      font-size: 0.75rem;
      border-radius: var(--tiptap-radius);
    }

    &-shortcut {
      color: var(--tiptap-muted-background);
      font-family: var(--tiptap-font-family-mono);
      font-size: 0.625rem;
      letter-spacing: 0.2em;
      text-transform: uppercase;
    }

    .ProseMirror-icon {
      width: 1.2rem;
      height: 1.2rem;
    }
  }

  &-input {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;

    label {
      font-size: 0.75rem;
      line-height: 1;
      color: var(--tiptap-popover-foreground);
    }

    input {
      display: flex;
      outline: none;
      border-radius: var(--tiptap-radius);
      border: 1px solid var(--tiptap-border);
      background-color: transparent;
      padding: 0.25rem 0.75rem;
      width: 100%;
      height: 2rem;
      font-size: 0.875rem;
      line-height: 1.25rem;
      transition-duration: 0.15s;
      transition-property: color, border, box-shadow;
      box-sizing: border-box;

      &:focus {
        border: 1px solid var(--tiptap-input);
        box-shadow: 0 0 0 1px var(--tiptap-ring);
      }

      &::placeholder {
        color: var(--tiptap-muted-foreground);
      }
    }
  }

  &-textarea {
    display: flex;
    resize: none;
    border: none;
    outline: none;
    background-color: transparent;
    padding: 0;
    width: 100%;
    font-size: 0.875rem;
    line-height: 1.25rem;
    transition-duration: 0.15s;
    transition-property: color, border, box-shadow;
    box-sizing: border-box;

    &::placeholder {
      color: var(--tiptap-muted-foreground);
    }
  }

  &-form {
    padding: 0.375rem;
    gap: 0.5rem;
    display: flex;
    flex-direction: column;
    min-width: 18rem;
  }

  &-action {
    margin-bottom: -0.25rem;
    gap: 0.375rem;
    display: flex;
    justify-content: flex-end;
  }

  &-color-picker {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 0.5rem;

    div {
      display: grid;
      grid-template-columns: repeat(13, 1fr);
      gap: 0.5rem;
    }

    button {
      appearance: none;
      cursor: pointer;
      line-height: 1;
      padding: 0.5rem;
      border-radius: var(--tiptap-radius);
      border: 1px solid var(--tiptap-border);
      transition-duration: 0.15s;
      transition-property: color, border, box-shadow;

      &:hover {
        border: 1px solid var(--tiptap-input);
        box-shadow: 0 0 0 1px var(--tiptap-ring);
      }

      span {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 1em !important;
        height: 1em !important;
      }
    }
  }

  &-input + &-button,
  &-group + &-button {
    margin-left: 0.4rem;
  }

  &-button + &-input,
  &-button + &-group {
    margin-right: 0.4rem;
  }
}

// endregion

// region click-menu

.ProseMirror-cm {
  display: flex;
  gap: 0.25rem;

  &-plus,
  &-drag {
    display: flex;
    justify-content: center;
    align-items: center;
    line-height: 1;
    width: 1.25rem;
    height: 1.25rem;
    padding: 0.25rem 0;
    transition-duration: 0.15s;
    transition-property: color, background-color, box-shadow;
    border-radius: var(--tiptap-radius);

    &:hover,
    &:focus,
    &[data-active] {
      background-color: var(--tiptap-accent-background);
    }
  }

  &-plus {
    cursor: pointer;
  }

  &-drag {
    cursor: grab;

    &:focus,
    &:active {
      cursor: grabbing;
    }
  }
}

// endregion

// region editor

.ProseMirror {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  max-width: 100%;
  width: 100%;
  outline: none;
  word-wrap: break-word;
  white-space: break-spaces;
  font-variant-ligatures: none;
  font-feature-settings: "liga" 0;
  color: var(--tiptap-foreground);
  background-color: var(--tiptap-background);
  font-size: var(--tiptap-font-size);
  font-family: var(--tiptap-font-family);
  font-weight: var(--tiptap-font-weight);
  line-height: var(--tiptap-line-height);
  box-sizing: border-box;

  &::before {
    content: "";
    position: absolute;
    left: -4em;
    width: 4em;
    height: 100%;
  }

  * {
    box-sizing: border-box;
  }

  > * {
    margin: 0.5em 0;
    width: 100%;
    max-width: 100%;
  }

  b,
  strong {
    font-weight: 600;
  }

  code {
    border-radius: 0.25em;
    padding: 0.2em 0.25em;
    font-family: var(--tiptap-font-family-mono);
    font-size: 85%;
    tab-size: 4;
    color: var(--tiptap-error-foreground);
    background-color: var(--tiptap-error-background);
  }

  mark {
    color: var(--tiptap-warn-foreground);
    background-color: var(--tiptap-warn-background);
  }

  a {
    cursor: pointer;
    color: var(--tiptap-primary-background);
  }

  blockquote {
    margin: 0.5em 0;
    padding-left: 1em;
    font-style: italic;
    overflow: hidden;
    position: relative;

    &::before {
      content: "";
      display: inline-block;
      width: 3px;
      border-radius: 1px;
      position: absolute;
      margin-left: -1em;
      top: 0;
      bottom: 0;
      background-color: var(--tiptap-primary-background);
    }
  }

  p {
    margin: 0.25em 0;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    margin: 0.25em 0;
    font-weight: 500;
  }

  hr {
    position: relative;
    height: 1em;
    border: 0;

    &::before {
      content: "";
      display: block;
      position: absolute;
      border-top: 1px solid var(--tiptap-primary-background);
      top: 0.5em;
      left: 0;
      right: 0;
    }
  }

  ul,
  ol {
    margin: 0;
    padding: 0 0 0 1.2em;
  }

  ol {
    ol {
      list-style: lower-alpha;

      ol {
        list-style: lower-roman;
      }
    }
  }

  li {
    position: relative;

    &::before {
      content: "";
      position: absolute;
      left: -3em;
      width: 3em;
      top: 0;
      bottom: 0;
    }
  }

  table {
    border-collapse: collapse;
    box-sizing: border-box;
    border-radius: var(--tiptap-radius);
    width: 100%;

    tr {
      position: relative;
      border: 1px solid var(--tiptap-border);
    }

    th,
    td {
      position: relative;
      min-width: 50px;
      padding: 0.25em 0.5em;
      border: 1px solid var(--tiptap-border);
    }

    [align="left"],
    [align="start"] {
      text-align: start;
    }

    [align="center"] {
      text-align: center;
    }

    [align="right"],
    [align="end"] {
      text-align: end;
    }

    .selectedCell {
      background-color: var(--tiptap-selected);
      background-clip: padding-box;
    }

    .column-resize-handle {
      position: absolute;
      right: -2px;
      top: 0;
      bottom: -2px;
      width: 4px;
      background-color: var(--tiptap-primary-background);
      pointer-events: none;
    }

    .ProseMirror-table-grip-drag {
      cursor: grab;
      display: flex;
      justify-content: center;
      align-items: center;
      line-height: 1;
      width: 0.375rem;
      height: 1.75rem;
      border-radius: var(--tiptap-radius);
      transition-duration: 0.15s;
      transition-property: width, height, border-radius;
      border: 1px solid var(--tiptap-border);
      background-color: var(--tiptap-accent-background);

      .ProseMirror-icon {
        opacity: 0;
        transition-duration: 0.15s;
        transition-property: opacity;
      }
    }

    .ProseMirror-table-grip-col,
    .ProseMirror-table-grip-row {
      position: absolute;
      width: 1rem;
      height: 1.75rem;
      display: flex;
      align-items: center;
      justify-content: center;

      &:hover,
      &:focus,
      &:active,
      &.active {
        .ProseMirror-table-grip-drag {
          width: 1rem;
          border-radius: var(--tiptap-radius);
          color: var(--tiptap-primary-foreground);
          border: 1px solid var(--tiptap-primary-background);
          background-color: var(--tiptap-primary-background);

          .ProseMirror-icon {
            opacity: 1;
          }
        }
      }
    }

    .ProseMirror-table-grip-col {
      top: 0;
      left: 50%;
      transform: rotate(90deg) translateX(-0.85rem) translateY(50%);
    }

    .ProseMirror-table-grip-row {
      left: 0;
      top: 50%;
      transform: translateX(-50%) translateY(-50%);
    }

    .ProseMirror-table-grip-cell {
      position: absolute;
      top: 0;
      left: 50%;
      transform: translateX(-50%);
    }

    .ProseMirror-table-grip-table {
      position: absolute;
      top: 0;
      left: 0;
    }
  }

  [data-type="image"] {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0.5em 0;
    border-radius: var(--tiptap-radius);
    border: 1px solid var(--tiptap-border);

    > img {
      display: block;
      border: 0;
      width: 100%;
      object-fit: cover;
      border-radius: var(--tiptap-radius);
    }

    > span {
      display: none;
      justify-content: center;
      align-items: center;
      gap: 0.375rem;
    }

    &[data-status] {
      resize: none;
      padding: 0.5em 0.5em;
      border: 1px solid var(--tiptap-border);

      > img {
        display: none;
      }

      .ProseMirror-lresizer,
      .ProseMirror-rresizer,
      .ProseMirror-bresizer {
        display: none;
      }
    }

    &[data-status="empty"] {
      color: var(--tiptap-warn-foreground);
      background-color: var(--tiptap-warn-background);

      > span:nth-of-type(1) {
        display: inline-flex;
      }
    }

    &[data-status="error"] {
      color: var(--tiptap-error-foreground);
      background-color: var(--tiptap-error-background);

      > span:nth-of-type(2) {
        display: inline-flex;
      }
    }

    &[data-status="loading"] {
      color: var(--tiptap-info-foreground);
      background-color: var(--tiptap-info-background);

      > span:nth-of-type(3) {
        display: inline-flex;
      }
    }
  }

  [data-type="audio"],
  [data-type="video"] {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0.5em 0;
    border-radius: var(--tiptap-radius);
    border: 1px solid var(--tiptap-border);

    video,
    audio,
    .plyr,
    .plyr__controls {
      --plyr-color-main: var(--tiptap-accent-foreground);
      --plyr-menu-background: var(--tiptap-popover-background);
      --plyr-audio-control-color: var(--tiptap-accent-foreground);

      width: 100%;
      border-radius: var(--tiptap-radius);

      .plyr__menu__container {
        --plyr-audio-controls-background: var(--tiptap-popover-background);
        --plyr-audio-control-color-hover: var(--tiptap-muted-foreground);
        --plyr-audio-control-background-hover: var(--tiptap-muted-background);
        --plyr-video-control-color: var(--tiptap-accent-foreground);
        --plyr-video-controls-background: var(--tiptap-popover-background);
        --plyr-video-control-color-hover: var(--tiptap-muted-foreground);
        --plyr-video-control-background-hover: var(--tiptap-muted-background);
      }
    }
  }

  [data-type="embed"] {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 0.5em 0;
    border-radius: var(--tiptap-radius);
    border: 1px solid var(--tiptap-border);

    iframe {
      width: 100%;
      height: 100%;
      border: 0;
      border-radius: var(--tiptap-radius);
    }
  }

  [data-type="details"] {
    display: flex;
    padding: 0.5em;
    border-radius: var(--tiptap-radius);
    border: 1px solid transparent;
    transition-duration: 0.15s;
    transition-property: border;

    &:hover {
      border: 1px solid var(--tiptap-border);
    }

    [data-type="detailsButton"] {
      display: flex;
      cursor: pointer;
      border: none;
      padding: 0;
      background-color: transparent;

      .ProseMirror-icon {
        width: 1.25em;
        height: 1.25em;
        transform: rotateZ(0deg);
      }

      > div {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 2em;
        height: 2em;
        border-radius: var(--tiptap-radius);

        &:hover {
          color: var(--tiptap-primary-background);
          background-color: var(--tiptap-accent-background);
        }
      }
    }

    [data-type="detailsContainer"] {
      flex: 1;
      margin-left: 0.25em;

      [data-type="detailsContent"] {
        display: none;
      }
    }

    &[open] {
      [data-type="detailsButton"] {
        .ProseMirror-icon {
          transform: rotateZ(90deg);
        }
      }

      [data-type="detailsContainer"] {
        [data-type="detailsContent"] {
          display: block;
        }
      }
    }
  }

  [data-type="mathInline"] {
    .ProseMirror-info {
      padding: 0 0.5em;
      border-radius: var(--tiptap-radius);
    }
  }

  [data-type="codeBlock"] {
    position: relative;
    display: flex;
    flex-direction: column;
    padding: 0.75em 1em;
    gap: 0.5em;
    border-radius: var(--tiptap-radius);
    border: 1px solid var(--tiptap-border);
    background-color: var(--tiptap-card-background);

    // toolbar
    [data-type="codeBlockToolbar"] {
      position: absolute;
      top: 0.5em;
      right: 0.5em;
      display: flex;
      gap: 0.5em;
      opacity: 0;
      transition-duration: 0.15s;
      transition-property: opacity;

      select {
        display: inline-block;
        font-size: 0.85em;
        padding: 0.25em;
        border-radius: var(--tiptap-radius);
        outline: 0;
        border: 1px solid var(--tiptap-border);
        background-color: var(--tiptap-popover-background);
        transition-duration: 0.15s;
        transition-property: color, border, box-shadow;

        &:hover,
        &:focus,
        &:active,
        &.active {
          border: 1px solid var(--tiptap-border);
          box-shadow: 0 0 0 1px var(--tiptap-ring);
        }
      }

      button {
        appearance: none;
        display: inline-block;
        font-size: 0.85em;
        padding: 0.25em;
        border-radius: var(--tiptap-radius);
        outline: 0;
        border: 1px solid var(--tiptap-border);
        background-color: var(--tiptap-popover-background);
        transition-duration: 0.15s;
        transition-property: color, border, box-shadow;

        &:hover,
        &:focus,
        &:active,
        &.active {
          border: 1px solid var(--tiptap-border);
          box-shadow: 0 0 0 1px var(--tiptap-ring);
        }
      }
    }

    &:hover [data-type="codeBlockToolbar"] {
      opacity: 1;
    }

    // content
    [data-type="codeBlockContent"] {
      white-space: pre !important;
      line-height: 1.5 !important;
      background: none !important;
      padding: 0 !important;
      border: 0 !important;
    }

    .hljs-comment,
    .hljs-quote {
      color: #a0a1a7;
      font-style: italic;
    }

    .hljs-doctag,
    .hljs-keyword,
    .hljs-formula {
      color: #a626a4;
    }

    .hljs-section,
    .hljs-name,
    .hljs-selector-tag,
    .hljs-deletion,
    .hljs-subst {
      color: #e45649;
    }

    .hljs-literal {
      color: #0184bb;
    }

    .hljs-string,
    .hljs-regexp,
    .hljs-addition,
    .hljs-attribute,
    .hljs-meta .hljs-string {
      color: #50a14f;
    }

    .hljs-attr,
    .hljs-variable,
    .hljs-template-variable,
    .hljs-type,
    .hljs-selector-class,
    .hljs-selector-attr,
    .hljs-selector-pseudo,
    .hljs-number {
      color: #986801;
    }

    .hljs-symbol,
    .hljs-bullet,
    .hljs-link,
    .hljs-meta,
    .hljs-selector-id,
    .hljs-title {
      color: #4078f2;
    }

    .hljs-built_in,
    .hljs-title.class_,
    .hljs-class .hljs-title {
      color: #c18401;
    }

    .hljs-emphasis {
      font-style: italic;
    }

    .hljs-strong {
      font-weight: bold;
    }

    .hljs-link {
      text-decoration: underline;
    }
  }

  [data-type="taskList"] {
    list-style: none;
    margin: 0;
    padding: 0;

    input {
      accent-color: var(--tiptap-primary-background);
    }

    li {
      display: flex;

      label {
        padding-right: 0.15em;
        padding-top: 0.3em;

        input {
          width: 1em;
          height: 1em;
        }
      }
    }

    div {
      width: 100%;
    }
  }
}

.resize-cursor {
  cursor: col-resize;
}

.ProseMirror-mono {
  font-family: var(--tiptap-font-family-mono);
}

.ProseMirror-info {
  color: var(--tiptap-info-foreground);
  background-color: var(--tiptap-info-background);
}

.ProseMirror-warn {
  color: var(--tiptap-warn-foreground);
  background-color: var(--tiptap-warn-background);
}

.ProseMirror-error {
  color: var(--tiptap-error-foreground);
  background-color: var(--tiptap-error-background);
}

.ProseMirror-separator {
  && {
    display: none !important;
  }
}

.ProseMirror-gapcursor {
  .ProseMirror[contenteditable="true"] & {
    display: none;
    pointer-events: none;
    position: relative;

    &::after {
      content: "";
      display: block;
      position: absolute;
      top: -3px;
      width: 20px;
      border-top: 2px solid var(--tiptap-ring);
      animation: ProseMirror-cursor-blink 1.1s steps(2, start) infinite;
    }
  }

  .ProseMirror-focused[contenteditable="true"] & {
    display: block;
  }
}

.ProseMirror-selectednode {
  .ProseMirror[contenteditable="true"] & {
    position: relative;

    &::after {
      content: "";
      position: absolute;
      left: -4px;
      right: -4px;
      top: -4px;
      bottom: -4px;
      border-radius: var(--tiptap-radius);
      pointer-events: none;
      background-color: var(--tiptap-selected);
    }

    &.ProseMirror-selectedcard {
      border: 1px solid var(--tiptap-ring);
      box-shadow: 0 0 0 1px var(--tiptap-ring);

      &::after {
        content: none !important;
      }
    }

    li& {
      &::after {
        left: -24px;
      }
    }

    ul[data-type="taskList"] li& {
      &::after {
        left: -4px;
      }
    }
  }
}

.ProseMirror-hideselection {
  caret-color: transparent;

  *::selection {
    background-color: transparent;
  }
}

.ProseMirror-inner-editor {
  position: relative;
  border-radius: var(--tiptap-radius);

  .ProseMirror {
    white-space: pre !important;
    line-height: 1.5 !important;
    font-family: var(--tiptap-font-family-mono) !important;
    font-weight: normal !important;
    font-size: 0.875em !important;
    background: none !important;
    overflow: auto !important;
    border: 0 !important;
    padding: 0.125rem !important;
  }

  &-editor {
    padding: 0.75em 1em;
    background-color: var(--tiptap-card-background);
    border-top-left-radius: var(--tiptap-radius);
    border-top-right-radius: var(--tiptap-radius);
    border-bottom: 1px solid var(--tiptap-border);
  }

  &-preview {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: auto;
    padding: 0.75em 1em;
    border-bottom-left-radius: var(--tiptap-radius);
    border-bottom-right-radius: var(--tiptap-radius);

    [id^="mermaid"] {
      width: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
    }
  }

  .ProseMirror[contenteditable="false"] & {
    &-editor {
      display: none !important;
    }
  }
}

.ProseMirror-inner-resizer {
  position: relative;
  border-radius: var(--tiptap-radius);

  .ProseMirror-lresizer,
  .ProseMirror-rresizer,
  .ProseMirror-bresizer {
    position: absolute;
    border-radius: 5px;
    border: 1px solid var(--tiptap-muted-background);
    background-color: var(--tiptap-muted-foreground);
    opacity: 0;
    transition: opacity 0.15s;

    > div {
      position: fixed;
      top: 0;
      bottom: 0;
      left: 0;
      right: 0;
    }

    &.ProseMirror-lresizer {
      right: 0.25rem;
      width: 5px;
      height: 40px;
      cursor: col-resize;

      > div {
        cursor: col-resize;
      }
    }

    &.ProseMirror-rresizer {
      left: 0.25rem;
      width: 5px;
      height: 40px;
      cursor: col-resize;

      > div {
        cursor: col-resize;
      }
    }

    &.ProseMirror-bresizer {
      bottom: 0.25rem;
      width: 40px;
      height: 5px;
      cursor: row-resize;

      > div {
        cursor: row-resize;
      }
    }
  }

  &:hover {
    .ProseMirror-lresizer,
    .ProseMirror-rresizer,
    .ProseMirror-bresizer {
      opacity: 1;
    }
  }

  .ProseMirror[contenteditable="false"] & {
    .ProseMirror-lresizer,
    .ProseMirror-rresizer,
    .ProseMirror-bresizer {
      display: none !important;
    }
  }
}

// endregion

// region print

@media print {
  .ProseMirror-fm,
  .ProseMirror-bm,
  .ProseMirror-cm {
    display: none;
  }
}

// endregion
