import { h, createApp, type Ref, type Slot } from 'vue'

import type { DanmuItem, DanmakuItem } from '@/types/danmaku.types'
import logger from '@/utils/log'

/**
 * 自定义弹幕
 */
type CustomDanmu = {
  [key: string]: any
}

/**
 * 弹幕类型
 */
type Danmu = string | CustomDanmu

export function useDanmakuInsert(
  danmuList: Ref<Danmu[]>,
  index: Ref<number>,
  containerWidth: Ref<number>,
  containerHeight: Ref<number>,
  dmContainer: Ref<HTMLDivElement>,
  danChannel: Record<string, any>,
  danmu: DanmuItem,
  danmaku: DanmakuItem,
  calcChannels: Ref<number>,
  emit: any,
  extraStyle: string,
  dmSlot?: Slot,
) {
  /**
   * 绘制弹幕
   */
  function draw() {
    if (danmuList.value.length) {
      if (index.value > danmuList.value.length - 1) {
        const screenDanmus = dmContainer.value.children.length

        if (danmaku.loop) {
          if (screenDanmus < index.value) {
            // 一轮弹幕插入完毕
            emit('list-end')
            index.value = 0
          }
          insert()
        }
      } else {
        insert()
      }
    }
  }

  /**
   * 插入弹幕（也暴露到外部，允许外部直接执行绘制弹幕方法）
   * @param {Object} dm 外部定义的弹幕
   */
  function insert(data?: any) {
    try {
      // 如果传入的是弹幕数据对象
      if (data && data.content) {
        let content = data.content
        // 如果content是字符串，尝试解析为JSON对象
        if (typeof content === 'string') {
          try {
            content = JSON.parse(content)
          } catch (e) {
            // 解析失败，保持原始字符串
            logger.warn('弹幕JSON解析失败，使用原始文本:', e)
          }
        }

        // 将处理后的内容添加到弹幕列表
        return add({
          id: data.commentId || Date.now().toString(),
          content: content,
        })
      }

      // 如果没有传入数据或传入的是简单弹幕，使用原始逻辑
      const _index = danmaku.loop ? index.value % danmuList.value.length : index.value
      const _danmu = data || danmuList.value[_index]
      let el = document.createElement(`div`)

      if (danmaku.useSlot && dmSlot) {
        el = getSlotComponent(_danmu, _index).$el
      } else {
        el.innerHTML = _danmu as string
        el.setAttribute('style', extraStyle)
        el.style.fontSize = `${danmu.fontSize}px`
        el.style.lineHeight = `3rem`
      }
      el.classList.add('dm')
      dmContainer.value.appendChild(el)
      el.style.opacity = '0'

      const offsetHeight = el.offsetHeight
      const offsetWidth = el.offsetWidth
      if (!danmu.height) {
        danmu.height = 48 // 强制使用 3rem 高度
      }
      // 如果没有设置轨道数，则在获取到所有高度后计算出最大轨道数
      if (!danmaku.channels) {
        calcChannels.value = Math.floor(containerHeight.value / (danmu.height + danmu.top))
      }
      let channelIndex = getChannelIndex(el)
      if (channelIndex >= 0) {
        const height = danmu.height
        const computedChannelIndex = () => {
          const top = channelIndex * (height + danmu.top) + offsetHeight
          if (top >= containerHeight.value) {
            channelIndex--
            computedChannelIndex()
          }
        }
        computedChannelIndex()
        logger.debug('danmaku height top: ', height, danmu.top)
        el.classList.add('move')
        el.dataset.index = `${_index}`
        el.dataset.channel = channelIndex.toString()
        el.style.opacity = '1'
        const top = channelIndex * (height + danmu.top) + 'px'
        el.style.top = top
        el.style.left = `${containerWidth.value}px`
        el.style.animationDuration = `${containerWidth.value / danmu.speeds}s`
        el.addEventListener('animationend', () => {
          if (Number(el.dataset.index) === danmuList.value.length - 1 && !danmaku.loop) {
            emit('play-end', el.dataset.index)
          }
          dmContainer.value && dmContainer.value.removeChild(el)
        })
        index.value++
        el.style.width = offsetWidth + danmu.right + 'px'
        el.style.setProperty('--dm-scroll-width', `-${containerWidth.value + offsetWidth}px`)
      } else {
        dmContainer.value.removeChild(el)
      }
    } catch (error) {
      logger.error('添加弹幕时发生错误:', error)
    }
  }

  function getSlotComponent(_danmu: any, _index: number) {
    const DmComponent = createApp({
      render() {
        return h('div', {}, [
          dmSlot &&
            dmSlot({
              danmu: _danmu,
              index: _index,
            }),
        ])
      },
    })

    const ele = DmComponent.mount(document.createElement('div'))
    return ele
  }

  function getChannelIndex(el: HTMLDivElement): number {
    let _channels = [...Array(danmaku.channels).keys()]

    if (danmaku.randomChannel) {
      _channels = _channels.sort(() => 0.5 - Math.random())
    }
    for (const i of _channels) {
      const items = danChannel[i]

      if (items && items.length) {
        for (let j = 0; j < items.length; j++) {
          const danRight = getDanRight(items[j]) - 10
          // 安全距离判断
          if (danRight <= (el.offsetWidth - items[j].offsetWidth) * 0.75 || danRight <= 0) {
            break
          }
          if (j === items.length - 1) {
            danChannel[i].push(el)
            el.addEventListener('animationend', () => danChannel[i].splice(0, 1))
            return i % danmaku.channels
          }
        }
      } else {
        danChannel[i] = [el]
        el.addEventListener('animationend', () => danChannel[i].splice(0, 1))
        return i % danmaku.channels
      }
    }
    return -1
  }

  /**
   * 获取弹幕右侧到屏幕右侧的距离
   */
  function getDanRight(el: HTMLDivElement) {
    const eleWidth = el.offsetWidth || parseInt(el.style.width)
    const eleRight =
      el.getBoundingClientRect().right || dmContainer.value.getBoundingClientRect().right + eleWidth
    return dmContainer.value.getBoundingClientRect().right - eleRight
  }

  /**
   * 添加弹幕（插入到当前播放的弹幕位置）
   */
  function add(danmu: Danmu): number {
    if (index.value === danmuList.value.length) {
      // 如果当前弹幕已经播放完了，那么仍然走 push
      danmuList.value.push(danmu)
      return danmuList.value.length - 1
    } else {
      const _index = index.value % danmuList.value.length
      danmuList.value.splice(_index, 0, danmu)
      return _index + 1
    }
  }

  /**
   * 添加弹幕（插入到弹幕末尾）
   */
  function push(danmu: Danmu): number {
    danmuList.value.push(danmu)
    return danmuList.value.length - 1
  }

  return {
    draw,
    insert,
    add,
    push,
    getChannelIndex,
    getDanRight,
    getSlotComponent,
  }
}
