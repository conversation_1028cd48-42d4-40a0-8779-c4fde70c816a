<template>
  <div class="editor-test">
    <h1>编辑器测试页面</h1>
    <div class="editor-container">
      <div ref="editorRef" class="editor"></div>
    </div>
    <div class="test-info">
      <h3>测试说明：</h3>
      <ul>
        <li>输入 "123" 应该保持为普通文本，不会自动转换为标题</li>
        <li>输入 "# 标题" 也不会自动转换（已禁用 Markdown 自动转换）</li>
        <li>点击菜单功能暂时禁用（正在修复中）</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'
import { Editor } from '@tiptap/vue-3'
import { createEditor } from '@/components/tiptap/core/editor/EditorCore'

const editorRef = ref<HTMLElement>()
let editor: Editor | undefined

const props = {
  placeholder: '请输入内容...',
  characterLimit: 1000,
  useThumbnail: false,
  extensions: ['bold', 'italic', 'heading', 'paragraph'],
  allExtensions: false,
  modelValue: {},
  editable: true,
  editorProps: {}
}

const emit = (event: string, content: any) => {
  console.log('Editor content updated:', content)
}

onMounted(() => {
  if (editorRef.value) {
    const editorInstance = createEditor(props, emit)
    editorInstance.initEditor()
    editor = editorInstance.editor.value
    
    if (editor && editorRef.value) {
      editorRef.value.appendChild(editor.options.element)
    }
  }
})

onUnmounted(() => {
  if (editor) {
    editor.destroy()
  }
})
</script>

<style scoped>
.editor-test {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.editor-container {
  border: 1px solid #ddd;
  border-radius: 8px;
  padding: 16px;
  margin: 20px 0;
  min-height: 200px;
}

.editor {
  outline: none;
}

.test-info {
  background: #f5f5f5;
  padding: 16px;
  border-radius: 8px;
  margin-top: 20px;
}

.test-info h3 {
  margin-top: 0;
}

.test-info ul {
  margin-bottom: 0;
}
</style>