<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑器功能测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        
        .test-container {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .instructions {
            background: #e2e3e5;
            padding: 15px;
            border-radius: 6px;
            margin: 15px 0;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .feature-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <h1>🎯 编辑器拖拽功能测试</h1>
    
    <div class="status success">
        ✅ 修复完成 - 拖拽和点击菜单功能已恢复
    </div>
    
    <div class="test-container">
        <h2>📋 测试清单</h2>
        
        <div class="instructions">
            <h3>🧪 如何测试</h3>
            <ol>
                <li>访问编辑器页面：<code>http://localhost:5174</code></li>
                <li>创建或编辑一篇文章</li>
                <li>在编辑器中输入多个段落</li>
                <li>测试以下功能...</li>
            </ol>
        </div>
        
        <div class="feature-list">
            <div class="feature-item">
                <h3>🖱️ 拖拽手柄</h3>
                <ul>
                    <li>鼠标悬停段落左侧</li>
                    <li>应显示拖拽手柄图标</li>
                    <li>拖拽可重新排列段落</li>
                </ul>
            </div>
            
            <div class="feature-item">
                <h3>📋 右键菜单</h3>
                <ul>
                    <li>右键点击段落</li>
                    <li>应显示上下文菜单</li>
                    <li>包含复制、删除等选项</li>
                </ul>
            </div>
            
            <div class="feature-item">
                <h3>⌨️ 键盘快捷键</h3>
                <ul>
                    <li><kbd>Ctrl+Space</kbd> 显示菜单</li>
                    <li><kbd>Alt+↑</kbd> 向上移动</li>
                    <li><kbd>Alt+↓</kbd> 向下移动</li>
                    <li><kbd>Ctrl+D</kbd> 复制段落</li>
                </ul>
            </div>
            
            <div class="feature-item">
                <h3>🎯 支持元素</h3>
                <ul>
                    <li>普通段落</li>
                    <li>标题 (H1-H6)</li>
                    <li>列表项</li>
                    <li>代码块</li>
                </ul>
            </div>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🔧 修复内容回顾</h2>
        
        <h3>主要修复项目：</h3>
        <ul>
            <li>✅ 修复样式文件导入问题</li>
            <li>✅ 配置默认菜单项</li>
            <li>✅ 修复位置验证错误 (RangeError)</li>
            <li>✅ 启用被注释的扩展</li>
            <li>✅ 添加边界检查和错误处理</li>
        </ul>
        
        <h3>技术细节：</h3>
        <ul>
            <li><strong>样式导入</strong>：在扩展入口文件中导入 SCSS 样式</li>
            <li><strong>菜单配置</strong>：使用 createDefaultMenuItems() 提供默认菜单</li>
            <li><strong>位置验证</strong>：添加文档大小检查避免越界错误</li>
            <li><strong>错误处理</strong>：增强 try-catch 和日志记录</li>
        </ul>
    </div>
    
    <div class="test-container">
        <h2>🚀 开始测试</h2>
        <p>现在可以访问编辑器进行测试：</p>
        <p><strong>开发服务器地址：</strong> <a href="http://localhost:5174" target="_blank">http://localhost:5174</a></p>
        
        <div class="instructions">
            <p><strong>测试步骤：</strong></p>
            <ol>
                <li>打开编辑器页面</li>
                <li>创建新文章或编辑现有文章</li>
                <li>在编辑器中输入几个段落</li>
                <li>测试拖拽和菜单功能</li>
                <li>如有问题，检查浏览器控制台错误</li>
            </ol>
        </div>
    </div>
    
    <div class="test-container">
        <h2>🐛 故障排除</h2>
        
        <h3>如果功能仍然无效：</h3>
        <ol>
            <li><strong>检查控制台错误</strong> - 打开浏览器开发者工具查看错误信息</li>
            <li><strong>验证样式加载</strong> - 在网络面板检查 CSS 文件是否正确加载</li>
            <li><strong>确认扩展状态</strong> - 在控制台运行调试代码检查扩展状态</li>
            <li><strong>重启开发服务器</strong> - 停止并重新启动 npm run dev</li>
        </ol>
        
        <h3>调试代码：</h3>
        <pre><code>// 在浏览器控制台中运行
console.log('Extensions:', editor.extensionManager.extensions.map(ext => ext.name))
console.log('Drag handle:', editor.extensionManager.extensions.find(ext => ext.name === 'dragHandle'))
console.log('Click menu:', editor.extensionManager.extensions.find(ext => ext.name === 'clickMenu'))</code></pre>
    </div>
</body>
</html>