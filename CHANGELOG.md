# 更新日志 (Changelog)

本文档记录了 Wen 项目的所有重要更改。

格式基于 [Keep a Changelog](https://keepachangelog.com/zh-CN/1.0.0/)，
并且本项目遵循 [语义化版本](https://semver.org/lang/zh-CN/)。

## [未发布]

### 新增
- 完整的拖拽重排功能实现
- 智能上下文菜单系统
- 全面的键盘无障碍支持
- 屏幕阅读器完整支持

### 更改
- 优化了拖拽操作的性能
- 改进了菜单定位算法
- 增强了视觉反馈效果

### 修复
- 修复了拖拽操作的边界情况
- 解决了菜单在小屏幕上的显示问题
- 修复了键盘导航的焦点问题

## [1.0.0] - 2024-12-19

### 新增

#### 🎯 核心功能
- **TipTap 拖拽扩展**: 完整的段落拖拽重排功能
  - 支持段落、标题、列表项、代码块、引用块的拖拽
  - 实时视觉反馈和拖拽预览
  - 智能拖放区域检测和验证
  - 平滑的动画过渡效果

- **点击菜单扩展**: 上下文感知的操作菜单
  - 智能菜单定位，自动避免视窗边界
  - 分组管理的菜单项系统
  - 动态菜单项过滤和可见性控制
  - 丰富的内容转换和格式化选项

#### ♿ 无障碍功能
- **增强键盘快捷键系统**:
  - `Alt + 1-6`: 直接转换为对应级别标题
  - `Alt + h/p/l/c/q`: 快速内容类型转换
  - `Alt + d`: 复制当前内容
  - `Alt + Delete`: 删除当前内容
  - `Alt + m`: 替代菜单激活快捷键
  - `Alt + Shift + ↑/↓`: 移动到文档顶部/底部

- **屏幕阅读器支持**:
  - 上下文感知的语音提示
  - 详细的操作反馈和状态播报
  - 位置信息播报（如"第2项，共5项"）
  - 内置键盘帮助系统

- **ARIA 无障碍属性**:
  - 完整的语义化标记
  - 动态焦点管理
  - 增强的导航提示
  - 分组感知的菜单导航

#### 🔧 技术实现
- **内容转换工具类**: 
  - 支持所有主要内容类型的相互转换
  - 批量转换操作支持
  - 智能转换可行性检测
  - 节点属性管理和操作

- **菜单项注册系统**:
  - 动态菜单项注册和管理
  - 分组和优先级支持
  - 上下文过滤和可见性控制
  - 可扩展的菜单项架构

- **键盘无障碍工具**:
  - 统一的键盘事件处理
  - 智能焦点管理
  - 屏幕阅读器集成
  - 用户偏好检测（如减少动画）

#### 📱 用户体验
- **视觉反馈系统**:
  - 拖拽过程中的实时预览
  - 拖放区域高亮显示
  - 平滑的动画过渡
  - 操作状态指示器

- **智能菜单定位**:
  - 自动视窗边界检测
  - 多种定位策略支持
  - 响应式设计适配
  - 移动设备优化

#### 🧪 测试和质量保证
- **全面的测试覆盖**:
  - 单元测试覆盖所有核心功能
  - 集成测试验证扩展兼容性
  - E2E 测试覆盖用户工作流
  - 无障碍专项测试

- **性能优化**:
  - 事件节流和防抖处理
  - 内存泄漏预防
  - 大文档性能优化
  - 懒加载和按需渲染

#### 📚 文档和开发体验
- **完整的文档系统**:
  - 详细的 API 文档
  - 无障碍使用指南
  - 开发者集成指南
  - 故障排除手册

- **开发工具支持**:
  - TypeScript 类型定义
  - ESLint 规则配置
  - 调试工具和日志
  - 开发模式支持

### 技术细节

#### 架构改进
- 采用模块化扩展架构
- 实现了插件式菜单系统
- 优化了事件处理机制
- 改进了状态管理模式

#### 性能优化
- 实现了事件节流机制
- 优化了 DOM 操作性能
- 减少了内存占用
- 改进了渲染效率

#### 兼容性
- 支持现代浏览器
- 移动设备适配
- 屏幕阅读器兼容
- 键盘导航支持

### 已知问题
- 在某些移动浏览器上拖拽体验可能不够流畅
- 复杂嵌套结构的拖拽可能需要额外处理
- 部分屏幕阅读器的兼容性仍在优化中

### 迁移指南
如果您正在从早期版本升级，请注意：

1. **API 变更**: 部分扩展配置选项已更新
2. **依赖更新**: 确保 TipTap 版本兼容
3. **样式调整**: 可能需要更新自定义样式
4. **测试更新**: 建议重新运行所有测试

### 贡献者
感谢所有为此版本做出贡献的开发者和测试人员。

---

## 版本说明

### 版本号规则
- **主版本号**: 不兼容的 API 修改
- **次版本号**: 向下兼容的功能性新增
- **修订号**: 向下兼容的问题修正

### 发布周期
- **主版本**: 根据重大功能发布
- **次版本**: 每月发布
- **修订版**: 根据需要发布

### 支持政策
- **当前版本**: 完全支持
- **前一个主版本**: 安全更新和重要修复
- **更早版本**: 不再维护

---

*更多信息请查看 [项目文档](web/docs/) 或 [GitHub Issues](../../issues)*