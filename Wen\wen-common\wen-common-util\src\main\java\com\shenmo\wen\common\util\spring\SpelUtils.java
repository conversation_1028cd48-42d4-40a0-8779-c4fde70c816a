package com.shenmo.wen.common.util.spring;

import com.shenmo.wen.common.constant.StringConstant;
import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.expression.BeanFactoryResolver;
import org.springframework.context.expression.MethodBasedEvaluationContext;
import org.springframework.core.DefaultParameterNameDiscoverer;
import org.springframework.core.ParameterNameDiscoverer;
import org.springframework.expression.Expression;
import org.springframework.expression.ExpressionParser;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Objects;
import java.util.Optional;

/**
 * Spel工具类
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Slf4j
public abstract class SpelUtils {

    /**
     * 参数发现器
     */
    private static final ParameterNameDiscoverer NAME_DISCOVERER = new DefaultParameterNameDiscoverer();

    /**
     * Express语法解析器
     */
    private static final ExpressionParser PARSER = new SpelExpressionParser();

    /**
     * 获取方法参数值
     *
     * @param elValue el表达式的值
     * @param target  目标对象
     * @param method  方法对象
     * @param args    方法参数数组
     * @return 方法参数值
     * <AUTHOR>
     */
    @NonNull
    public static Object getMethodArgValue(String elValue, Object target, Method method, Object[] args) {

        if (Objects.isNull(elValue)) {
            return "";
        }
        ExpressionRootObject rootObject = new ExpressionRootObject(method, args, target);
        StandardEvaluationContext context = new MethodBasedEvaluationContext(rootObject, method, args, NAME_DISCOVERER);
        context.setBeanResolver(new BeanFactoryResolver(SpringAwareUtils.beanFactory()));
        return Optional.ofNullable(getMethodArgExpression(elValue, args))
                .map(e -> e.getValue(context))
                .orElse(elValue);
    }

    /**
     * 获取方法参数表达式
     *
     * @param elValue el表达式的值
     * @param args    方法参数数组
     * @return 方法参数表达式
     * <AUTHOR>
     */
    @Nullable
    public static Expression getMethodArgExpression(String elValue, Object[] args) {

        Expression expression = null;
        if (elValue.contains(StringConstant.HASH)) {
            try {
                expression = PARSER.parseExpression(elValue);
            } catch (Exception ignored) {
                log.warn("SPEL no data was parsed, expression: {}, args: {}", elValue, Arrays.toString(args));
            }
        }

        return expression;
    }

    /**
     * Spel表达式根对象
     *
     * <AUTHOR>
     * @version 1.0.0
     */
    @Getter
    @RequiredArgsConstructor
    public static class ExpressionRootObject {

        /**
         * 目标方法
         */
        private final Method method;

        /**
         * 目标方法参数列表
         */
        private final Object[] args;

        /**
         * 目标对象
         */
        private final Object target;
    }

}
