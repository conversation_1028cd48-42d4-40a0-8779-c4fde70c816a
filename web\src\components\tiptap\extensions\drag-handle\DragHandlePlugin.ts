import { <PERSON>lug<PERSON>, <PERSON>lug<PERSON><PERSON><PERSON> } from '@tiptap/pm/state'
import { EditorView } from '@tiptap/pm/view'
import type { Editor } from '@tiptap/core'
import type { DragHandleOptions, DragOperation, DropZone } from './types'
import { KeyboardAccessibilityUtils } from '../shared/utils/keyboardAccessibility'
import * as PerformanceUtils from '../shared/utils/performanceOptimizations'
import { PerformanceMonitoring } from '../shared/utils/performanceMonitoring'
import { PerformanceConfigManager } from '../shared/config/performanceConfig'

export interface DragHandlePluginOptions extends DragHandleOptions {
  editor: Editor
}

class DragHandlePluginView {
  private editor: Editor
  private options: DragHandlePluginOptions
  private view: EditorView
  private dragHandles: Map<HTMLElement, HTMLElement> = new Map()
  private currentDragOperation: DragOperation | null = null
  private dropZones: DropZone[] = []

  constructor(view: EditorView, options: DragHandlePluginOptions) {
    this.view = view
    this.editor = options.editor
    this.options = options
    
    this.init()
  }

  private init() {
    // Add drag handles to supported nodes
    this.updateDragHandles()
    
    // Get performance configuration
    const config = PerformanceConfigManager.getConfig()
    
    // Listen for editor updates to refresh drag handles with optimized throttling
    const throttledUpdate = PerformanceUtils.throttle(() => {
      const endMeasure = PerformanceMonitoring.Profiler.start('dragHandle-update')
      try {
        this.updateDragHandles()
        endMeasure(true)
      } catch (error) {
        endMeasure(false)
        console.error('Error updating drag handles:', error)
      }
    }, config.throttling.updateEvents)
    
    this.editor.on('update', throttledUpdate)
    
    // Track this for cleanup
    PerformanceUtils.MemoryManager.trackEventListener(this, () => {
      this.editor.off('update', throttledUpdate)
    })

    // Set up periodic cleanup for memory management
    const cleanupInterval = setInterval(() => {
      this.performPeriodicCleanup()
    }, config.memoryManagement.cleanupInterval)
    
    PerformanceUtils.MemoryManager.trackTimer(this, cleanupInterval)
  }

  private updateDragHandles() {
    // Use DOM batcher for efficient DOM operations
    const batcher = new PerformanceUtils.DOMBatcher()
    
    // Clear existing handles
    batcher.add(() => this.clearDragHandles())
    
    // Find all supported nodes and add drag handles
    const { dom } = this.view
    const supportedSelectors = this.options.supportedNodes.map(node => {
      const config = this.options.nodeTypeConfigs[node]
      return config ? config.selector : this.getDefaultSelector(node)
    }).join(', ')

    const elements = dom.querySelectorAll(supportedSelectors)
    
    // Use virtual scrolling for large documents
    const config = PerformanceConfigManager.getConfig()
    if (config.virtualScrolling.enabled && elements.length >= config.virtualScrolling.threshold) {
      this.updateDragHandlesVirtual(elements, batcher)
    } else {
      elements.forEach((element) => {
        if (element instanceof HTMLElement && this.shouldAddDragHandle(element)) {
          batcher.add(() => this.addDragHandle(element))
        }
      })
    }
    
    // Execute all batched operations
    batcher.flushSync()
  }

  private getDefaultSelector(nodeType: string): string {
    switch (nodeType) {
      case 'paragraph': return 'p'
      case 'heading': return 'h1, h2, h3, h4, h5, h6'
      case 'listItem': return 'li'
      case 'codeBlock': return '.code-block, pre'
      case 'blockquote': return 'blockquote'
      case 'bulletList': return 'ul'
      case 'orderedList': return 'ol'
      default: return `.${nodeType}`
    }
  }

  private shouldAddDragHandle(element: HTMLElement): boolean {
    // Don't add drag handles to nested elements that already have one
    if (element.querySelector('.drag-handle')) return false
    
    // Don't add to elements that are inside other draggable elements
    const parent = element.closest('[data-drag-handle]')
    if (parent && parent !== element) return false
    
    // Check if element is visible and has content
    const rect = element.getBoundingClientRect()
    if (rect.width === 0 || rect.height === 0) return false
    
    return true
  }

  private addDragHandle(element: HTMLElement) {
    // Skip if already has drag handle
    if (this.dragHandles.has(element)) return

    // Determine node type from element
    const nodeType = this.getNodeTypeFromElement(element)
    if (!nodeType) return

    const handle = document.createElement('div')
    handle.className = `drag-handle drag-handle--${nodeType}`
    handle.draggable = true
    handle.setAttribute('data-drag-handle', 'true')
    handle.setAttribute('data-node-type', nodeType)
    
    // Add appropriate icon based on node type
    const icon = this.getIconForNodeType(nodeType)
    handle.innerHTML = `
      <div class="drag-handle__icon">
        ${icon}
      </div>
    `

    // Position the handle appropriately for different node types
    this.positionDragHandle(element, handle, nodeType)
    
    // Store the relationship
    this.dragHandles.set(element, handle)

    // Add event listeners
    handle.addEventListener('dragstart', (e) => this.handleDragStart(e, element))
    handle.addEventListener('dragend', (e) => this.handleDragEnd(e))
    
    // Add accessibility attributes
    this.addAccessibilityAttributes(handle, nodeType)
  }

  private getNodeTypeFromElement(element: HTMLElement): string | null {
    // Check each node type configuration to find matching selector
    for (const [nodeType, config] of Object.entries(this.options.nodeTypeConfigs)) {
      const selectors = config.selector.split(',').map(s => s.trim())
      if (selectors.some(selector => element.matches(selector))) {
        return nodeType
      }
    }
    return null
  }

  private getIconForNodeType(nodeType: string): string {
    const icons = {
      paragraph: `
        <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
          <circle cx="3" cy="3" r="1"/>
          <circle cx="9" cy="3" r="1"/>
          <circle cx="3" cy="6" r="1"/>
          <circle cx="9" cy="6" r="1"/>
          <circle cx="3" cy="9" r="1"/>
          <circle cx="9" cy="9" r="1"/>
        </svg>
      `,
      heading: `
        <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
          <rect x="2" y="2" width="8" height="2" rx="1"/>
          <rect x="2" y="5" width="6" height="2" rx="1"/>
          <rect x="2" y="8" width="4" height="2" rx="1"/>
        </svg>
      `,
      listItem: `
        <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
          <circle cx="3" cy="3" r="1"/>
          <rect x="5" y="2" width="5" height="2" rx="1"/>
          <circle cx="3" cy="6" r="1"/>
          <rect x="5" y="5" width="5" height="2" rx="1"/>
          <circle cx="3" cy="9" r="1"/>
          <rect x="5" y="8" width="5" height="2" rx="1"/>
        </svg>
      `,
      codeBlock: `
        <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
          <path d="M3 4L1 6L3 8M9 4L11 6L9 8M7 2L5 10"/>
        </svg>
      `,
      blockquote: `
        <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
          <path d="M2 3V7H4L5 9V7H6V3H2ZM7 3V7H9L10 9V7H11V3H7Z"/>
        </svg>
      `,
      bulletList: `
        <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
          <circle cx="2" cy="3" r="1"/>
          <rect x="4" y="2" width="6" height="2" rx="1"/>
          <circle cx="2" cy="6" r="1"/>
          <rect x="4" y="5" width="6" height="2" rx="1"/>
          <circle cx="2" cy="9" r="1"/>
          <rect x="4" y="8" width="6" height="2" rx="1"/>
        </svg>
      `,
      orderedList: `
        <svg width="12" height="12" viewBox="0 0 12 12" fill="currentColor">
          <text x="2" y="5" font-size="4" font-weight="bold">1</text>
          <rect x="4" y="2" width="6" height="2" rx="1"/>
          <text x="2" y="8" font-size="4" font-weight="bold">2</text>
          <rect x="4" y="5" width="6" height="2" rx="1"/>
        </svg>
      `
    }
    
    return icons[nodeType as keyof typeof icons] || icons.paragraph
  }

  private positionDragHandle(element: HTMLElement, handle: HTMLElement, nodeType: string) {
    element.style.position = 'relative'
    
    // Different positioning strategies for different node types
    switch (nodeType) {
      case 'listItem':
        // Position handle to the left of list marker
        handle.style.cssText = `
          position: absolute;
          left: -24px;
          top: 2px;
          z-index: 10;
        `
        break
        
      case 'heading':
        // Position handle slightly higher for headings
        handle.style.cssText = `
          position: absolute;
          left: -20px;
          top: 0;
          z-index: 10;
        `
        break
        
      case 'codeBlock':
        // Position handle at top-left corner for code blocks
        handle.style.cssText = `
          position: absolute;
          left: -20px;
          top: 4px;
          z-index: 10;
        `
        break
        
      case 'blockquote':
        // Position handle outside the quote border
        handle.style.cssText = `
          position: absolute;
          left: -24px;
          top: 4px;
          z-index: 10;
        `
        break
        
      case 'bulletList':
      case 'orderedList':
        // Position handle for entire list
        handle.style.cssText = `
          position: absolute;
          left: -20px;
          top: 2px;
          z-index: 10;
        `
        break
        
      default:
        // Default positioning for paragraphs and other elements
        handle.style.cssText = `
          position: absolute;
          left: -20px;
          top: 2px;
          z-index: 10;
        `
    }
    
    element.appendChild(handle)
  }

  /**
   * Update drag handles using virtual scrolling for large documents
   */
  private updateDragHandlesVirtual(elements: NodeListOf<Element>, batcher: PerformanceUtils.DOMBatcher) {
    const config = PerformanceConfigManager.getConfig()
    const containerRect = this.view.dom.getBoundingClientRect()
    const scrollTop = this.view.dom.scrollTop
    const containerHeight = containerRect.height
    
    // Calculate visible range
    const virtualManager = new PerformanceUtils.VirtualScrollManager(
      this.view.dom,
      config.virtualScrolling.itemHeight
    )
    virtualManager.setTotalItems(elements.length)
    
    const visibleRange = virtualManager.calculateVisibleRange(
      scrollTop,
      containerHeight,
      config.virtualScrolling.bufferSize
    )
    
    // Only process visible elements
    for (let i = visibleRange.start; i <= visibleRange.end && i < elements.length; i++) {
      const element = elements[i]
      if (element instanceof HTMLElement && this.shouldAddDragHandle(element)) {
        batcher.add(() => this.addDragHandle(element))
      }
    }
  }

  /**
   * Perform periodic cleanup to prevent memory leaks
   */
  private performPeriodicCleanup() {
    const config = PerformanceConfigManager.getConfig()
    
    // Clean up orphaned drag handles
    const orphanedHandles = Array.from(document.querySelectorAll('.drag-handle')).filter(handle => {
      const parent = handle.parentElement
      return !parent || !this.view.dom.contains(parent)
    })
    
    orphanedHandles.forEach(handle => {
      if (handle.parentNode) {
        handle.parentNode.removeChild(handle)
      }
    })
    
    // Clean up drag handles map
    const toRemove: HTMLElement[] = []
    this.dragHandles.forEach((handle, element) => {
      if (!this.view.dom.contains(element) || !element.parentNode) {
        toRemove.push(element)
      }
    })
    
    toRemove.forEach(element => {
      const handle = this.dragHandles.get(element)
      if (handle && handle.parentNode) {
        handle.parentNode.removeChild(handle)
      }
      this.dragHandles.delete(element)
    })
    
    // Trigger garbage collection hint if available
    if (toRemove.length > config.memoryManagement.gcThreshold && 'gc' in window) {
      (window as any).gc()
    }
  }

  private addAccessibilityAttributes(handle: HTMLElement, nodeType: string) {
    // Calculate position information for better accessibility
    const allHandles = Array.from(document.querySelectorAll('.drag-handle'))
    const position = allHandles.indexOf(handle)
    const totalNodes = allHandles.length

    // Use the enhanced accessibility utility
    KeyboardAccessibilityUtils.addEnhancedDragHandleAria(handle, nodeType, position, totalNodes)
    
    // Add keyboard event listener for drag handle activation
    handle.addEventListener('keydown', (event) => {
      if (event.key === 'Enter' || event.key === ' ') {
        event.preventDefault()
        // Focus on the element for keyboard navigation
        const parentElement = handle.parentElement
        if (parentElement) {
          parentElement.focus()
          KeyboardAccessibilityUtils.announceDragOperation('start', nodeType)
          
          // Announce keyboard help
          setTimeout(() => {
            KeyboardAccessibilityUtils.announceKeyboardHelp('drag')
          }, 1000)
        }
      } else if (event.key === 'F1' || (event.key === '?' && !event.shiftKey)) {
        // Help shortcut
        event.preventDefault()
        KeyboardAccessibilityUtils.announceKeyboardHelp('drag')
      } else {
        // Handle other keyboard shortcuts using enhanced handler
        const parentElement = handle.parentElement
        if (parentElement) {
          const pos = this.view.posAtDOM(parentElement, 0)
          const node = this.view.state.doc.nodeAt(pos || 0)
          if (node && pos !== null) {
            const handled = KeyboardAccessibilityUtils.handleEnhancedDragKeyboard(
              this.editor, event, node, pos
            )
            if (handled) {
              event.preventDefault()
              
              // Update drag state for screen readers
              if (event.altKey && (event.key === 'ArrowUp' || event.key === 'ArrowDown')) {
                handle.setAttribute('aria-grabbed', 'true')
                setTimeout(() => {
                  handle.setAttribute('aria-grabbed', 'false')
                }, 500)
              }
            }
          }
        }
      }
    })
    
    // Enhanced focus and blur event listeners
    handle.addEventListener('focus', () => {
      KeyboardAccessibilityUtils.announceWithContext(
        '拖拽手柄已获得焦点',
        {
          nodeType,
          position,
          totalItems: totalNodes,
          operation: '焦点'
        }
      )
      
      // Add visual focus indicator
      handle.classList.add('keyboard-focused')
    })
    
    handle.addEventListener('blur', () => {
      // Remove visual focus indicator
      handle.classList.remove('keyboard-focused')
      
      // Reset drag state
      handle.setAttribute('aria-grabbed', 'false')
    })

    // Add mouse interaction announcements
    handle.addEventListener('mouseenter', () => {
      if (document.activeElement !== handle) {
        KeyboardAccessibilityUtils.announceWithContext(
          '拖拽手柄可用',
          {
            nodeType,
            operation: '鼠标悬停'
          }
        )
      }
    })
  }

  private clearDragHandles() {
    this.dragHandles.forEach((handle, element) => {
      if (handle.parentNode) {
        handle.parentNode.removeChild(handle)
      }
    })
    this.dragHandles.clear()
  }

  private handleDragStart(event: DragEvent, sourceElement: HTMLElement) {
    const endMeasure = PerformanceMonitoring.Profiler.start('dragHandle-dragStart')
    
    try {
      if (!event.dataTransfer) return

      // Find the ProseMirror position
      const pos = this.view.posAtDOM(sourceElement, 0)
      if (pos === null || pos === undefined) return

      const node = this.view.state.doc.nodeAt(pos)
      if (!node) return

      // Create ghost image with RAF throttling for smooth animation
      const createGhostImageThrottled = PerformanceUtils.rafThrottle(() => {
        const ghostImage = this.createGhostImage(sourceElement)
        event.dataTransfer!.setDragImage(ghostImage, 0, 0)
        return ghostImage
      })
      
      const ghostImage = createGhostImageThrottled()
      event.dataTransfer.effectAllowed = 'move'

      // Store drag operation
      this.currentDragOperation = {
        sourceNode: node,
        sourcePos: pos,
        targetPos: null,
        dragElement: sourceElement,
        ghostImage,
        isActive: true,
        nodeType: node.type.name,
        preservedAttributes: {},
        nestingContext: {
          sourceParent: null,
          sourceDepth: 0
        }
      }

      // Update editor storage
      this.editor.storage.dragHandle.isDragging = true
      this.editor.storage.dragHandle.currentDragElement = sourceElement
      this.editor.storage.dragHandle.dragState = this.currentDragOperation

      // Show drop zones with throttling
      const showDropZonesThrottled = PerformanceUtils.throttle(() => {
        this.showDropZones()
      }, PerformanceConfigManager.getConfig().throttling.dragEvents)
      
      showDropZonesThrottled()

      // Add visual feedback
      sourceElement.classList.add('dragging')
      
      endMeasure(true)
    } catch (error) {
      endMeasure(false)
      console.error('Error in drag start:', error)
    }
  }

  private handleDragEnd(event: DragEvent) {
    const endMeasure = PerformanceMonitoring.Profiler.start('dragHandle-dragEnd')
    
    try {
      if (!this.currentDragOperation) return

      // Use DOM batcher for efficient cleanup
      const batcher = new PerformanceUtils.DOMBatcher()
      
      // Clean up visual feedback
      batcher.add(() => {
        this.currentDragOperation!.dragElement.classList.remove('dragging')
      })
      
      batcher.add(() => {
        this.hideDropZones()
      })
      
      // Remove ghost image
      if (this.currentDragOperation.ghostImage && this.currentDragOperation.ghostImage.parentNode) {
        batcher.add(() => {
          this.currentDragOperation!.ghostImage!.parentNode!.removeChild(this.currentDragOperation!.ghostImage!)
        })
      }

      // Execute batched DOM operations
      batcher.flushSync()

      // Reset state
      this.currentDragOperation = null
      this.editor.storage.dragHandle.isDragging = false
      this.editor.storage.dragHandle.currentDragElement = null
      this.editor.storage.dragHandle.dragState = null
      
      endMeasure(true)
    } catch (error) {
      endMeasure(false)
      console.error('Error in drag end:', error)
    }
  }

  private createGhostImage(element: HTMLElement): HTMLElement {
    const ghost = element.cloneNode(true) as HTMLElement
    ghost.style.position = 'absolute'
    ghost.style.top = '-1000px'
    ghost.style.left = '-1000px'
    ghost.style.opacity = this.options.ghostImageOpacity.toString()
    ghost.style.pointerEvents = 'none'
    ghost.style.transform = 'rotate(5deg)'
    ghost.style.boxShadow = '0 8px 32px rgba(0, 0, 0, 0.2)'
    ghost.style.borderRadius = '8px'
    ghost.style.maxWidth = '300px'
    
    document.body.appendChild(ghost)
    return ghost
  }

  private showDropZones() {
    const endMeasure = PerformanceMonitoring.Profiler.start('dragHandle-showDropZones')
    
    try {
      this.hideDropZones() // Clear existing zones
      
      const { dom } = this.view
      const supportedSelectors = this.options.supportedNodes.map(node => {
        const config = this.options.nodeTypeConfigs[node]
        return config ? config.selector : this.getDefaultSelector(node)
      }).join(', ')
      
      const allElements = dom.querySelectorAll(supportedSelectors)
      const config = PerformanceConfigManager.getConfig()
      
      // Use DOM batcher for efficient drop zone creation
      const batcher = new PerformanceUtils.DOMBatcher()
      
      // Use virtual scrolling for large documents
      if (config.virtualScrolling.enabled && allElements.length >= config.virtualScrolling.threshold) {
        this.showDropZonesVirtual(allElements, batcher)
      } else {
        allElements.forEach((element, index) => {
          if (element instanceof HTMLElement && 
              element !== this.currentDragOperation?.dragElement &&
              this.isValidDropTarget(element)) {
            batcher.add(() => {
              const dropZone = this.createDropZone(element, index)
              if (dropZone) {
                this.dropZones.push(dropZone)
              }
            })
          }
        })
      }
      
      // Execute batched operations
      batcher.flushSync()
      
      endMeasure(true)
    } catch (error) {
      endMeasure(false)
      console.error('Error showing drop zones:', error)
    }
  }

  /**
   * Show drop zones using virtual scrolling for large documents
   */
  private showDropZonesVirtual(elements: NodeListOf<Element>, batcher: PerformanceUtils.DOMBatcher) {
    const config = PerformanceConfigManager.getConfig()
    const containerRect = this.view.dom.getBoundingClientRect()
    const scrollTop = this.view.dom.scrollTop
    const containerHeight = containerRect.height
    
    // Calculate visible range
    const virtualManager = new PerformanceUtils.VirtualScrollManager(
      this.view.dom,
      config.virtualScrolling.itemHeight
    )
    virtualManager.setTotalItems(elements.length)
    
    const visibleRange = virtualManager.calculateVisibleRange(
      scrollTop,
      containerHeight,
      config.virtualScrolling.bufferSize
    )
    
    // Only create drop zones for visible elements
    for (let i = visibleRange.start; i <= visibleRange.end && i < elements.length; i++) {
      const element = elements[i]
      if (element instanceof HTMLElement && 
          element !== this.currentDragOperation?.dragElement &&
          this.isValidDropTarget(element)) {
        batcher.add(() => {
          const dropZone = this.createDropZone(element, i)
          if (dropZone) {
            this.dropZones.push(dropZone)
          }
        })
      }
    }
  }

  private isValidDropTarget(element: HTMLElement): boolean {
    if (!this.currentDragOperation) return false
    
    // Get node types
    const sourceNodeType = this.currentDragOperation.nodeType
    const targetNodeType = this.getNodeTypeFromElement(element)
    
    if (!targetNodeType) return false
    
    // Check if source can be dragged into target
    const sourceConfig = this.options.nodeTypeConfigs[sourceNodeType]
    const targetConfig = this.options.nodeTypeConfigs[targetNodeType]
    
    if (!sourceConfig || !targetConfig) return false
    
    // Basic compatibility check
    return sourceConfig.canDragInto.includes(targetNodeType) &&
           targetConfig.canReceiveFrom.includes(sourceNodeType)
  }

  private createDropZone(element: HTMLElement, index: number): DropZone | null {
    const pos = this.view.posAtDOM(element, 0)
    if (pos === null || pos === undefined) return null
    
    const targetNode = this.view.state.doc.nodeAt(pos)
    if (!targetNode) return null
    
    const nodeType = targetNode.type.name
    const targetConfig = this.options.nodeTypeConfigs[nodeType]
    if (!targetConfig) return null
    
    // Determine if this is a valid drop target
    const isValid = this.isValidDropTarget(element)
    
    const indicator = document.createElement('div')
    indicator.className = `drop-zone-indicator drop-zone-indicator--${nodeType} ${isValid ? 'valid' : 'invalid'}`
    
    // Style indicator based on node type and validity
    const indicatorColor = isValid ? 'var(--color-primary, #3b82f6)' : 'var(--color-error, #ef4444)'
    const indicatorHeight = this.getIndicatorHeightForNodeType(nodeType)
    
    indicator.style.cssText = `
      position: absolute;
      left: 0;
      right: 0;
      height: ${indicatorHeight}px;
      background: ${indicatorColor};
      border-radius: 2px;
      opacity: 0;
      transition: opacity 0.2s ease;
      z-index: 100;
      pointer-events: none;
    `

    // Position indicator based on node type
    this.positionDropIndicator(element, indicator, nodeType)

    // Add event listeners for drop zone
    element.addEventListener('dragover', (e) => this.handleDragOver(e, element, indicator))
    element.addEventListener('dragleave', (e) => this.handleDragLeave(e, element, indicator))
    element.addEventListener('drop', (e) => this.handleDrop(e, element))

    // Calculate nesting level
    const $pos = this.view.state.doc.resolve(pos)
    const nestingLevel = $pos.depth
    
    return {
      element,
      position: pos,
      isValid,
      visualIndicator: indicator,
      nodeType,
      canAcceptNodeTypes: targetConfig.canReceiveFrom,
      nestingLevel,
      validationReason: isValid ? undefined : 'Node type incompatibility or nesting rules violation'
    }
  }

  private getIndicatorHeightForNodeType(nodeType: string): number {
    switch (nodeType) {
      case 'heading': return 6
      case 'codeBlock': return 4
      case 'blockquote': return 4
      case 'bulletList':
      case 'orderedList': return 3
      default: return 4
    }
  }

  private positionDropIndicator(element: HTMLElement, indicator: HTMLElement, nodeType: string) {
    element.style.position = 'relative'
    
    switch (nodeType) {
      case 'listItem':
        // Position indicator at the top of list item
        indicator.style.top = '-2px'
        element.insertBefore(indicator, element.firstChild)
        break
        
      case 'heading':
        // Position indicator above heading
        indicator.style.top = '-3px'
        element.insertBefore(indicator, element.firstChild)
        break
        
      case 'codeBlock':
        // Position indicator at top of code block
        indicator.style.top = '-2px'
        element.insertBefore(indicator, element.firstChild)
        break
        
      case 'blockquote':
        // Position indicator outside quote border
        indicator.style.top = '-2px'
        indicator.style.left = '-4px'
        indicator.style.right = '-4px'
        element.insertBefore(indicator, element.firstChild)
        break
        
      case 'bulletList':
      case 'orderedList':
        // Position indicator above entire list
        indicator.style.top = '-2px'
        element.insertBefore(indicator, element.firstChild)
        break
        
      default:
        // Default positioning
        indicator.style.top = '-2px'
        element.insertBefore(indicator, element.firstChild)
    }
  }

  private hideDropZones() {
    this.dropZones.forEach(zone => {
      if (zone.visualIndicator && zone.visualIndicator.parentNode) {
        zone.visualIndicator.parentNode.removeChild(zone.visualIndicator)
      }
      // Remove event listeners
      zone.element.removeEventListener('dragover', this.handleDragOver as any)
      zone.element.removeEventListener('dragleave', this.handleDragLeave as any)
      zone.element.removeEventListener('drop', this.handleDrop as any)
    })
    this.dropZones = []
  }

  private handleDragOver(event: DragEvent, element: HTMLElement, indicator: HTMLElement) {
    event.preventDefault()
    event.dataTransfer!.dropEffect = 'move'
    
    // Use RAF throttling for smooth visual feedback
    const updateIndicator = PerformanceUtils.rafThrottle(() => {
      indicator.style.opacity = '1'
    })
    updateIndicator()
  }

  private handleDragLeave(event: DragEvent, element: HTMLElement, indicator: HTMLElement) {
    // Only hide if we're actually leaving the element (not entering a child)
    if (!element.contains(event.relatedTarget as Node)) {
      // Use RAF throttling for smooth visual feedback
      const hideIndicator = PerformanceUtils.rafThrottle(() => {
        indicator.style.opacity = '0'
      })
      hideIndicator()
    }
  }

  private handleDrop(event: DragEvent, targetElement: HTMLElement) {
    event.preventDefault()
    
    if (!this.currentDragOperation) return

    const targetPos = this.view.posAtDOM(targetElement, 0)
    if (targetPos === null || targetPos === undefined) return

    // Execute the move operation
    this.executeDrop(this.currentDragOperation.sourcePos, targetPos)
  }

  private executeDrop(sourcePos: number, targetPos: number) {
    const { state, dispatch } = this.view
    const { tr } = state

    try {
      // Get the source node
      const sourceNode = state.doc.nodeAt(sourcePos)
      if (!sourceNode) return

      // Calculate positions accounting for the deletion
      let adjustedTargetPos = targetPos
      if (sourcePos < targetPos) {
        adjustedTargetPos -= sourceNode.nodeSize
      }

      // Create transaction
      const newTr = tr
        .delete(sourcePos, sourcePos + sourceNode.nodeSize)
        .insert(adjustedTargetPos, sourceNode)

      dispatch(newTr)
    } catch (error) {
      console.error('Error executing drop:', error)
    }
  }

  destroy() {
    // Use performance utilities for cleanup
    PerformanceUtils.MemoryManager.cleanup(this)
    
    this.clearDragHandles()
    this.hideDropZones()
    this.currentDragOperation = null
    
    // Clear any remaining DOM references
    this.dragHandles.clear()
    this.dropZones = []
  }
}

export class DragHandlePlugin extends Plugin {
  constructor(options: DragHandlePluginOptions) {
    super({
      key: new PluginKey('dragHandle'),
      
      view(editorView) {
        return new DragHandlePluginView(editorView, options)
      },
      
      props: {
        handleDOMEvents: {
          dragstart: (view, event) => {
            // Let the plugin view handle drag start
            return false
          },
          dragover: (view, event) => {
            // Prevent default to allow drop
            if (event.dataTransfer?.types.includes('text/html')) {
              event.preventDefault()
              return true
            }
            return false
          },
          drop: (view, event) => {
            // Let the plugin view handle drop
            return false
          },
          dragend: (view, event) => {
            // Let the plugin view handle drag end
            return false
          }
        }
      }
    })
  }
}