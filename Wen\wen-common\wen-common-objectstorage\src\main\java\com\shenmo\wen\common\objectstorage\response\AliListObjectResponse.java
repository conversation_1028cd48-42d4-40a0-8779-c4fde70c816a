package com.shenmo.wen.common.objectstorage.response;

import com.aliyun.oss.model.OSSObjectSummary;
import com.shenmo.wen.common.constant.StringConstant;
import org.springframework.lang.NonNull;

import java.util.List;
import java.util.Optional;

/**
 * ali的列举对象响应
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class AliListObjectResponse extends ListObjectResponse<List<OSSObjectSummary>> {

    /**
     * 构造方法
     *
     * @param origin 源对象
     * @param bucket 桶名
     * @param prefix 对象前缀
     * <AUTHOR>
     */
    public AliListObjectResponse(List<OSSObjectSummary> origin, String bucket, String prefix) {
        super(origin, bucket, prefix);
    }

    @NonNull
    @Override
    public Long getSize() {

        return (long) getNames().size();
    }

    @NonNull
    @Override
    public Long getPrefixSize() {

        return (long) getPrefixes().size();
    }

    @NonNull
    @Override
    public List<String> getNames() {
        return Optional.of(origin)
                .map(summary -> summary.stream()
                        .map(OSSObjectSummary::getKey)
                        .toList())
                .orElse(List.of());
    }

    @NonNull
    @Override
    public List<String> getPrefixes() {
        return Optional.of(origin)
                .map(summary -> summary.stream()
                        .map(OSSObjectSummary::getKey)
                        .filter(key -> key.endsWith(StringConstant.SLASH))
                        .toList())
                .orElse(List.of());
    }
}
