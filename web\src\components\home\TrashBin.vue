<template>
  <transition name="trash-bin-fade">
    <div v-if="visible" class="trash-bin" :class="{ 'trash-bin-active': isActive }">
      <svg
        xmlns="http://www.w3.org/2000/svg"
        viewBox="0 0 24 24"
        width="48"
        height="48"
        :style="{ color: isActive ? '#ff4444' : '#666666' }"
      >
        <path
          fill="currentColor"
          d="M19 4h-3.5l-1-1h-5l-1 1H5v2h14M6 19a2 2 0 0 0 2 2h8a2 2 0 0 0 2-2V7H6v12Z"
        />
      </svg>
      <span class="trash-bin-text">{{ isActive ? '释放删除' : '拖拽到此处删除' }}</span>
    </div>
  </transition>
</template>

<script lang="ts" setup>
defineProps<{
  visible: boolean
  isActive: boolean
}>()
</script>

<style scoped lang="scss">
.trash-bin {
  position: fixed;
  bottom: 2rem;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 8rem;
  height: 8rem;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 1rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  transition: all 0.3s ease;
  z-index: 9998;

  &.trash-bin-active {
    background-color: rgba(255, 68, 68, 0.1);
    transform: translateX(-50%) scale(1.1);
    box-shadow: 0 6px 20px rgba(255, 68, 68, 0.3);
  }

  .trash-bin-text {
    margin-top: 0.5rem;
    font-size: 0.875rem;
    color: #666666;
    font-weight: 500;
  }

  &.trash-bin-active .trash-bin-text {
    color: #ff4444;
  }
}

/* 垃圾篓进入/离开动画 */
.trash-bin-fade-enter-active,
.trash-bin-fade-leave-active {
  transition:
    opacity 0.3s ease,
    transform 0.3s ease;
}

.trash-bin-fade-enter-from {
  opacity: 0;
  transform: translateX(-50%) translateY(2rem);
}

.trash-bin-fade-leave-to {
  opacity: 0;
  transform: translateX(-50%) translateY(2rem);
}
</style>
