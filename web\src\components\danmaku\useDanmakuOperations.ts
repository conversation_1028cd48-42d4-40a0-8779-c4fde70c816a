import { createApp, h } from 'vue'

import logger from '@/utils/log'

import { useDanmakuEffects } from './useDanmakuEffects'

import type { Danmu } from './types'
import type { Ref, ComputedRef } from 'vue'

interface DanmakuOperationsOptions {
  props: any
  emit: Function
  slots: any
  danmakuConfig: any
  danmuStyle: any
  danmuList: ComputedRef<Danmu[]>
  container: Ref<HTMLDivElement>
  dmContainer: Ref<HTMLDivElement>
  containerWidth: Ref<number>
  containerHeight: Ref<number>
  calcChannels: Ref<number>
  danmuHeight: Ref<number>
  index: Ref<number>
  hidden: Ref<boolean>
  paused: Ref<boolean>
  getChannelIndex: (el: HTMLDivElement, dmContainer: HTMLDivElement) => number
  danChannel: any
  timer: number
}

export function useDanmakuOperations(options: DanmakuOperationsOptions) {
  const {
    props,
    emit,
    slots,
    danmakuConfig,
    danmuStyle,
    danmuList,
    container,
    dmContainer,
    containerWidth,
    containerHeight,
    calcChannels,
    danmuHeight,
    index,
    hidden,
    paused,
    getChannelIndex,
    danChannel,
    timer: timerRef,
  } = options

  // 计时器
  let timer = timerRef

  /**
   * 创建插槽组件
   */
  function getSlotComponent(_danmu: any, _index: number) {
    const DmComponent = createApp({
      render() {
        return h('div', {}, [
          slots.dm &&
            slots.dm({
              danmu: _danmu,
              index: _index,
            }),
        ])
      },
    })

    const ele = DmComponent.mount(document.createElement('div'))
    return ele
  }

  /**
   * 绘制弹幕
   */
  function draw() {
    if (!paused.value && danmuList.value.length) {
      if (index.value > danmuList.value.length - 1) {
        const screenDanmus = dmContainer.value.children.length

        if (danmakuConfig.loop) {
          if (screenDanmus < index.value) {
            // 一轮弹幕插入完毕
            emit('list-end')
            index.value = 0
          }
          insert()
        }
      } else {
        insert()
      }
    }
  }

  /**
   * 插入弹幕
   */
  function insert(data?: any) {
    try {
      // 如果传入的是弹幕数据对象
      if (data && data.content) {
        let content = data.content
        // 如果content是字符串，尝试解析为JSON对象
        if (typeof content === 'string') {
          try {
            content = JSON.parse(content)
          } catch (e) {
            // 解析失败，保持原始字符串
            logger.warn('弹幕JSON解析失败，使用原始文本:', e)
          }
        }

        // 将处理后的内容添加到弹幕列表
        return add({
          id: data.commentId || Date.now().toString(),
          content: content,
        })
      }

      // 如果没有传入数据或传入的是简单弹幕，使用原始逻辑
      const _index = danmakuConfig.loop ? index.value % danmuList.value.length : index.value
      const _danmu = data || danmuList.value[_index]
      let el = document.createElement(`div`)

      if (danmakuConfig.useSlot) {
        el = getSlotComponent(_danmu, _index).$el
      } else {
        el.innerHTML = _danmu as string
        el.setAttribute('style', props.extraStyle)
        el.style.fontSize = `${danmuStyle.fontSize}px`
        el.style.lineHeight = `3rem`
      }
      el.classList.add('dm')
      dmContainer.value.appendChild(el)
      el.style.opacity = '0'

      const offsetHeight = el.offsetHeight
      const offsetWidth = el.offsetWidth
      if (!danmuStyle.height) {
        danmuHeight.value = 48 // 强制使用 3rem 高度
      }
      // 如果没有设置轨道数，则在获取到所有高度后计算出最大轨道数
      if (!danmakuConfig.channels) {
        calcChannels.value = Math.floor(
          containerHeight.value / (danmuStyle.height + danmuStyle.top),
        )
      }
      let channelIndex = getChannelIndex(el, dmContainer.value)
      if (channelIndex >= 0) {
        const height = danmuStyle.height
        const computedChannelIndex = () => {
          const top = channelIndex * (height + danmuStyle.top) + offsetHeight
          if (top >= containerHeight.value) {
            channelIndex--
            computedChannelIndex()
          }
        }
        computedChannelIndex()
        logger.debug('danmaku height top: ', height, danmuStyle.top)
        el.classList.add('move')
        el.dataset.index = `${_index}`
        el.dataset.channel = channelIndex.toString()
        el.style.opacity = '1'
        const top = channelIndex * (height + danmuStyle.top) + 'px'
        el.style.top = top
        el.style.left = `${containerWidth.value}px`
        el.style.animationDuration = `${containerWidth.value / danmuStyle.speeds}s`
        el.addEventListener('animationend', () => {
          if (Number(el.dataset.index) === danmuList.value.length - 1 && !danmakuConfig.loop) {
            emit('play-end', el.dataset.index)
          }
          dmContainer.value && dmContainer.value.removeChild(el)
        })
        index.value++
        el.style.width = offsetWidth + danmuStyle.right + 'px'
        el.style.setProperty('--dm-scroll-width', `-${containerWidth.value + offsetWidth}px`)
      } else {
        dmContainer.value.removeChild(el)
      }
    } catch (error) {
      logger.error('添加弹幕时发生错误:', error)
    }
  }

  /**
   * 清除定时器
   */
  function clearTimer() {
    clearInterval(timer)
    timer = 0
  }

  /**
   * 开始播放弹幕
   */
  function play() {
    paused.value = false
    if (!timer) {
      timer = window.setInterval(() => draw(), danmakuConfig.debounce)
    }
  }

  /**
   * 暂停弹幕
   */
  function pause(): void {
    paused.value = true
  }

  /**
   * 停止弹幕
   */
  function stop() {
    Object.assign(danChannel, {})
    dmContainer.value.innerHTML = ''
    paused.value = true
    hidden.value = false
    clearTimer()
    index.value = 0
  }

  /**
   * 显示弹幕
   */
  function show(): void {
    hidden.value = false
  }

  /**
   * 隐藏弹幕
   */
  function hide(): void {
    hidden.value = true
  }

  /**
   * 重置弹幕
   */
  function reset() {
    danmuHeight.value = 0
    containerWidth.value = container.value.offsetWidth
    containerHeight.value = container.value.offsetHeight
    if (containerWidth.value === 0 || containerHeight.value === 0) {
      throw new Error('获取不到容器宽高')
    }

    if (props.isSuspend) {
      const { initSuspendEvents } = useDanmakuEffects(dmContainer, emit)
      initSuspendEvents()
    }

    if (danmakuConfig.autoplay) {
      play()
    }
  }

  /**
   * 添加弹幕（插入到当前播放的弹幕位置）
   */
  function add(danmu: Danmu): number {
    if (index.value === danmuList.value.length) {
      // 如果当前弹幕已经播放完了，那么仍然走 push
      danmuList.value.push(danmu)

      return danmuList.value.length - 1
    } else {
      const _index = index.value % danmuList.value.length
      danmuList.value.splice(_index, 0, danmu)

      return _index + 1
    }
  }

  /**
   * 添加弹幕（插入到弹幕末尾）
   */
  function push(danmu: Danmu): number {
    danmuList.value.push(danmu)
    return danmuList.value.length - 1
  }

  /**
   * 获取播放状态
   */
  function getPlayState(): boolean {
    return !paused.value
  }

  return {
    getSlotComponent,
    draw,
    insert,
    add,
    push,
    clearTimer,
    play,
    pause,
    stop,
    show,
    hide,
    reset,
    getPlayState,
  }
}
