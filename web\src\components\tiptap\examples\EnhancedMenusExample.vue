<template>
  <div class="enhanced-menus-example">
    <h2>Enhanced Menus Example</h2>
    <p>This example demonstrates the enhanced click menu and block menu functionality.</p>
    
    <div class="editor-container">
      <div class="editor-wrapper" ref="editorRef"></div>
    </div>
    
    <div class="instructions">
      <h3>How to use:</h3>
      <ul>
        <li><strong>Click Menu:</strong> Hover over any block to see the click menu with plus and drag buttons</li>
        <li><strong>Block Menu:</strong> Type <code>/</code> to open the command menu</li>
        <li><strong>Drag & Drop:</strong> Use the drag handle to reorder blocks</li>
        <li><strong>Context Menu:</strong> Click the drag handle to see conversion options</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onBeforeUnmount } from 'vue'
import { Editor } from '@tiptap/core'
import StarterKit from '@tiptap/starter-kit'
import TaskList from '@tiptap/extension-task-list'
import TaskItem from '@tiptap/extension-task-item'
import Table from '@tiptap/extension-table'
import TableRow from '@tiptap/extension-table-row'
import TableHeader from '@tiptap/extension-table-header'
import TableCell from '@tiptap/extension-table-cell'
import Image from '@tiptap/extension-image'

import {
  createEnhancedClickMenu,
  createEnhancedBlockMenu,
  registerBlockMenuItems,
} from '../extensions/enhanced-menus'

const editorRef = ref<HTMLElement>()
let editor: Editor | null = null

const initialContent = `
<h1>Enhanced Menus Demo</h1>
<p>Welcome to the enhanced menus demonstration! This editor includes both click menus and block menus.</p>

<h2>Click Menu Features</h2>
<p>Hover over any block to see the click menu appear on the left side. The menu includes:</p>
<ul>
  <li>Plus button to add new blocks</li>
  <li>Drag handle for reordering content</li>
  <li>Context menu for block transformations</li>
</ul>

<h2>Block Menu Features</h2>
<p>Type "/" anywhere to open the command menu. You can:</p>
<ul>
  <li>Search for commands by typing</li>
  <li>Navigate with arrow keys</li>
  <li>Press Enter to execute commands</li>
</ul>

<blockquote>
  <p>Try hovering over this quote block and using the click menu!</p>
</blockquote>

<h3>Try the Block Menu</h3>
<p>Click at the end of this line and type "/" to see the block menu in action.</p>
`

onMounted(() => {
  if (!editorRef.value) return

  editor = new Editor({
    element: editorRef.value,
    extensions: [
      StarterKit,
      TaskList,
      TaskItem.configure({
        nested: true,
      }),
      Table.configure({
        resizable: true,
      }),
      TableRow,
      TableHeader,
      TableCell,
      Image,
      createEnhancedClickMenu(),
      createEnhancedBlockMenu(),
    ],
    content: initialContent,
    editorProps: {
      attributes: {
        class: 'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
      },
    },
  })

  // Register block menu items
  registerBlockMenuItems(editor)
})

onBeforeUnmount(() => {
  if (editor) {
    editor.destroy()
  }
})
</script>

<style scoped>
.enhanced-menus-example {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.editor-container {
  margin: 20px 0;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  overflow: hidden;
}

.editor-wrapper {
  position: relative;
  min-height: 400px;
  padding: 20px;
  background: white;
}

.instructions {
  margin-top: 20px;
  padding: 16px;
  background: #f9fafb;
  border-radius: 8px;
  border-left: 4px solid #3b82f6;
}

.instructions h3 {
  margin-top: 0;
  color: #1f2937;
}

.instructions ul {
  margin-bottom: 0;
}

.instructions li {
  margin-bottom: 8px;
}

.instructions code {
  background: #e5e7eb;
  padding: 2px 4px;
  border-radius: 3px;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

/* Import enhanced menu styles */
:deep(.ProseMirror) {
  outline: none;
  padding: 20px;
  min-height: 300px;
}

/* Enhanced menu styles */
:deep(.ProseMirror-cm) {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  z-index: 998;
}

:deep(.ProseMirror-cm-plus),
:deep(.ProseMirror-cm-drag) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  border-radius: 4px;
  cursor: pointer;
  color: #6b7280;
  transition: all 0.15s ease;
}

:deep(.ProseMirror-cm-plus:hover),
:deep(.ProseMirror-cm-drag:hover) {
  background: #f3f4f6;
  color: #374151;
}

:deep(.ProseMirror-cm-drag) {
  cursor: grab;
}

:deep(.ProseMirror-cm-drag:active) {
  cursor: grabbing;
}

:deep(.ProseMirror-bm) {
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  padding: 8px;
  max-height: 400px;
  overflow-y: auto;
  min-width: 280px;
  z-index: 1000;
}

:deep(.ProseMirror-bm-button) {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: none;
  border-radius: 6px;
  cursor: pointer;
  text-align: left;
  gap: 12px;
  transition: background-color 0.15s ease;
}

:deep(.ProseMirror-bm-button:hover),
:deep(.ProseMirror-bm-button[data-active="true"]) {
  background: #f3f4f6;
}

:deep(.ProseMirror-bm-button-icon) {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  color: #6b7280;
}

:deep(.ProseMirror-bm-button-name) {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
}

:deep(.ProseMirror-bm-button-shortcut) {
  font-size: 12px;
  color: #9ca3af;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace;
}

:deep(.ProseMirror-bm-divider) {
  display: block;
  height: 1px;
  background: #e5e7eb;
  margin: 8px 0;
}

:deep(.ProseMirror-bm-empty) {
  padding: 16px;
  text-align: center;
  color: #9ca3af;
  font-size: 14px;
}

:deep(.ProseMirror-icon-plus::before) {
  content: "➕";
  font-size: 12px;
}

:deep(.ProseMirror-icon-drag::before) {
  content: "⋮⋮";
  font-size: 12px;
  letter-spacing: -2px;
}
</style>
