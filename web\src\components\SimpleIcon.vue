<template>
  <div
    class="simple-icon"
    :style="{
      width: size + 'px',
      height: size + 'px',
      display: 'inline-flex',
      alignItems: 'center',
      justifyContent: 'center',
    }"
    v-html="processedSvg"
  ></div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  name: string
  size?: number
  color?: string
}

const props = withDefaults(defineProps<Props>(), {
  size: 20,
  color: 'currentColor',
})

// 简单的图标映射
const iconMap: Record<string, string> = {
  bold: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><g fill="none"><path d="M5 4.5A1.5 1.5 0 0 1 6.5 3h3.88c2.364 0 4.12 1.934 4.12 4.12c0 .819-.247 1.606-.68 2.269c.842.749 1.427 1.849 1.427 3.241c0 2.775-2.318 4.37-4.367 4.37H6.5A1.5 1.5 0 0 1 5 15.5v-11zM8 6v2.25h2.38c.625 0 1.12-.516 1.12-1.13A1.12 1.12 0 0 0 10.38 6H8zm0 5.25V14h2.88c.691 0 1.367-.537 1.367-1.37c0-.84-.684-1.38-1.367-1.38H8z" fill="currentColor"></path></g></svg>',
  italic:
    '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><g fill="none"><path d="M8 3.25a.75.75 0 0 1 .75-.75h7.5a.75.75 0 0 1 0 1.5h-3.235L8.592 15.5h2.658a.75.75 0 0 1 0 1.5h-7.5a.75.75 0 0 1 0-1.5h3.235L11.408 4H8.75A.75.75 0 0 1 8 3.25z" fill="currentColor"></path></g></svg>',
  h1: '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20"><g fill="none"><path d="M16.573 3.823a.75.75 0 0 0-1.058.53c-.255 1.138-1.308 2.608-2.681 3.523a.75.75 0 1 0 .832 1.248A8.769 8.769 0 0 0 15.5 7.47V15.5a.75.75 0 0 0 1.5 0V4.516a.75.75 0 0 0-.427-.693zM3.5 4.5a.75.75 0 1 0-1.5 0v11a.75.75 0 0 0 1.5 0v-5h5v5a.75.75 0 0 0 1.5 0v-11a.75.75 0 1 0-1.5 0V9h-5V4.5z" fill="currentColor"></path></g></svg>',
}

const processedSvg = computed(() => {
  const svg = iconMap[props.name]
  if (!svg) {
    console.warn(`Icon "${props.name}" not found`)
    return `<span style="color: red;">?</span>`
  }

  return svg
    .replace(/width="[^"]*"/g, `width="${props.size}px"`)
    .replace(/height="[^"]*"/g, `height="${props.size}px"`)
    .replace(/fill="currentColor"/g, `fill="${props.color}"`)
    .replace(/stroke="currentColor"/g, `stroke="${props.color}"`)
    .replace(/<g fill="none">/g, '<g>')
})
</script>

<style scoped>
.simple-icon {
  line-height: 1;
}
</style>
