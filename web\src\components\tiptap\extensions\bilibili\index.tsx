import { Node, type RawCommands } from '@tiptap/vue-3'
import { VueNodeViewRenderer } from '@tiptap/vue-3'

import BilibiliNodeView from './BilibiliNodeView.vue'

import type { NodeViewProps } from '@tiptap/core'

// 导入 Vue 组件和样式
import './styles/bilibili.scss'

/**
 * 解析 Bilibili 视频 URL，提取视频 ID
 * @param url 视频链接
 * @returns 视频 ID 或 null
 */
const parseBilibiliUrl = (url: string): string | null => {
  // 支持 BV 号
  const bvidPattern = /(BV[a-zA-Z0-9]{10})/
  // 支持 aid 号
  const aidPattern = /aid=(\d+)/
  // 支持短链接
  const shortPattern = /b23\.tv\/([a-zA-Z0-9]+)/
  // 支持完整链接
  const fullPattern = /bilibili\.com\/video\/([a-zA-Z0-9]+)/

  const bvidMatch = url.match(bvidPattern)
  const aidMatch = url.match(aidPattern)
  const shortMatch = url.match(shortPattern)
  const fullMatch = url.match(fullPattern)

  return bvidMatch
    ? bvidMatch[1]
    : aidMatch
      ? aidMatch[1]
      : shortMatch
        ? shortMatch[1]
        : fullMatch
          ? fullMatch[1]
          : null
}

const Bilibili = Node.create({
  name: 'bilibili',
  group: 'block',
  content: 'text*',
  inline: false,
  atom: true,

  addAttributes() {
    return {
      src: {
        default: null,
        parseHTML: (element) => {
          const src = element.getAttribute('src')
          return src ? parseBilibiliUrl(src) : null
        },
      },
      danmaku: {
        default: true,
      },
      mute: {
        default: false,
      },
    }
  },

  parseHTML() {
    return [
      {
        tag: 'iframe[src]',
        getAttrs: (dom) => ({
          src: dom.getAttribute('src'),
          danmaku: dom.getAttribute('data-danmaku') === 'true',
          mute: dom.getAttribute('data-mute') === 'true',
        }),
      },
    ]
  },

  // 添加 renderHTML 方法，用于将节点渲染为 DOM
  renderHTML({ node }) {
    const { src, danmaku, mute } = node.attrs
    const videoId = parseBilibiliUrl(src)

    if (!videoId) {
      return ['div', { class: 'bilibili-error' }, '无效的 Bilibili 视频链接']
    }

    const params = new URLSearchParams({
      [videoId.startsWith('BV') ? 'bvid' : 'aid']: videoId,
      page: '1',
      danmaku: danmaku ? '1' : '0',
      mute: mute ? '1' : '0',
      high_quality: '1',
    })

    const iframeSrc = `//www.bilibili.com/blackboard/html5mobileplayer.html?${params.toString()}`

    return [
      'div',
      { class: 'bilibili-container' },
      [
        'div',
        { class: 'bilibili-wrapper' },
        [
          'iframe',
          {
            src: iframeSrc,
            scrolling: 'no',
            border: '0',
            frameborder: '0',
            framespacing: '0',
            allowfullscreen: 'true',
            'data-danmaku': danmaku,
            'data-mute': mute,
            class: 'bilibili-iframe',
          },
        ],
      ],
    ]
  },

  // 使用 Vue 组件渲染，移除 JavaScript 动态 DOM 渲染
  addNodeView() {
    return VueNodeViewRenderer(
      BilibiliNodeView as unknown as typeof BilibiliNodeView & { new (): NodeViewProps },
    )
  },

  addCommands() {
    return {
      setBilibiliVideo:
        (options: { src: string; danmaku?: boolean; mute?: boolean }) =>
        ({ commands }: { commands: Record<string, (...args: unknown[]) => boolean> }) => {
          return commands.insertContent({
            type: this.name,
            attrs: options,
          })
        },
    } as Partial<RawCommands>
  },
})

export default Bilibili
