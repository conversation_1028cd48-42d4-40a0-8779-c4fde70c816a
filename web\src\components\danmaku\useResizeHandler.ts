import { type Ref } from 'vue'

import type { DanmuItem } from '@/types/danmaku.types'

export function useResizeHandler(
  container: Ref<HTMLDivElement>,
  dmContainer: Ref<HTMLDivElement>,
  containerWidth: Ref<number>,
  containerHeight: Ref<number>,
  danmu: DanmuItem,
) {
  /**
   * 初始化核心尺寸
   */
  function initCore() {
    containerWidth.value = container.value.offsetWidth
    containerHeight.value = container.value.offsetHeight
    if (containerWidth.value === 0 || containerHeight.value === 0) {
      throw new Error('获取不到容器宽高')
    }
  }

  /**
   * 重置尺寸
   */
  function resize() {
    initCore()
    const items = dmContainer.value.getElementsByClassName('dm')

    for (let i = 0; i < items.length; i++) {
      const el = items[i] as HTMLDivElement

      el.style.setProperty('--dm-scroll-width', `-${containerWidth.value + el.offsetWidth}px`)
      el.style.left = `${containerWidth.value}px`
      el.style.animationDuration = `${containerWidth.value / danmu.speeds}s`
    }
  }

  return {
    initCore,
    resize,
  }
}
