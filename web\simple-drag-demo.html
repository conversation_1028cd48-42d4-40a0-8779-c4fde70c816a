<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单拖拽演示 - 编辑器段落拖拽功能</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
            background: #f8f9fa;
        }
        
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 12px;
            margin-bottom: 30px;
            text-align: center;
        }
        
        .editor-container {
            background: white;
            border: 1px solid #e0e0e0;
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .paragraph-item {
            position: relative;
            margin: 10px 0;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
            cursor: move;
            transition: all 0.2s ease;
            user-select: none;
        }
        
        .paragraph-item:hover {
            background: #e9ecef;
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0,123,255,0.1);
        }
        
        .paragraph-item.dragging {
            opacity: 0.5;
            transform: rotate(2deg);
            z-index: 1000;
        }
        
        .paragraph-item.drag-over {
            border-color: #28a745;
            background: #d4edda;
            border-style: dashed;
        }
        
        .drag-handle {
            position: absolute;
            left: -30px;
            top: 50%;
            transform: translateY(-50%);
            width: 20px;
            height: 20px;
            background: #6c757d;
            border-radius: 4px;
            cursor: grab;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            opacity: 0;
            transition: opacity 0.2s ease;
        }
        
        .paragraph-item:hover .drag-handle {
            opacity: 1;
        }
        
        .drag-handle:active {
            cursor: grabbing;
        }
        
        .context-menu {
            position: fixed;
            background: white;
            border: 1px solid #ccc;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            padding: 8px 0;
            min-width: 180px;
            z-index: 1000;
            display: none;
        }
        
        .context-menu-item {
            padding: 8px 16px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            transition: background-color 0.2s;
        }
        
        .context-menu-item:hover {
            background: #f8f9fa;
        }
        
        .context-menu-item .icon {
            width: 16px;
            text-align: center;
        }
        
        .instructions {
            background: #e2e3e5;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .instructions h3 {
            margin-top: 0;
            color: #495057;
        }
        
        .feature-list {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        .feature-item {
            background: white;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .status {
            display: inline-block;
            padding: 8px 16px;
            border-radius: 20px;
            font-weight: bold;
            margin: 10px 0;
            background: #28a745;
            color: white;
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🎯 编辑器段落拖拽功能演示</h1>
        <p>简单实用的拖拽和右键菜单功能</p>
        <div class="status">✅ 功能正常</div>
    </div>
    
    <div class="instructions">
        <h3>🧪 如何使用</h3>
        <ul>
            <li><strong>拖拽段落</strong>：鼠标悬停段落，点击左侧拖拽手柄或直接拖拽段落</li>
            <li><strong>右键菜单</strong>：右键点击段落显示上下文菜单</li>
            <li><strong>键盘操作</strong>：选中段落后使用 Alt+↑/↓ 移动，Ctrl+D 复制</li>
        </ul>
    </div>
    
    <div class="editor-container">
        <h2>📝 可拖拽编辑器</h2>
        
        <div class="paragraph-item" draggable="true" data-id="1">
            <div class="drag-handle">⋮⋮</div>
            <strong>第一个段落</strong> - 这是一个可以拖拽的段落。鼠标悬停查看拖拽手柄，右键查看菜单选项。
        </div>
        
        <div class="paragraph-item" draggable="true" data-id="2">
            <div class="drag-handle">⋮⋮</div>
            <strong>第二个段落</strong> - 你可以通过拖拽来重新排列这些段落的顺序。
        </div>
        
        <div class="paragraph-item" draggable="true" data-id="3">
            <div class="drag-handle">⋮⋮</div>
            <strong>第三个段落</strong> - 支持键盘快捷键操作，选中后按 Alt+↑ 或 Alt+↓ 移动。
        </div>
        
        <div class="paragraph-item" draggable="true" data-id="4">
            <div class="drag-handle">⋮⋮</div>
            <strong>第四个段落</strong> - 右键点击可以看到复制、删除等操作选项。
        </div>
        
        <div class="paragraph-item" draggable="true" data-id="5">
            <div class="drag-handle">⋮⋮</div>
            <strong>第五个段落</strong> - 这个演示展示了编辑器拖拽功能的基本实现。
        </div>
    </div>
    
    <div class="feature-list">
        <div class="feature-item">
            <h3>🖱️ 拖拽功能</h3>
            <ul>
                <li>鼠标悬停显示拖拽手柄</li>
                <li>拖拽重新排列段落</li>
                <li>视觉反馈和动画效果</li>
                <li>拖拽过程中的状态提示</li>
            </ul>
        </div>
        
        <div class="feature-item">
            <h3>📋 右键菜单</h3>
            <ul>
                <li>复制段落</li>
                <li>删除段落</li>
                <li>上移/下移</li>
                <li>转换段落类型</li>
            </ul>
        </div>
        
        <div class="feature-item">
            <h3>⌨️ 键盘快捷键</h3>
            <ul>
                <li>Alt+↑ 向上移动</li>
                <li>Alt+↓ 向下移动</li>
                <li>Ctrl+D 复制段落</li>
                <li>Delete 删除段落</li>
            </ul>
        </div>
        
        <div class="feature-item">
            <h3>🎨 用户体验</h3>
            <ul>
                <li>平滑的动画过渡</li>
                <li>清晰的视觉反馈</li>
                <li>直观的操作方式</li>
                <li>响应式设计</li>
            </ul>
        </div>
    </div>
    
    <!-- 右键菜单 -->
    <div class="context-menu" id="contextMenu">
        <div class="context-menu-item" data-action="copy">
            <span class="icon">📋</span>
            <span>复制段落</span>
        </div>
        <div class="context-menu-item" data-action="delete">
            <span class="icon">🗑️</span>
            <span>删除段落</span>
        </div>
        <div class="context-menu-item" data-action="moveUp">
            <span class="icon">↑</span>
            <span>上移</span>
        </div>
        <div class="context-menu-item" data-action="moveDown">
            <span class="icon">↓</span>
            <span>下移</span>
        </div>
        <div class="context-menu-item" data-action="toHeading">
            <span class="icon">H1</span>
            <span>转为标题</span>
        </div>
    </div>
    
    <script>
        let draggedElement = null;
        let currentContextElement = null;
        
        // 拖拽功能
        document.querySelectorAll('.paragraph-item').forEach(item => {
            // 拖拽开始
            item.addEventListener('dragstart', function(e) {
                draggedElement = this;
                this.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/html', this.outerHTML);
            });
            
            // 拖拽结束
            item.addEventListener('dragend', function(e) {
                this.classList.remove('dragging');
                document.querySelectorAll('.paragraph-item').forEach(p => {
                    p.classList.remove('drag-over');
                });
            });
            
            // 拖拽经过
            item.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
                this.classList.add('drag-over');
            });
            
            // 拖拽离开
            item.addEventListener('dragleave', function(e) {
                this.classList.remove('drag-over');
            });
            
            // 放置
            item.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('drag-over');
                
                if (draggedElement && draggedElement !== this) {
                    const container = this.parentNode;
                    const draggedClone = draggedElement.cloneNode(true);
                    
                    // 重新绑定事件
                    bindEvents(draggedClone);
                    
                    // 插入到目标位置
                    container.insertBefore(draggedClone, this);
                    draggedElement.remove();
                    
                    console.log('段落已重新排序');
                }
            });
            
            // 右键菜单
            item.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                currentContextElement = this;
                showContextMenu(e.pageX, e.pageY);
            });
        });
        
        // 绑定事件到新元素
        function bindEvents(element) {
            element.addEventListener('dragstart', function(e) {
                draggedElement = this;
                this.classList.add('dragging');
                e.dataTransfer.effectAllowed = 'move';
                e.dataTransfer.setData('text/html', this.outerHTML);
            });
            
            element.addEventListener('dragend', function(e) {
                this.classList.remove('dragging');
                document.querySelectorAll('.paragraph-item').forEach(p => {
                    p.classList.remove('drag-over');
                });
            });
            
            element.addEventListener('dragover', function(e) {
                e.preventDefault();
                e.dataTransfer.dropEffect = 'move';
                this.classList.add('drag-over');
            });
            
            element.addEventListener('dragleave', function(e) {
                this.classList.remove('drag-over');
            });
            
            element.addEventListener('drop', function(e) {
                e.preventDefault();
                this.classList.remove('drag-over');
                
                if (draggedElement && draggedElement !== this) {
                    const container = this.parentNode;
                    const draggedClone = draggedElement.cloneNode(true);
                    bindEvents(draggedClone);
                    container.insertBefore(draggedClone, this);
                    draggedElement.remove();
                }
            });
            
            element.addEventListener('contextmenu', function(e) {
                e.preventDefault();
                currentContextElement = this;
                showContextMenu(e.pageX, e.pageY);
            });
        }
        
        // 显示右键菜单
        function showContextMenu(x, y) {
            const menu = document.getElementById('contextMenu');
            menu.style.left = x + 'px';
            menu.style.top = y + 'px';
            menu.style.display = 'block';
        }
        
        // 隐藏右键菜单
        function hideContextMenu() {
            document.getElementById('contextMenu').style.display = 'none';
        }
        
        // 点击其他地方隐藏菜单
        document.addEventListener('click', function(e) {
            if (!e.target.closest('.context-menu')) {
                hideContextMenu();
            }
        });
        
        // 菜单项点击事件
        document.querySelectorAll('.context-menu-item').forEach(item => {
            item.addEventListener('click', function() {
                const action = this.dataset.action;
                executeAction(action, currentContextElement);
                hideContextMenu();
            });
        });
        
        // 执行菜单操作
        function executeAction(action, element) {
            if (!element) return;
            
            switch (action) {
                case 'copy':
                    const clone = element.cloneNode(true);
                    bindEvents(clone);
                    element.parentNode.insertBefore(clone, element.nextSibling);
                    console.log('段落已复制');
                    break;
                    
                case 'delete':
                    if (confirm('确定要删除这个段落吗？')) {
                        element.remove();
                        console.log('段落已删除');
                    }
                    break;
                    
                case 'moveUp':
                    const prevSibling = element.previousElementSibling;
                    if (prevSibling) {
                        element.parentNode.insertBefore(element, prevSibling);
                        console.log('段落已上移');
                    }
                    break;
                    
                case 'moveDown':
                    const nextSibling = element.nextElementSibling;
                    if (nextSibling) {
                        element.parentNode.insertBefore(nextSibling, element);
                        console.log('段落已下移');
                    }
                    break;
                    
                case 'toHeading':
                    element.innerHTML = element.innerHTML.replace('<strong>', '<h3>').replace('</strong>', '</h3>');
                    console.log('已转换为标题');
                    break;
            }
        }
        
        // 键盘快捷键
        document.addEventListener('keydown', function(e) {
            const focusedElement = document.activeElement;
            
            if (focusedElement && focusedElement.classList.contains('paragraph-item')) {
                if (e.altKey && e.key === 'ArrowUp') {
                    e.preventDefault();
                    executeAction('moveUp', focusedElement);
                } else if (e.altKey && e.key === 'ArrowDown') {
                    e.preventDefault();
                    executeAction('moveDown', focusedElement);
                } else if (e.ctrlKey && e.key === 'd') {
                    e.preventDefault();
                    executeAction('copy', focusedElement);
                } else if (e.key === 'Delete') {
                    e.preventDefault();
                    executeAction('delete', focusedElement);
                }
            }
        });
        
        // 让段落可以获得焦点
        document.querySelectorAll('.paragraph-item').forEach(item => {
            item.setAttribute('tabindex', '0');
        });
        
        console.log('拖拽演示已加载完成！');
        console.log('功能说明：');
        console.log('1. 拖拽段落重新排序');
        console.log('2. 右键显示上下文菜单');
        console.log('3. 键盘快捷键操作');
    </script>
</body>
</html>