package com.shenmo.wen.modules.user.pojo.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.shenmo.wen.common.mybatis.handler.TimestampToLongTypeHandler;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

/**
 * 用户实体类
 * <AUTHOR>
 */
@Data
public class WenUserVo {

    /**
     * 用户ID
     */
    private Long id;

    /**
     * 用户名
     */
    private String username;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 头像URL
     */
    private String avatar;

    /**
     * IP归属地
     */
    private String ipLocation;

    /**
     * 职业信息
     */
    private String job;

    /**
     * 用户等级
     */
    private Integer level;

    /**
     * 用户经验值
     */
    private Integer experience;

    /**
     * 通知接收类型
     * 0-全部，1-发布，2-修改，3-收藏，4-关闭
     */
    private Integer notificationReceiveType;

    /**
     * 创建时间
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long ctTm;

    /**
     * 修改时间
     */
    @TableField(typeHandler = TimestampToLongTypeHandler.class, jdbcType = JdbcType.TIMESTAMP)
    private Long mdTm;
}
