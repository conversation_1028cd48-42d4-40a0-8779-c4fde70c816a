package com.shenmo.wen.modules.user.mapper;


import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.shenmo.wen.modules.user.pojo.domain.WenSearchUser;
import com.shenmo.wen.modules.user.pojo.entity.WenUser;

import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;
import java.util.Optional;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface WenUserMapper extends BaseMapper<WenUser> {

    @Select("""
            select id from wen_user limit #{offset}, #{limit}
            """)
    List<Long> batchIds(@Param("offset") int offset,@Param("limit") int limit);

    default int updateIpById(Long id, String ip) {
        return update(Wrappers.<WenUser>lambdaUpdate().eq(WenUser::getId, id).set(WenUser::getIp, ip));
    }

    default WenUser byId(long id) {
        return Optional.ofNullable(selectById(id)).orElse(new WenUser());
    }

    default boolean existsById(long id) {
        return selectCount(Wrappers.<WenUser>lambdaQuery().eq(WenUser::getId, id)) > 0;
    }

    default WenUser byPhone(String phone) {
        return selectOne(wrapperByPhone(phone));
    }

    default long countByPhone(String phone) {
        return selectCount(wrapperByPhone(phone));
    }

    default Wrapper<WenUser> wrapperByPhone(String phone) {
        return Wrappers.<WenUser>lambdaQuery().eq(WenUser::getPhone, phone);
    }

    default List<WenUser> listIdUsernameByUsernames(List<String> usernames) {
        if (CollectionUtils.isEmpty(usernames)) {
            return List.of();
        }
        return selectList(Wrappers.<WenUser>lambdaQuery().select(WenUser::getId, WenUser::getUsername).in(WenUser::getUsername, usernames));
    }

    default long countByUsername(String username) {
        return selectCount(Wrappers.<WenUser>lambdaQuery().eq(WenUser::getUsername, username));
    }

    default long updateAvatarById(long id, String avatar) {
        return update(Wrappers.<WenUser>lambdaUpdate().eq(WenUser::getId, id).set(WenUser::getAvatar, avatar));
    }

    default WenUser byUsername(String username) {
        return Optional.ofNullable(selectOne(Wrappers.<WenUser>lambdaQuery().eq(WenUser::getUsername, username))).orElse(new WenUser());
    }

    /**
     * 更新用户通知接收类型
     * @param id 用户ID
     * @param notificationReceiveType 通知接收类型
     * @return 是否更新成功
     */
    default boolean updateNotificationReceiveTypeById(long id, Integer notificationReceiveType) {
        return update(Wrappers.<WenUser>lambdaUpdate()
                .eq(WenUser::getId, id)
                .set(WenUser::getNotificationReceiveType, notificationReceiveType)) > 0;
    }

    @Select("select id, `username`, `avatar` from `wen_user` where `username` like concat(#{username}, '%')")
    List<WenSearchUser> searchByUsernameLike(String username);

    /**
     * 根据用户ID获取用户名
     *
     * @param userId 用户ID
     * @return 用户名
     */
    default String getUsernameById(Long userId) {
        return Optional.ofNullable(selectOne(Wrappers.<WenUser>lambdaQuery()
                .select(WenUser::getUsername)
                .eq(WenUser::getId, userId)))
                .map(WenUser::getUsername)
                .orElse("");
    }

    /**
     * 根据用户ID获取头像
     *
     * @param userId 用户ID
     * @return 头像URL
     */
    default String getAvatarById(Long userId) {
        return Optional.ofNullable(selectOne(Wrappers.<WenUser>lambdaQuery()
                .select(WenUser::getAvatar)
                .eq(WenUser::getId, userId)))
                .map(WenUser::getAvatar)
                .orElse("");
    }
}
