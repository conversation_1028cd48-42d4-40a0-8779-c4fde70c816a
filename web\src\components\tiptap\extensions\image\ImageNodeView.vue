<template>
  <NodeViewWrapper
    ref="imageWrapper"
    class="image-wrapper"
    :class="{
      'readonly-image': !isEditable,
      'ProseMirror-selectednode': isSelected && isEditable,
      resizing: isResizing,
    }"
    :style="wrapperStyles"
    @click="handleWrapperClick"
  >
    <!-- 图片元素 -->
    <img
      ref="imageElement"
      :src="imageSrc"
      :alt="node.attrs.alt || ''"
      :style="imageStyles"
      :data-relative-src="relativeSrc"
      :data-original-src="relativeSrc"
      contenteditable="false"
      loading="eager"
      class="cursor-pointer"
      @dblclick="handleImageDoubleClick"
      @click="handleImageClick"
      @load="handleImageLoad"
      @error="handleImageError"
    />

    <!-- 调整大小控制点 -->
    <template v-if="isEditable && isSelected">
      <!-- 角控制点 -->
      <div
        v-for="position in cornerHandles"
        :key="position"
        :class="['resize-handle', `handle-${position}`]"
        :data-handle-position="position"
        :data-center-handle="false"
        :style="getHandleStyle(position)"
        @mousedown="(e) => handleResizeStart(e, position)"
        @touchstart="(e) => handleTouchStart(e, position)"
      ></div>

      <!-- 边控制点 -->
      <div
        v-for="position in edgeHandles"
        :key="position"
        :class="['resize-handle', `handle-${position}`]"
        :data-handle-position="position"
        :data-center-handle="true"
        :style="getHandleStyle(position)"
        @mousedown="(e) => handleResizeStart(e, position)"
        @touchstart="(e) => handleTouchStart(e, position)"
      ></div>
    </template>

    <!-- 尺寸信息显示 -->
    <div v-if="isResizing" class="resize-info">
      {{ Math.round(currentWidth) }} × {{ Math.round(currentHeight) }}
    </div>
  </NodeViewWrapper>
</template>

<script setup lang="ts">
import { NodeViewWrapper } from '@tiptap/vue-3'
import {
  ref,
  computed,
  onMounted,
  onBeforeUnmount,
  watch,
  nextTick,
  shallowRef,
  readonly,
} from 'vue'

import { useImagePreview } from './composables/useImagePreview'
import { extractRelativePath, getFullImageUrl } from './ImageResourceManager'

import type { NodeViewProps } from '@tiptap/core'
import type { Node as ProseMirrorNode } from '@tiptap/pm/model'
import type { Decoration } from '@tiptap/pm/view'
import type { Editor } from '@tiptap/vue-3'

// 导入样式
import './styles/image-node-view.scss'

// 组件属性定义 - 扩展 Tiptap 的 NodeViewProps
interface Props extends NodeViewProps {
  useThumbnail?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  useThumbnail: true,
})

// 模板引用
const imageWrapper = ref()
const imageElement = ref<HTMLImageElement>()

// 基础状态 - 使用 shallowRef 优化性能
const isEditable = computed(() => props.editor.isEditable)
const isSelected = computed(() => props.selected)
const isResizing = shallowRef(false)
const aspectRatio = shallowRef(1)

// 图片相关状态 - 使用 shallowRef 优化性能
const relativeSrc = computed(() => extractRelativePath(props.node.attrs.src))
const imageSrc = computed(() => getFullImageUrl(relativeSrc.value, props.useThumbnail))

// 调整大小状态 - 使用 shallowRef 优化性能
const startX = shallowRef(0)
const startY = shallowRef(0)
const startWidth = shallowRef(0)
const startHeight = shallowRef(0)
const currentWidth = shallowRef(0)
const currentHeight = shallowRef(0)
const currentHandle = shallowRef<string | null>(null)
const resizeWidth = shallowRef('')
const resizeHeight = shallowRef('')

// 控制点定义 - 使用 readonly 优化性能
const cornerHandles = readonly(['top-left', 'top-right', 'bottom-left', 'bottom-right'])
const edgeHandles = readonly(['top', 'right', 'bottom', 'left'])

// DOM 观察器
let mutationObserver: MutationObserver | null = null

// 缓存计算结果 - 优化控制点样式计算
const handleStylesCache = new Map<string, Record<string, any>>()

// 计算样式 - 使用 shallowReactive 优化性能
const wrapperStyles = computed(() => ({
  display: 'inline-flex' as const,
  alignItems: 'center' as const,
  justifyContent: 'center' as const,
  position: 'relative' as const,
  margin: '0',
  padding: '0',
  verticalAlign: 'baseline' as const,
  lineHeight: '1',
  transform: 'translateY(0)',
  transition: 'none',
  zIndex: '1',
  willChange: 'transform' as const,
  whiteSpace: 'nowrap' as const,
  boxSizing: 'border-box' as const,
  width: 'fit-content' as const,
  maxWidth: '100%',
  border: '0',
}))

const imageStyles = computed(() => ({
  display: 'block' as const,
  maxWidth: '100%',
  margin: '0',
  padding: '0',
  verticalAlign: 'baseline' as const,
  transform: 'translateY(0)',
  transition: 'none',
  willChange: 'transform' as const,
  lineHeight: 'normal' as const,
  fontSize: 'inherit' as const,
  width: isResizing.value ? resizeWidth.value : props.node.attrs.width || '',
  height: isResizing.value ? resizeHeight.value : props.node.attrs.height || '',
}))

// 计算控制点样式的核心函数
const calculateHandleStyle = (position: string) => {
  const baseStyle = {
    position: 'absolute' as const,
    background: '#2d8cf0',
    border: '1px solid white',
    borderRadius: '50%',
    zIndex: '100',
    display: isSelected.value && isEditable.value ? 'block' : 'none',
    visibility: (isSelected.value && isEditable.value ? 'visible' : 'hidden') as
      | 'visible'
      | 'hidden',
    opacity: isSelected.value && isEditable.value ? '1' : '0',
  }

  // 角控制点样式
  if (cornerHandles.includes(position)) {
    const cornerStyles = {
      width: '8px',
      height: '8px',
      cursor: getCursorForHandle(position),
    }

    const positionStyles: Record<string, any> = {
      'top-left': { top: '-4px', left: '-4px' },
      'top-right': { top: '-4px', right: '-4px' },
      'bottom-left': { bottom: '-4px', left: '-4px' },
      'bottom-right': { bottom: '-4px', right: '-4px' },
    }

    return { ...baseStyle, ...cornerStyles, ...positionStyles[position] }
  }
  // 边控制点样式
  else if (edgeHandles.includes(position)) {
    const edgeStyles = {
      borderRadius: '2px',
      cursor: getCursorForHandle(position),
    }

    const positionStyles: Record<string, any> = {
      top: {
        top: '-4px',
        left: 'calc(50% - 6px)',
        width: '12px',
        height: '4px',
      },
      right: {
        right: '-4px',
        top: 'calc(50% - 6px)',
        width: '4px',
        height: '12px',
      },
      bottom: {
        bottom: '-4px',
        left: 'calc(50% - 6px)',
        width: '12px',
        height: '4px',
      },
      left: {
        left: '-4px',
        top: 'calc(50% - 6px)',
        width: '4px',
        height: '12px',
      },
    }

    return { ...baseStyle, ...edgeStyles, ...positionStyles[position] }
  }

  return baseStyle
}

// 获取控制点样式 - 优化缓存策略，缩放时不使用缓存
const getHandleStyle = (position: string) => {
  // 缩放时不使用缓存，确保实时更新
  if (isResizing.value) {
    return calculateHandleStyle(position)
  }

  // 检查缓存
  const cacheKey = `${position}-${isSelected.value}-${isEditable.value}`
  if (handleStylesCache.has(cacheKey)) {
    return handleStylesCache.get(cacheKey)!
  }

  // 计算并缓存结果
  const result = calculateHandleStyle(position)
  handleStylesCache.set(cacheKey, result)
  return result
}

// 获取控制点光标样式 - 使用静态对象优化性能
const cursorMap = Object.freeze({
  'top-left': 'nw-resize',
  'top-right': 'ne-resize',
  'bottom-left': 'sw-resize',
  'bottom-right': 'se-resize',
  top: 'n-resize',
  right: 'e-resize',
  bottom: 's-resize',
  left: 'w-resize',
})

const getCursorForHandle = (position: string) => {
  return cursorMap[position as keyof typeof cursorMap] || 'pointer'
}

// 判断是否为中心控制点 - 使用 Set 优化性能
const edgeHandleSet = new Set(edgeHandles)
const isCenterHandle = (position: string) => {
  return edgeHandleSet.has(position)
}

// 图片加载处理 - 使用防抖优化性能
let loadTimer: number | null = null
const handleImageLoad = () => {
  if (loadTimer) {
    clearTimeout(loadTimer)
  }

  loadTimer = window.setTimeout(() => {
    if (imageElement.value) {
      const rect = imageElement.value.getBoundingClientRect()
      currentWidth.value = rect.width
      currentHeight.value = rect.height
      aspectRatio.value = rect.width / rect.height
    }
  }, 16) // 约 60fps
}

// 图片加载错误处理
const handleImageError = () => {
  console.warn('图片加载失败:', imageSrc.value)
}

// 包装器点击处理
const handleWrapperClick = (e: MouseEvent) => {
  // 防止事件传播到编辑器
  e.stopPropagation()
}

// 图片点击处理
const handleImageClick = (e: MouseEvent) => {
  e.preventDefault()
  e.stopPropagation()

  if (!isEditable.value) {
    // 非编辑模式下点击预览
    handlePreview(e)
  }
}

// 图片双击处理
const handleImageDoubleClick = (e: MouseEvent) => {
  e.preventDefault()
  e.stopPropagation()

  if (isEditable.value) {
    // 编辑模式下双击预览
    handlePreview(e)
  }
}

// 图片预览处理
const handlePreview = (e: MouseEvent | TouchEvent) => {
  e.preventDefault()
  e.stopPropagation()

  const { showImagePreview } = useImagePreview()

  // 检查当前图片是否已经是原图
  const currentImageSrc = imageElement.value?.src || imageSrc.value
  const isCurrentImageOriginal = !currentImageSrc.includes('/thumbnail')

  // 如果当前图片已经是原图，则直接使用当前图片作为预览源
  const previewSrc = isCurrentImageOriginal ? currentImageSrc : imageSrc.value
  const previewOriginalSrc = isCurrentImageOriginal
    ? relativeSrc.value.replace('/thumbnail', '')
    : relativeSrc.value

  showImagePreview({
    src: previewSrc,
    originalSrc: previewOriginalSrc,
    alt: props.node.attrs.alt || '图片预览',
    useThumbnail: props.useThumbnail && !isCurrentImageOriginal,
    onOriginalLoaded: (originalUrl: string) => {
      if (imageElement.value && relativeSrc.value.includes('/thumbnail')) {
        imageElement.value.src = originalUrl
        const newOriginalSrc = relativeSrc.value.replace('/thumbnail', '')
        imageElement.value.dataset.originalSrc = newOriginalSrc

        if (typeof props.getPos === 'function' && !props.editor.isEditable) {
          try {
            props.editor.commands.updateAttributes('image', {
              src: newOriginalSrc,
            })
          } catch (error) {
            console.warn('更新图片属性失败:', error)
          }
        }
      }
    },
  })
}

// 调整大小开始处理
const handleResizeStart = (e: MouseEvent, position: string) => {
  e.preventDefault()
  e.stopPropagation()

  if (!isEditable.value) return
  if (typeof props.getPos !== 'function') return

  // 确保图片节点被选中
  const pos = props.getPos()
  props.editor.commands.setNodeSelection(pos)

  // 记录开始调整的状态
  isResizing.value = true
  currentHandle.value = position

  // 获取图片当前尺寸
  if (imageElement.value) {
    const rect = imageElement.value.getBoundingClientRect()
    startWidth.value = rect.width
    startHeight.value = rect.height
    currentWidth.value = rect.width
    currentHeight.value = rect.height
    aspectRatio.value = startWidth.value / startHeight.value

    // 初始化调整尺寸
    resizeWidth.value = `${rect.width}px`
    resizeHeight.value = `${rect.height}px`
  }

  // 记录鼠标起始位置
  startX.value = e.clientX
  startY.value = e.clientY

  // 添加全局事件监听 - 使用 passive: false 确保事件能被正确阻止
  document.addEventListener('mousemove', handleResize, { passive: false })
  document.addEventListener('mouseup', handleResizeEnd, { passive: true })
}

// 触摸开始处理
const handleTouchStart = (e: TouchEvent, position: string) => {
  if (!e.touches || e.touches.length === 0) return

  const touch = e.touches[0]
  const touchEvent = {
    clientX: touch.clientX,
    clientY: touch.clientY,
    preventDefault: () => {},
    stopPropagation: () => {},
  } as unknown as MouseEvent

  handleResizeStart(touchEvent, position)

  // 防止页面滚动
  requestAnimationFrame(() => {
    document.body.style.overflow = 'hidden'
  })

  // 添加触摸事件监听
  document.addEventListener('touchmove', handleTouchResize, { passive: false })
  document.addEventListener('touchend', handleTouchEnd, { passive: true })
  document.addEventListener('touchcancel', handleTouchEnd, { passive: true })
}

// 调整大小处理 - 立即更新提供最佳跟随体验
const handleResize = (e: MouseEvent) => {
  if (!isResizing.value || !currentHandle.value) return

  e.preventDefault()

  // 立即计算新尺寸，无延迟
  const deltaX = e.clientX - startX.value
  const deltaY = e.clientY - startY.value

  const position = currentHandle.value
  let newWidth = startWidth.value
  let newHeight = startHeight.value

  const keepRatio = e.shiftKey
  const freeTransform = e.altKey

  // 根据控制点位置调整尺寸
  switch (position) {
    case 'top-left':
      newWidth = startWidth.value - deltaX
      newHeight = startHeight.value - deltaY
      break
    case 'top-right':
      newWidth = startWidth.value + deltaX
      newHeight = startHeight.value - deltaY
      break
    case 'bottom-left':
      newWidth = startWidth.value - deltaX
      newHeight = startHeight.value + deltaY
      break
    case 'bottom-right':
      newWidth = startWidth.value + deltaX
      newHeight = startHeight.value + deltaY
      break
    case 'top':
      newHeight = startHeight.value - deltaY
      if (keepRatio) newWidth = newHeight * aspectRatio.value
      break
    case 'right':
      newWidth = startWidth.value + deltaX
      if (keepRatio) newHeight = newWidth / aspectRatio.value
      break
    case 'bottom':
      newHeight = startHeight.value + deltaY
      if (keepRatio) newWidth = newHeight * aspectRatio.value
      break
    case 'left':
      newWidth = startWidth.value - deltaX
      if (keepRatio) newHeight = newWidth / aspectRatio.value
      break
  }

  // 保持宽高比处理
  if (keepRatio && !isCenterHandle(position) && !freeTransform) {
    if (Math.abs(deltaX) > Math.abs(deltaY)) {
      newHeight = newWidth / aspectRatio.value
    } else {
      newWidth = newHeight * aspectRatio.value
    }
  }

  // 确保尺寸不小于最小值
  const minSize = 20
  newWidth = Math.max(newWidth, minSize)
  newHeight = Math.max(newHeight, minSize)

  // 立即更新当前尺寸
  currentWidth.value = newWidth
  currentHeight.value = newHeight

  // 立即应用新尺寸到调整状态
  resizeWidth.value = `${newWidth}px`
  resizeHeight.value = `${newHeight}px`
}

// 触摸调整大小处理 - 立即更新提供最佳跟随体验
const handleTouchResize = (e: TouchEvent) => {
  if (!isResizing.value || !currentHandle.value) return
  if (!e.touches || e.touches.length === 0) return

  e.preventDefault()

  const touch = e.touches[0]
  const touchCount = e.touches.length

  // 立即创建触摸事件对象并处理
  const touchEvent = {
    clientX: touch.clientX,
    clientY: touch.clientY,
    preventDefault: () => {},
    altKey: touchCount === 2, // 两指触摸模拟Alt键
    shiftKey: touchCount === 3, // 三指触摸模拟Shift键
  } as unknown as MouseEvent

  // 立即调用resize处理函数
  handleResize(touchEvent)
}

// 触摸结束处理
const handleTouchEnd = () => {
  handleResizeEnd()
}

// 调整大小结束处理
const handleResizeEnd = () => {
  if (!isResizing.value) return

  // 移除全局事件监听
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', handleResizeEnd)
  document.removeEventListener('touchmove', handleTouchResize)
  document.removeEventListener('touchend', handleTouchEnd)
  document.removeEventListener('touchcancel', handleTouchEnd)

  // 恢复页面滚动
  document.body.style.overflow = ''

  // 重置调整状态
  isResizing.value = false
  currentHandle.value = null
  resizeWidth.value = ''
  resizeHeight.value = ''

  // 更新节点属性
  if (typeof props.getPos === 'function' && imageElement.value) {
    const width = Math.round(currentWidth.value)
    const height = Math.round(currentHeight.value)

    props.editor.commands.updateAttributes('image', {
      width: `${width}px`,
      height: `${height}px`,
    })
  }
}

// 初始化 MutationObserver - 优化性能
const initMutationObserver = () => {
  // 获取 NodeViewWrapper 的 DOM 元素
  const wrapperElement = imageWrapper.value?.$el || imageWrapper.value
  if (!wrapperElement) return

  if (mutationObserver) {
    mutationObserver.disconnect()
  }

  let observerTimer: number | null = null

  mutationObserver = new MutationObserver((mutations) => {
    if (observerTimer) {
      clearTimeout(observerTimer)
    }

    observerTimer = window.setTimeout(() => {
      const relevantMutations = mutations.filter(
        (mutation) => mutation.type === 'attributes' && mutation.attributeName === 'class',
      )

      if (relevantMutations.length === 0) return

      const target = relevantMutations[0].target as HTMLElement
      const wasSelected = isSelected.value
      const currentlySelected = target.classList.contains('ProseMirror-selectednode')

      // 如果是新选中的图片，添加视觉反馈
      if (!wasSelected && currentlySelected && isEditable.value) {
        wrapperElement?.scrollIntoView({
          behavior: 'smooth',
          block: 'center',
        })

        // 添加短暂高亮效果
        if (wrapperElement) {
          wrapperElement.animate(
            [
              { outline: '2px solid #2d8cf0', boxShadow: '0 0 0 3px rgba(45, 140, 240, 0.2)' },
              { outline: '2px solid #2d8cf0', boxShadow: '0 0 0 5px rgba(45, 140, 240, 0.5)' },
              { outline: '2px solid #2d8cf0', boxShadow: '0 0 0 3px rgba(45, 140, 240, 0.2)' },
            ],
            {
              duration: 600,
              easing: 'ease-in-out',
            },
          )
        }
      }

      // 非编辑模式下移除选中状态
      if (!isEditable.value && currentlySelected) {
        requestAnimationFrame(() => {
          target.classList.remove('ProseMirror-selectednode')
        })
      }
    }, 50)
  })

  mutationObserver.observe(wrapperElement, {
    attributes: true,
    attributeFilter: ['class'],
    subtree: false,
  })
}

// 监听编辑模式变化
watch(
  () => props.editor.isEditable,
  (newValue) => {
    if (!newValue) {
      const wrapperElement = imageWrapper.value?.$el || imageWrapper.value
      if (wrapperElement) {
        wrapperElement.classList.remove('ProseMirror-selectednode')
      }
    }
    // 清理缓存
    handleStylesCache.clear()
  },
)

// 监听选中状态变化，清理缓存
watch(
  () => isSelected.value,
  () => {
    handleStylesCache.clear()
  },
)

// 监听编辑器更新事件
const handleEditorUpdate = () => {
  if (!props.editor.isEditable) {
    const wrapperElement = imageWrapper.value?.$el || imageWrapper.value
    if (wrapperElement) {
      wrapperElement.classList.remove('ProseMirror-selectednode')
    }
  }
}

// 组件挂载
onMounted(() => {
  nextTick(() => {
    initMutationObserver()
    handleImageLoad()

    // 监听编辑器更新
    props.editor.on('update', handleEditorUpdate)

    // 确保在非编辑模式下立即移除选中样式
    if (!isEditable.value) {
      const wrapperElement = imageWrapper.value?.$el || imageWrapper.value
      if (wrapperElement) {
        wrapperElement.classList.remove('ProseMirror-selectednode')
      }
    }
  })
})

// 组件卸载
onBeforeUnmount(() => {
  // 清理定时器
  if (loadTimer) {
    clearTimeout(loadTimer)
    loadTimer = null
  }

  if (mutationObserver) {
    mutationObserver.disconnect()
    mutationObserver = null
  }

  // 移除编辑器事件监听
  props.editor.off('update', handleEditorUpdate)

  // 清理全局事件监听
  document.removeEventListener('mousemove', handleResize)
  document.removeEventListener('mouseup', handleResizeEnd)
  document.removeEventListener('touchmove', handleTouchResize)
  document.removeEventListener('touchend', handleTouchEnd)
  document.removeEventListener('touchcancel', handleTouchEnd)

  // 恢复页面滚动
  document.body.style.overflow = ''

  // 清理缓存
  handleStylesCache.clear()
})

// 暴露更新方法给父组件
const updateNode = (updatedNode: ProseMirrorNode) => {
  // 节点更新时的处理逻辑
  if (updatedNode.attrs.src !== props.node.attrs.src) {
    // src 变化时会自动通过 computed 更新
  }
  if (updatedNode.attrs.width !== props.node.attrs.width) {
    if (imageElement.value) {
      imageElement.value.style.width = updatedNode.attrs.width || ''
    }
  }
  if (updatedNode.attrs.height !== props.node.attrs.height) {
    if (imageElement.value) {
      imageElement.value.style.height = updatedNode.attrs.height || ''
    }
  }
}

// 暴露给父组件的方法
defineExpose({
  updateNode,
})
</script>

<style lang="scss" scoped>
@use './styles/image-node-view.scss';

// 添加 Vue 组件特有的样式
.image-wrapper {
  // 确保控制点在选中时可见
  &.ProseMirror-selectednode {
    .resize-handle {
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
    }
  }

  // 调整中的样式
  &.resizing {
    .resize-handle {
      display: block !important;
      visibility: visible !important;
      opacity: 1 !important;
    }
  }

  // 非编辑模式下隐藏控制点
  &.readonly-image .resize-handle {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
  }
}
</style>
