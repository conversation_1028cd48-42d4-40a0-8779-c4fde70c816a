import type { Editor } from '@tiptap/core'
import type { Node as ProseMirrorNode } from '@tiptap/pm/model'
import type { ClickMenuItem, MenuItemGroup } from './types'
import { ContentTransformationUtils } from './utils/contentTransformation'

/**
 * Registry for managing click menu items with filtering and grouping capabilities
 */
export class MenuItemRegistry {
  private items: Map<string, ClickMenuItem> = new Map()
  private groups: Map<string, MenuItemGroup> = new Map()

  /**
   * Register a menu item
   */
  register(item: ClickMenuItem): void {
    this.items.set(item.id, item)
    
    // Add to group if specified
    if (item.group) {
      this.addToGroup(item)
    }
  }

  /**
   * Register multiple menu items
   */
  registerMany(items: ClickMenuItem[]): void {
    items.forEach(item => this.register(item))
  }

  /**
   * Unregister a menu item
   */
  unregister(itemId: string): boolean {
    const item = this.items.get(itemId)
    if (item && item.group) {
      this.removeFromGroup(item)
    }
    return this.items.delete(itemId)
  }

  /**
   * Get all registered menu items
   */
  getAll(): ClickMenuItem[] {
    return Array.from(this.items.values())
  }

  /**
   * Get menu item by ID
   */
  getById(itemId: string): ClickMenuItem | undefined {
    return this.items.get(itemId)
  }

  /**
   * Get filtered menu items based on node context
   */
  getFilteredItems(node: ProseMirrorNode): ClickMenuItem[] {
    return Array.from(this.items.values())
      .filter(item => item.isVisible(node))
      .sort((a, b) => {
        // Sort by group priority, then by registration order
        const groupA = a.group ? this.groups.get(a.group)?.priority ?? 0 : 0
        const groupB = b.group ? this.groups.get(b.group)?.priority ?? 0 : 0
        return groupB - groupA
      })
  }

  /**
   * Get menu items grouped by their group property
   */
  getGroupedItems(node: ProseMirrorNode): MenuItemGroup[] {
    const filteredItems = this.getFilteredItems(node)
    const groupMap = new Map<string, ClickMenuItem[]>()
    
    // Group items
    filteredItems.forEach(item => {
      const groupId = item.group || 'default'
      if (!groupMap.has(groupId)) {
        groupMap.set(groupId, [])
      }
      groupMap.get(groupId)!.push(item)
    })

    // Convert to MenuItemGroup array
    return Array.from(groupMap.entries()).map(([groupId, items]) => {
      const group = this.groups.get(groupId)
      return {
        id: groupId,
        label: group?.label || groupId,
        items,
        priority: group?.priority || 0
      }
    }).sort((a, b) => b.priority - a.priority)
  }

  /**
   * Register a menu item group
   */
  registerGroup(group: Omit<MenuItemGroup, 'items'>): void {
    this.groups.set(group.id, { ...group, items: [] })
  }

  /**
   * Get all registered groups
   */
  getGroups(): MenuItemGroup[] {
    return Array.from(this.groups.values())
  }

  /**
   * Clear all registered items and groups
   */
  clear(): void {
    this.items.clear()
    this.groups.clear()
  }

  /**
   * Get visible items for a node (alias for getFilteredItems)
   */
  getVisibleItems(node: ProseMirrorNode): ClickMenuItem[] {
    return this.getFilteredItems(node)
  }

  /**
   * Get enabled items for a node
   */
  getEnabledItems(node: ProseMirrorNode): ClickMenuItem[] {
    return this.getFilteredItems(node).filter(item => item.isEnabled(node))
  }

  /**
   * Remove an item by ID (alias for unregister)
   */
  remove(itemId: string): boolean {
    return this.unregister(itemId)
  }

  /**
   * Execute an action for a menu item
   */
  executeAction(itemId: string, editor: Editor, node: ProseMirrorNode, pos: number): void {
    const item = this.getById(itemId)
    if (item && item.isEnabled(node)) {
      try {
        item.action(editor, node, pos)
      } catch (error) {
        console.error(`Failed to execute action for item ${itemId}:`, error)
      }
    }
  }

  private addToGroup(item: ClickMenuItem): void {
    if (!item.group) return
    
    let group = this.groups.get(item.group)
    if (!group) {
      // Create default group if it doesn't exist
      group = {
        id: item.group,
        label: item.group,
        items: [],
        priority: 0
      }
      this.groups.set(item.group, group)
    }
    
    // Add item to group if not already present
    if (!group.items.find(i => i.id === item.id)) {
      group.items.push(item)
    }
  }

  private removeFromGroup(item: ClickMenuItem): void {
    if (!item.group) return
    
    const group = this.groups.get(item.group)
    if (group) {
      group.items = group.items.filter(i => i.id !== item.id)
    }
  }
}

/**
 * Global menu item registry instance
 */
export const menuItemRegistry = new MenuItemRegistry()

/**
 * Create node-specific menu items for different content types
 */
function createNodeSpecificMenuItems(): ClickMenuItem[] {
  return [
    // Paragraph-specific items
    {
      id: 'paragraph.indent',
      label: '增加缩进',
      icon: '→',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          // Add indentation to paragraph
          const tr = editor.state.tr
          const attrs = { ...node.attrs, indent: (node.attrs.indent || 0) + 1 }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to indent paragraph:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'paragraph',
      isEnabled: (node: ProseMirrorNode) => (node.attrs.indent || 0) < 6,
      group: 'paragraph'
    },
    {
      id: 'paragraph.outdent',
      label: '减少缩进',
      icon: '←',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, indent: Math.max(0, (node.attrs.indent || 0) - 1) }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to outdent paragraph:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'paragraph',
      isEnabled: (node: ProseMirrorNode) => (node.attrs.indent || 0) > 0,
      group: 'paragraph'
    },
    {
      id: 'paragraph.alignLeft',
      label: '左对齐',
      icon: '⬅',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.setTextAlignment(editor, node, pos, 'left')
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'paragraph',
      isEnabled: (node: ProseMirrorNode) => node.attrs.textAlign !== 'left',
      group: 'paragraph'
    },
    {
      id: 'paragraph.alignCenter',
      label: '居中对齐',
      icon: '⬌',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.setTextAlignment(editor, node, pos, 'center')
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'paragraph',
      isEnabled: (node: ProseMirrorNode) => node.attrs.textAlign !== 'center',
      group: 'paragraph'
    },
    {
      id: 'paragraph.alignRight',
      label: '右对齐',
      icon: '➡',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.setTextAlignment(editor, node, pos, 'right')
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'paragraph',
      isEnabled: (node: ProseMirrorNode) => node.attrs.textAlign !== 'right',
      group: 'paragraph'
    },
    {
      id: 'paragraph.alignJustify',
      label: '两端对齐',
      icon: '⬌',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.setTextAlignment(editor, node, pos, 'justify')
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'paragraph',
      isEnabled: (node: ProseMirrorNode) => node.attrs.textAlign !== 'justify',
      group: 'paragraph'
    },
    {
      id: 'paragraph.split',
      label: '分割段落',
      icon: '✂',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.splitNode(editor, node, pos)
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'paragraph',
      isEnabled: (node: ProseMirrorNode) => node.textContent.length > 10,
      group: 'paragraph'
    },

    // Heading-specific items
    {
      id: 'heading.increaseLevel',
      label: '降低标题级别',
      icon: 'H↓',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const currentLevel = node.attrs.level || 1
          const newLevel = Math.min(6, currentLevel + 1) as 1 | 2 | 3 | 4 | 5 | 6
          const tr = editor.state.tr
          const attrs = { ...node.attrs, level: newLevel }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to increase heading level:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'heading',
      isEnabled: (node: ProseMirrorNode) => (node.attrs.level || 1) < 6,
      group: 'heading'
    },
    {
      id: 'heading.decreaseLevel',
      label: '提高标题级别',
      icon: 'H↑',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const currentLevel = node.attrs.level || 1
          const newLevel = Math.max(1, currentLevel - 1) as 1 | 2 | 3 | 4 | 5 | 6
          const tr = editor.state.tr
          const attrs = { ...node.attrs, level: newLevel }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to decrease heading level:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'heading',
      isEnabled: (node: ProseMirrorNode) => (node.attrs.level || 1) > 1,
      group: 'heading'
    },
    {
      id: 'heading.addId',
      label: '添加锚点ID',
      icon: '#',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const id = node.textContent.toLowerCase()
            .replace(/[^\w\s-]/g, '')
            .replace(/\s+/g, '-')
            .trim()
          
          const tr = editor.state.tr
          const attrs = { ...node.attrs, id }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to add heading ID:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'heading',
      isEnabled: (node: ProseMirrorNode) => !node.attrs.id && node.textContent.length > 0,
      group: 'heading'
    },
    {
      id: 'heading.removeId',
      label: '移除锚点ID',
      icon: '#⌫',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs }
          delete attrs.id
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to remove heading ID:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'heading',
      isEnabled: (node: ProseMirrorNode) => !!node.attrs.id,
      group: 'heading'
    },
    {
      id: 'heading.alignLeft',
      label: '左对齐',
      icon: '⬅',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.setTextAlignment(editor, node, pos, 'left')
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'heading',
      isEnabled: (node: ProseMirrorNode) => node.attrs.textAlign !== 'left',
      group: 'heading'
    },
    {
      id: 'heading.alignCenter',
      label: '居中对齐',
      icon: '⬌',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.setTextAlignment(editor, node, pos, 'center')
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'heading',
      isEnabled: (node: ProseMirrorNode) => node.attrs.textAlign !== 'center',
      group: 'heading'
    },
    {
      id: 'heading.alignRight',
      label: '右对齐',
      icon: '➡',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.setTextAlignment(editor, node, pos, 'right')
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'heading',
      isEnabled: (node: ProseMirrorNode) => node.attrs.textAlign !== 'right',
      group: 'heading'
    },
    {
      id: 'heading.toggleCollapsible',
      label: '切换折叠',
      icon: '📁',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.toggleNodeAttribute(editor, node, pos, 'collapsible', true)
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'heading',
      isEnabled: () => true,
      group: 'heading'
    },

    // List-specific items
    {
      id: 'list.increaseIndent',
      label: '增加列表缩进',
      icon: '→•',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          editor.chain().focus().setTextSelection(pos).sinkListItem('listItem').run()
        } catch (error) {
          console.error('Failed to increase list indent:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'listItem',
      isEnabled: (node: ProseMirrorNode) => true,
      group: 'list'
    },
    {
      id: 'list.decreaseIndent',
      label: '减少列表缩进',
      icon: '←•',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          editor.chain().focus().setTextSelection(pos).liftListItem('listItem').run()
        } catch (error) {
          console.error('Failed to decrease list indent:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'listItem',
      isEnabled: (node: ProseMirrorNode) => true,
      group: 'list'
    },
    {
      id: 'list.toggleType',
      label: '切换列表类型',
      icon: '1.↔•',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          // Find parent list to determine current type
          const $pos = editor.state.doc.resolve(pos)
          let listParent = null
          for (let i = $pos.depth; i > 0; i--) {
            const node = $pos.node(i)
            if (node.type.name === 'bulletList' || node.type.name === 'orderedList') {
              listParent = node
              break
            }
          }
          
          if (listParent) {
            if (listParent.type.name === 'bulletList') {
              editor.chain().focus().setTextSelection(pos).toggleList('orderedList', 'listItem').run()
            } else {
              editor.chain().focus().setTextSelection(pos).toggleList('bulletList', 'listItem').run()
            }
          }
        } catch (error) {
          console.error('Failed to toggle list type:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'listItem',
      isEnabled: (node: ProseMirrorNode) => true,
      group: 'list'
    },
    {
      id: 'list.addCheckbox',
      label: '添加复选框',
      icon: '☐',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, checked: false }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to add checkbox:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'listItem',
      isEnabled: (node: ProseMirrorNode) => node.attrs.checked === undefined,
      group: 'list'
    },
    {
      id: 'list.removeCheckbox',
      label: '移除复选框',
      icon: '☐⌫',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs }
          delete attrs.checked
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to remove checkbox:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'listItem',
      isEnabled: (node: ProseMirrorNode) => node.attrs.checked !== undefined,
      group: 'list'
    },
    {
      id: 'list.toggleCheck',
      label: '切换选中状态',
      icon: '☑',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, checked: !node.attrs.checked }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to toggle checkbox:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'listItem',
      isEnabled: (node: ProseMirrorNode) => node.attrs.checked !== undefined,
      group: 'list'
    },
    {
      id: 'list.convertToParagraph',
      label: '转换为段落',
      icon: 'P',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.transformListItemToParagraph(editor, node, pos)
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'listItem',
      isEnabled: () => true,
      group: 'list'
    },
    {
      id: 'list.splitItem',
      label: '分割列表项',
      icon: '✂',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.splitNode(editor, node, pos)
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'listItem',
      isEnabled: (node: ProseMirrorNode) => node.textContent.length > 5,
      group: 'list'
    },

    // Code block-specific items
    {
      id: 'codeBlock.changeLanguage',
      label: '更改语言',
      icon: '</>', 
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          // This would typically open a language selector
          // For now, we'll cycle through common languages
          const languages = ['javascript', 'typescript', 'python', 'java', 'css', 'html', 'json', 'markdown', 'sql', 'bash', 'yaml', 'xml', 'php', 'go', 'rust', 'cpp', 'csharp']
          const currentLang = node.attrs.language || 'javascript'
          const currentIndex = languages.indexOf(currentLang)
          const nextLang = languages[(currentIndex + 1) % languages.length]
          
          const tr = editor.state.tr
          const attrs = { ...node.attrs, language: nextLang }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to change code language:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'codeBlock',
      isEnabled: () => true,
      group: 'codeBlock'
    },
    {
      id: 'codeBlock.setJavaScript',
      label: '设为JavaScript',
      icon: 'JS',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, language: 'javascript' }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to set JavaScript language:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'codeBlock',
      isEnabled: (node: ProseMirrorNode) => node.attrs.language !== 'javascript',
      group: 'codeBlock'
    },
    {
      id: 'codeBlock.setTypeScript',
      label: '设为TypeScript',
      icon: 'TS',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, language: 'typescript' }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to set TypeScript language:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'codeBlock',
      isEnabled: (node: ProseMirrorNode) => node.attrs.language !== 'typescript',
      group: 'codeBlock'
    },
    {
      id: 'codeBlock.setPython',
      label: '设为Python',
      icon: 'PY',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, language: 'python' }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to set Python language:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'codeBlock',
      isEnabled: (node: ProseMirrorNode) => node.attrs.language !== 'python',
      group: 'codeBlock'
    },
    {
      id: 'codeBlock.copy',
      label: '复制代码',
      icon: '📋',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          navigator.clipboard.writeText(node.textContent)
        } catch (error) {
          console.error('Failed to copy code:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'codeBlock',
      isEnabled: (node: ProseMirrorNode) => node.textContent.length > 0,
      group: 'codeBlock'
    },
    {
      id: 'codeBlock.format',
      label: '格式化代码',
      icon: '✨',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          // Basic formatting - add proper indentation
          const lines = node.textContent.split('\n')
          let indentLevel = 0
          const formatted = lines.map(line => {
            const trimmed = line.trim()
            if (trimmed.endsWith('{') || trimmed.endsWith('[') || trimmed.endsWith('(')) {
              const result = '  '.repeat(indentLevel) + trimmed
              indentLevel++
              return result
            } else if (trimmed.startsWith('}') || trimmed.startsWith(']') || trimmed.startsWith(')') || trimmed.startsWith('</')) {
              indentLevel = Math.max(0, indentLevel - 1)
              return '  '.repeat(indentLevel) + trimmed
            } else {
              return '  '.repeat(indentLevel) + trimmed
            }
          }).join('\n')
          
          const tr = editor.state.tr
          tr.replaceWith(pos + 1, pos + node.nodeSize - 1, editor.schema.text(formatted))
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to format code:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'codeBlock',
      isEnabled: (node: ProseMirrorNode) => node.textContent.length > 0,
      group: 'codeBlock'
    },
    {
      id: 'codeBlock.toggleLineNumbers',
      label: '切换行号',
      icon: '#',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, showLineNumbers: !node.attrs.showLineNumbers }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to toggle line numbers:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'codeBlock',
      isEnabled: () => true,
      group: 'codeBlock'
    },
    {
      id: 'codeBlock.toggleWrap',
      label: '切换换行',
      icon: '↩',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, wrap: !node.attrs.wrap }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to toggle line wrap:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'codeBlock',
      isEnabled: () => true,
      group: 'codeBlock'
    },
    {
      id: 'codeBlock.addTitle',
      label: '添加标题',
      icon: '🏷',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, title: node.attrs.title || '代码块标题' }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to add code block title:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'codeBlock',
      isEnabled: (node: ProseMirrorNode) => !node.attrs.title,
      group: 'codeBlock'
    },
    {
      id: 'codeBlock.removeTitle',
      label: '移除标题',
      icon: '🏷⌫',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs }
          delete attrs.title
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to remove code block title:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'codeBlock',
      isEnabled: (node: ProseMirrorNode) => !!node.attrs.title,
      group: 'codeBlock'
    },
    {
      id: 'codeBlock.toggleHighlight',
      label: '切换语法高亮',
      icon: '🌈',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.toggleNodeAttribute(editor, node, pos, 'highlight', true)
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'codeBlock',
      isEnabled: () => true,
      group: 'codeBlock'
    },

    // Blockquote-specific items
    {
      id: 'blockquote.changeStyle',
      label: '更改引用样式',
      icon: '🎨',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const styles = ['default', 'warning', 'info', 'success', 'error']
          const currentStyle = node.attrs.style || 'default'
          const currentIndex = styles.indexOf(currentStyle)
          const nextStyle = styles[(currentIndex + 1) % styles.length]
          
          const tr = editor.state.tr
          const attrs = { ...node.attrs, style: nextStyle }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to change blockquote style:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'blockquote',
      isEnabled: () => true,
      group: 'blockquote'
    },
    {
      id: 'blockquote.setDefault',
      label: '默认样式',
      icon: '📄',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, style: 'default' }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to set default style:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'blockquote',
      isEnabled: (node: ProseMirrorNode) => node.attrs.style !== 'default',
      group: 'blockquote'
    },
    {
      id: 'blockquote.setWarning',
      label: '警告样式',
      icon: '⚠️',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, style: 'warning' }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to set warning style:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'blockquote',
      isEnabled: (node: ProseMirrorNode) => node.attrs.style !== 'warning',
      group: 'blockquote'
    },
    {
      id: 'blockquote.setInfo',
      label: '信息样式',
      icon: 'ℹ️',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, style: 'info' }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to set info style:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'blockquote',
      isEnabled: (node: ProseMirrorNode) => node.attrs.style !== 'info',
      group: 'blockquote'
    },
    {
      id: 'blockquote.setSuccess',
      label: '成功样式',
      icon: '✅',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, style: 'success' }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to set success style:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'blockquote',
      isEnabled: (node: ProseMirrorNode) => node.attrs.style !== 'success',
      group: 'blockquote'
    },
    {
      id: 'blockquote.setError',
      label: '错误样式',
      icon: '❌',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, style: 'error' }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to set error style:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'blockquote',
      isEnabled: (node: ProseMirrorNode) => node.attrs.style !== 'error',
      group: 'blockquote'
    },
    {
      id: 'blockquote.addCitation',
      label: '添加引用来源',
      icon: '📝',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, citation: node.attrs.citation || '引用来源' }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to add citation:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'blockquote',
      isEnabled: (node: ProseMirrorNode) => !node.attrs.citation,
      group: 'blockquote'
    },
    {
      id: 'blockquote.removeCitation',
      label: '移除引用来源',
      icon: '📝⌫',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs }
          delete attrs.citation
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to remove citation:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'blockquote',
      isEnabled: (node: ProseMirrorNode) => !!node.attrs.citation,
      group: 'blockquote'
    },
    {
      id: 'blockquote.alignLeft',
      label: '左对齐',
      icon: '⬅',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.setTextAlignment(editor, node, pos, 'left')
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'blockquote',
      isEnabled: (node: ProseMirrorNode) => node.attrs.textAlign !== 'left',
      group: 'blockquote'
    },
    {
      id: 'blockquote.alignCenter',
      label: '居中对齐',
      icon: '⬌',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.setTextAlignment(editor, node, pos, 'center')
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'blockquote',
      isEnabled: (node: ProseMirrorNode) => node.attrs.textAlign !== 'center',
      group: 'blockquote'
    },

    // Image-specific items (if image extension is available)
    {
      id: 'image.alignLeft',
      label: '图片左对齐',
      icon: '🖼️⬅',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, align: 'left' }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to align image left:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'image',
      isEnabled: () => true,
      group: 'image'
    },
    {
      id: 'image.alignCenter',
      label: '图片居中',
      icon: '🖼️⬌',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, align: 'center' }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to center image:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'image',
      isEnabled: () => true,
      group: 'image'
    },
    {
      id: 'image.alignRight',
      label: '图片右对齐',
      icon: '🖼️➡',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, align: 'right' }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to align image right:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'image',
      isEnabled: () => true,
      group: 'image'
    },
    {
      id: 'image.resize',
      label: '调整图片大小',
      icon: '🔍',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const currentWidth = node.attrs.width || 'auto'
          const sizes = ['auto', '25%', '50%', '75%', '100%']
          const currentIndex = sizes.indexOf(currentWidth)
          const nextSize = sizes[(currentIndex + 1) % sizes.length]
          
          const tr = editor.state.tr
          const attrs = { ...node.attrs, width: nextSize }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to resize image:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'image',
      isEnabled: () => true,
      group: 'image'
    },
    {
      id: 'image.addCaption',
      label: '添加图片说明',
      icon: '📝',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, caption: node.attrs.caption || '图片说明' }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to add image caption:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'image',
      isEnabled: (node: ProseMirrorNode) => !node.attrs.caption,
      group: 'image'
    },
    {
      id: 'image.removeCaption',
      label: '移除图片说明',
      icon: '📝⌫',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const tr = editor.state.tr
          const attrs = { ...node.attrs }
          delete attrs.caption
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to remove image caption:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'image',
      isEnabled: (node: ProseMirrorNode) => !!node.attrs.caption,
      group: 'image'
    },
    {
      id: 'image.toggleBorder',
      label: '切换边框',
      icon: '🔲',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.toggleNodeAttribute(editor, node, pos, 'border', true)
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'image',
      isEnabled: () => true,
      group: 'image'
    },
    {
      id: 'image.toggleShadow',
      label: '切换阴影',
      icon: '🌫',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.toggleNodeAttribute(editor, node, pos, 'shadow', true)
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'image',
      isEnabled: () => true,
      group: 'image'
    },

    // Table-specific items
    {
      id: 'table.addRowAbove',
      label: '在上方插入行',
      icon: '⬆️+',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          // Fallback implementation for table row insertion
          const tr = editor.state.tr
          // This is a placeholder - actual table commands would need proper table extension
          console.log('Add row above at position:', pos)
        } catch (error) {
          console.error('Failed to add row above:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => ['table', 'tableRow', 'tableCell', 'tableHeader'].includes(node.type.name),
      isEnabled: () => true,
      group: 'table'
    },
    {
      id: 'table.addRowBelow',
      label: '在下方插入行',
      icon: '⬇️+',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          // Fallback implementation for table row insertion
          const tr = editor.state.tr
          // This is a placeholder - actual table commands would need proper table extension
          console.log('Add row below at position:', pos)
        } catch (error) {
          console.error('Failed to add row below:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => ['table', 'tableRow', 'tableCell', 'tableHeader'].includes(node.type.name),
      isEnabled: () => true,
      group: 'table'
    },
    {
      id: 'table.addColumnLeft',
      label: '在左侧插入列',
      icon: '⬅️+',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          // Fallback implementation for table column insertion
          const tr = editor.state.tr
          // This is a placeholder - actual table commands would need proper table extension
          console.log('Add column left at position:', pos)
        } catch (error) {
          console.error('Failed to add column left:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => ['table', 'tableRow', 'tableCell', 'tableHeader'].includes(node.type.name),
      isEnabled: () => true,
      group: 'table'
    },
    {
      id: 'table.addColumnRight',
      label: '在右侧插入列',
      icon: '➡️+',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          // Fallback implementation for table column insertion
          const tr = editor.state.tr
          // This is a placeholder - actual table commands would need proper table extension
          console.log('Add column right at position:', pos)
        } catch (error) {
          console.error('Failed to add column right:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => ['table', 'tableRow', 'tableCell', 'tableHeader'].includes(node.type.name),
      isEnabled: () => true,
      group: 'table'
    },
    {
      id: 'table.deleteRow',
      label: '删除行',
      icon: '⬇️🗑',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          // Fallback implementation for table row deletion
          const tr = editor.state.tr
          // This is a placeholder - actual table commands would need proper table extension
          console.log('Delete row at position:', pos)
        } catch (error) {
          console.error('Failed to delete row:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => ['tableRow', 'tableCell', 'tableHeader'].includes(node.type.name),
      isEnabled: () => true,
      group: 'table'
    },
    {
      id: 'table.deleteColumn',
      label: '删除列',
      icon: '➡️🗑',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          // Fallback implementation for table column deletion
          const tr = editor.state.tr
          // This is a placeholder - actual table commands would need proper table extension
          console.log('Delete column at position:', pos)
        } catch (error) {
          console.error('Failed to delete column:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => ['tableCell', 'tableHeader'].includes(node.type.name),
      isEnabled: () => true,
      group: 'table'
    },
    {
      id: 'table.toggleHeaderRow',
      label: '切换表头行',
      icon: '📋',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          // Fallback implementation for table header toggle
          const tr = editor.state.tr
          // This is a placeholder - actual table commands would need proper table extension
          console.log('Toggle header row at position:', pos)
        } catch (error) {
          console.error('Failed to toggle header row:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => ['table', 'tableRow', 'tableCell', 'tableHeader'].includes(node.type.name),
      isEnabled: () => true,
      group: 'table'
    },
    {
      id: 'table.toggleHeaderColumn',
      label: '切换表头列',
      icon: '📋|',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          // Fallback implementation for table header toggle
          const tr = editor.state.tr
          // This is a placeholder - actual table commands would need proper table extension
          console.log('Toggle header column at position:', pos)
        } catch (error) {
          console.error('Failed to toggle header column:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => ['table', 'tableRow', 'tableCell', 'tableHeader'].includes(node.type.name),
      isEnabled: () => true,
      group: 'table'
    },

    // Horizontal rule specific items
    {
      id: 'horizontalRule.changeStyle',
      label: '更改分割线样式',
      icon: '━',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const styles = ['solid', 'dashed', 'dotted', 'double']
          const currentStyle = node.attrs.style || 'solid'
          const currentIndex = styles.indexOf(currentStyle)
          const nextStyle = styles[(currentIndex + 1) % styles.length]
          
          const tr = editor.state.tr
          const attrs = { ...node.attrs, style: nextStyle }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to change horizontal rule style:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'horizontalRule',
      isEnabled: () => true,
      group: 'horizontalRule'
    },
    {
      id: 'horizontalRule.changeThickness',
      label: '更改分割线粗细',
      icon: '━━',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        try {
          const thicknesses = ['thin', 'medium', 'thick']
          const currentThickness = node.attrs.thickness || 'medium'
          const currentIndex = thicknesses.indexOf(currentThickness)
          const nextThickness = thicknesses[(currentIndex + 1) % thicknesses.length]
          
          const tr = editor.state.tr
          const attrs = { ...node.attrs, thickness: nextThickness }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        } catch (error) {
          console.error('Failed to change horizontal rule thickness:', error)
        }
      },
      isVisible: (node: ProseMirrorNode) => node.type.name === 'horizontalRule',
      isEnabled: () => true,
      group: 'horizontalRule'
    }
  ]
}

/**
 * Create and register default menu items
 */
export function createDefaultMenuItems(): ClickMenuItem[] {
  const defaultItems: ClickMenuItem[] = [
    // Edit group items
    {
      id: 'duplicate',
      label: '复制段落',
      icon: '📋',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.duplicateNode(editor, node, pos)
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'heading', 'listItem', 'codeBlock', 'blockquote'].includes(node.type.name)
      },
      isEnabled: (node: ProseMirrorNode) => true,
      group: 'edit',
      shortcut: 'Ctrl+D'
    },
    {
      id: 'delete',
      label: '删除段落',
      icon: '🗑️',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.deleteNode(editor, node, pos)
      },
      isVisible: (node: ProseMirrorNode) => true,
      isEnabled: (node: ProseMirrorNode) => true,
      group: 'edit',
      shortcut: 'Delete'
    },
    {
      id: 'moveUp',
      label: '上移',
      icon: '↑',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.moveNodeUp(editor, node, pos)
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'heading', 'listItem', 'codeBlock', 'blockquote', 'bulletList', 'orderedList'].includes(node.type.name)
      },
      isEnabled: (node: ProseMirrorNode) => true,
      group: 'edit'
    },
    {
      id: 'moveDown',
      label: '下移',
      icon: '↓',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.moveNodeDown(editor, node, pos)
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'heading', 'listItem', 'codeBlock', 'blockquote', 'bulletList', 'orderedList'].includes(node.type.name)
      },
      isEnabled: (node: ProseMirrorNode) => true,
      group: 'edit'
    },
    
    // Transform group items
    {
      id: 'convertToHeading1',
      label: '转换为标题 1',
      icon: 'H1',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        if (node.type.name === 'paragraph' || node.type.name === 'blockquote') {
          ContentTransformationUtils.transformParagraphToHeading(editor, node, pos, 1)
        } else if (node.type.name === 'heading') {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, level: 1 as 1 | 2 | 3 | 4 | 5 | 6 }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        }
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'blockquote'].includes(node.type.name) || (node.type.name === 'heading' && node.attrs.level !== 1)
      },
      isEnabled: () => true,
      group: 'transform'
    },
    {
      id: 'convertToHeading2',
      label: '转换为标题 2',
      icon: 'H2',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        if (node.type.name === 'paragraph' || node.type.name === 'blockquote') {
          ContentTransformationUtils.transformParagraphToHeading(editor, node, pos, 2)
        } else if (node.type.name === 'heading') {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, level: 2 as 1 | 2 | 3 | 4 | 5 | 6 }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        }
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'blockquote'].includes(node.type.name) || (node.type.name === 'heading' && node.attrs.level !== 2)
      },
      isEnabled: () => true,
      group: 'transform'
    },
    {
      id: 'convertToHeading3',
      label: '转换为标题 3',
      icon: 'H3',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        if (node.type.name === 'paragraph' || node.type.name === 'blockquote') {
          ContentTransformationUtils.transformParagraphToHeading(editor, node, pos, 3)
        } else if (node.type.name === 'heading') {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, level: 3 as 1 | 2 | 3 | 4 | 5 | 6 }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        }
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'blockquote'].includes(node.type.name) || (node.type.name === 'heading' && node.attrs.level !== 3)
      },
      isEnabled: () => true,
      group: 'transform'
    },
    {
      id: 'convertToHeading4',
      label: '转换为标题 4',
      icon: 'H4',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        if (node.type.name === 'paragraph' || node.type.name === 'blockquote') {
          ContentTransformationUtils.transformParagraphToHeading(editor, node, pos, 4)
        } else if (node.type.name === 'heading') {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, level: 4 as 1 | 2 | 3 | 4 | 5 | 6 }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        }
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'blockquote'].includes(node.type.name) || (node.type.name === 'heading' && node.attrs.level !== 4)
      },
      isEnabled: () => true,
      group: 'transform'
    },
    {
      id: 'convertToHeading5',
      label: '转换为标题 5',
      icon: 'H5',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        if (node.type.name === 'paragraph' || node.type.name === 'blockquote') {
          ContentTransformationUtils.transformParagraphToHeading(editor, node, pos, 5)
        } else if (node.type.name === 'heading') {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, level: 5 as 1 | 2 | 3 | 4 | 5 | 6 }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        }
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'blockquote'].includes(node.type.name) || (node.type.name === 'heading' && node.attrs.level !== 5)
      },
      isEnabled: () => true,
      group: 'transform'
    },
    {
      id: 'convertToHeading6',
      label: '转换为标题 6',
      icon: 'H6',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        if (node.type.name === 'paragraph' || node.type.name === 'blockquote') {
          ContentTransformationUtils.transformParagraphToHeading(editor, node, pos, 6)
        } else if (node.type.name === 'heading') {
          const tr = editor.state.tr
          const attrs = { ...node.attrs, level: 6 as 1 | 2 | 3 | 4 | 5 | 6 }
          tr.setNodeMarkup(pos, null, attrs)
          editor.view.dispatch(tr)
        }
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'blockquote'].includes(node.type.name) || (node.type.name === 'heading' && node.attrs.level !== 6)
      },
      isEnabled: () => true,
      group: 'transform'
    },
    {
      id: 'convertToParagraph',
      label: '转换为段落',
      icon: 'P',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.transformHeadingToParagraph(editor, node, pos)
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['heading', 'codeBlock', 'blockquote'].includes(node.type.name)
      },
      isEnabled: (node: ProseMirrorNode) => true,
      group: 'transform'
    },
    {
      id: 'convertToBulletList',
      label: '转换为无序列表',
      icon: '•',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.transformToListItem(editor, node, pos, 'bulletList')
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'heading', 'blockquote'].includes(node.type.name)
      },
      isEnabled: (node: ProseMirrorNode) => true,
      group: 'transform'
    },
    {
      id: 'convertToOrderedList',
      label: '转换为有序列表',
      icon: '1.',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.transformToListItem(editor, node, pos, 'orderedList')
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'heading', 'blockquote'].includes(node.type.name)
      },
      isEnabled: (node: ProseMirrorNode) => true,
      group: 'transform'
    },
    {
      id: 'convertToCodeBlock',
      label: '转换为代码块',
      icon: '</>',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.transformToCodeBlock(editor, node, pos)
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'heading', 'blockquote'].includes(node.type.name)
      },
      isEnabled: (node: ProseMirrorNode) => true,
      group: 'transform'
    },
    {
      id: 'convertToBlockquote',
      label: '转换为引用块',
      icon: '"',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.transformParagraphToBlockquote(editor, node, pos)
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'heading'].includes(node.type.name)
      },
      isEnabled: (node: ProseMirrorNode) => true,
      group: 'transform'
    },
    
    // Format group items
    {
      id: 'toggleBold',
      label: '加粗',
      icon: 'B',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.applyFormatting(editor, node, pos, 'bold')
      },
      isVisible: (node: ProseMirrorNode) => {
        return ContentTransformationUtils.getAvailableFormatting(node.type.name).includes('bold')
      },
      isEnabled: (node: ProseMirrorNode) => node.textContent.length > 0,
      group: 'format',
      shortcut: 'Ctrl+B'
    },
    {
      id: 'toggleItalic',
      label: '斜体',
      icon: 'I',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.applyFormatting(editor, node, pos, 'italic')
      },
      isVisible: (node: ProseMirrorNode) => {
        return ContentTransformationUtils.getAvailableFormatting(node.type.name).includes('italic')
      },
      isEnabled: (node: ProseMirrorNode) => node.textContent.length > 0,
      group: 'format',
      shortcut: 'Ctrl+I'
    },
    {
      id: 'toggleUnderline',
      label: '下划线',
      icon: 'U',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.applyFormatting(editor, node, pos, 'underline')
      },
      isVisible: (node: ProseMirrorNode) => {
        return ContentTransformationUtils.getAvailableFormatting(node.type.name).includes('underline')
      },
      isEnabled: (node: ProseMirrorNode) => node.textContent.length > 0,
      group: 'format',
      shortcut: 'Ctrl+U'
    },
    {
      id: 'toggleStrike',
      label: '删除线',
      icon: 'S',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.applyFormatting(editor, node, pos, 'strike')
      },
      isVisible: (node: ProseMirrorNode) => {
        return ContentTransformationUtils.getAvailableFormatting(node.type.name).includes('strike')
      },
      isEnabled: (node: ProseMirrorNode) => node.textContent.length > 0,
      group: 'format'
    },
    {
      id: 'toggleCode',
      label: '行内代码',
      icon: '`',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.applyFormatting(editor, node, pos, 'code')
      },
      isVisible: (node: ProseMirrorNode) => {
        return ContentTransformationUtils.getAvailableFormatting(node.type.name).includes('code')
      },
      isEnabled: (node: ProseMirrorNode) => node.textContent.length > 0,
      group: 'format'
    },
    
    // Additional formatting and utility items
    {
      id: 'alignLeft',
      label: '左对齐',
      icon: '⬅',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.setTextAlignment(editor, node, pos, 'left')
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'heading', 'blockquote'].includes(node.type.name)
      },
      isEnabled: () => true,
      group: 'format'
    },
    {
      id: 'alignCenter',
      label: '居中对齐',
      icon: '⬌',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.setTextAlignment(editor, node, pos, 'center')
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'heading', 'blockquote'].includes(node.type.name)
      },
      isEnabled: () => true,
      group: 'format'
    },
    {
      id: 'alignRight',
      label: '右对齐',
      icon: '➡',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.setTextAlignment(editor, node, pos, 'right')
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'heading', 'blockquote'].includes(node.type.name)
      },
      isEnabled: () => true,
      group: 'format'
    },
    {
      id: 'alignJustify',
      label: '两端对齐',
      icon: '⬌⬌',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.setTextAlignment(editor, node, pos, 'justify')
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'blockquote'].includes(node.type.name)
      },
      isEnabled: () => true,
      group: 'format'
    },
    {
      id: 'splitNode',
      label: '分割节点',
      icon: '✂',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.splitNode(editor, node, pos)
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'heading', 'blockquote'].includes(node.type.name)
      },
      isEnabled: (node: ProseMirrorNode) => node.textContent.length > 10,
      group: 'edit'
    },
    {
      id: 'mergeWithNext',
      label: '与下一节点合并',
      icon: '🔗',
      action: (editor: Editor, node: ProseMirrorNode, pos: number) => {
        ContentTransformationUtils.mergeWithNext(editor, node, pos)
      },
      isVisible: (node: ProseMirrorNode) => {
        return ['paragraph', 'heading', 'listItem'].includes(node.type.name)
      },
      isEnabled: () => true,
      group: 'edit'
    }
  ]

  // Get node-specific items
  const nodeSpecificItems = createNodeSpecificMenuItems()
  
  // Combine all items
  const allItems = [...defaultItems, ...nodeSpecificItems]

  // Register default groups
  menuItemRegistry.registerGroup({ id: 'edit', label: '编辑', priority: 100 })
  menuItemRegistry.registerGroup({ id: 'transform', label: '转换', priority: 80 })
  menuItemRegistry.registerGroup({ id: 'format', label: '格式', priority: 60 })
  
  // Register node-specific groups
  menuItemRegistry.registerGroup({ id: 'paragraph', label: '段落', priority: 70 })
  menuItemRegistry.registerGroup({ id: 'heading', label: '标题', priority: 75 })
  menuItemRegistry.registerGroup({ id: 'list', label: '列表', priority: 65 })
  menuItemRegistry.registerGroup({ id: 'codeBlock', label: '代码', priority: 55 })
  menuItemRegistry.registerGroup({ id: 'blockquote', label: '引用', priority: 60 })
  menuItemRegistry.registerGroup({ id: 'image', label: '图片', priority: 50 })
  menuItemRegistry.registerGroup({ id: 'table', label: '表格', priority: 45 })
  menuItemRegistry.registerGroup({ id: 'horizontalRule', label: '分割线', priority: 40 })

  // Register all items
  menuItemRegistry.registerMany(allItems)

  return allItems
}