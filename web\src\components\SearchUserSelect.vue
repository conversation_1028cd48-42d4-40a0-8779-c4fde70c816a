<template>
  <NSelect
    v-model:value="selectedUserIds"
    filterable
    clearable
    multiple
    remote
    value-field="id"
    label-field="username"
    :options="allUserOptions"
    :loading="userSearchLoading"
    :placeholder="placeholder"
    @search="handleUserSearch"
    :render-label="renderUserLabel"
  />
</template>

<script setup lang="tsx">
import { NSelect, NAvatar } from 'naive-ui'
import { ref, computed } from 'vue'

import userApi from '@/api/user'
import fileApi from '@/api/file'
import type { ResponseData } from '@/types/response_data.types'
import type { SearchUser } from '@/types/user.types'
import logger from '@/utils/log'

const props = defineProps<{
  modelValue: SearchUser[]
  placeholder?: string
}>()

const emit = defineEmits<{
  (e: 'update:modelValue', value: SearchUser[]): void
}>()

// 用户搜索
const userSearchLoading = ref(false)
const userOptions = ref<SearchUser[]>([])

// 合并选项和已选用户
const allUserOptions = computed(() => {
  // 去重合并
  const allOptions = [...userOptions.value]

  // 添加已选用户到选项中，确保能够回显
  props.modelValue.forEach((user) => {
    if (!allOptions.some((option) => option.id === user.id)) {
      allOptions.push(user)
    }
  })

  return allOptions
})

// 渲染用户选项，显示头像和用户名
const renderUserLabel = (option: SearchUser) => {
  const avatar = fileApi.getResourceURL(option.avatar as string) || ''
  logger.debug('render user label: ', option)
  return (
    <>
      <NAvatar
        size="small"
        round
        object-fit="cover"
        src={avatar}
        fallbackSrc="/avatar/avatar.png"
        style={{
          marginRight: '8px',
          verticalAlign: 'middle',
        }}
      />
      <span>{option.username}</span>
    </>
  )
}

// 使用ID为v-model值
const selectedUserIds = computed({
  get: () => props.modelValue.map((user) => user.id),
  set: (newIds: string[]) => {
    // 保留被选中的用户
    const remainingUsers = props.modelValue.filter((user) => newIds.includes(user.id))

    // 添加新选择的用户
    const newUsers = allUserOptions.value.filter(
      (option) =>
        newIds.includes(option.id) && !remainingUsers.some((user) => user.id === option.id),
    )

    emit('update:modelValue', [...remainingUsers, ...newUsers])
  },
})

// 处理用户搜索
const handleUserSearch = (query: string) => {
  if (!query.trim()) {
    userOptions.value = []
    return
  }
  userSearchLoading.value = true
  userApi
    .searchUser(query)
    .then((res: ResponseData) => {
      if (res.data) {
        userOptions.value = res.data
      }
    })
    .finally(() => {
      userSearchLoading.value = false
    })
}

// 重置方法
const reset = () => {
  userOptions.value = []
}

defineExpose({
  reset,
})
</script>
