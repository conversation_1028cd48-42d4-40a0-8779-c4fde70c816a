// 基础编辑器样式
.tiptap-editor-wrapper {
  .editor-content {
    // 确保编辑器内容区域可以继承背景色
    background-color: inherit !important;

    .ProseMirrorNoneOutline {
      outline: none;
    }

    .ProseMirrorInput {
      border: 1px solid #b3b3b3 !important;
      border-radius: 4px;
      background-color: white;
      transition: all 0.3s ease;
    }

    .ProseMirror:focus-within {
      p.is-editor-empty:first-child::before {
        display: none;
      }
    }

    .ProseMirror {
      display: flex;
      flex-direction: column;
      width: 100%;
      min-height: 30px;
      font-size: 16px;

      // 让背景色完全继承父元素，确保可以被覆盖
      background-color: inherit !important;

      // 确保暗黑模式下的固定评论显示正确的背景色
      .dark-theme .user-comment-container-fixed &,
      .dark-theme .comment-flash & {
        background-color: var(--blue-light) !important;
      }

      &::before {
        display: none;
      }

      // 文本选择样式增强
      ::selection {
        background-color: rgba(45, 140, 240, 30%);
        color: inherit;
      }

      // 段落样式
      p {
        margin: 0.25rem 0.5rem;
        position: relative;
        transition: background-color 0.2s ease;
        line-height: 1.5;
        min-height: 1.5em;

        &:focus,
        &.has-focus {
          background-color: rgba(90, 214, 150, 5%);
          border-radius: 4px;
        }

        code {
          background-color: #f6f2ff;
          border-radius: 0.4rem;
          color: #181818;
          font-size: 0.85rem;
          padding: 0.25em 0.3em;
        }

        a {
          color: #56a9ff;
          cursor: pointer;
        }
      }

      // 空编辑器占位符
      p.is-editor-empty:first-child::before {
        color: #adb5bd;
        content: attr(data-placeholder);
        float: left;
        height: 0;
        pointer-events: none;
      }

      // 标题样式
      h1,
      h2,
      h3,
      h4,
      h5,
      h6 {
        margin: 0.5rem;
      }

      // 引用块样式
      blockquote {
        border-left: 3px solid #d8d5d3;
        color: #868686;
        margin: 0.5rem;
        padding-left: 1rem;
      }

      // 水平分割线
      hr {
        border: none;
        border-top: 1px solid #e7e4e2;
        cursor: pointer;
        margin: 2rem 0;

        &.ProseMirror-selectednode {
          border-top: 1px solid #e7e4e2;
        }
      }
    }
  }
}

// 全屏模式样式
.tiptap-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw !important;
  width: 100dvw !important;
  height: 100vh !important;
  height: 100dvh !important;
  z-index: 9999;
  background-color: inherit;
  color: inherit;
  padding: 1rem;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
}

.tiptap-fullscreen :deep(.editor-content) {
  flex-grow: 1;
  overflow-y: auto;
  max-height: calc(100vh - 6rem);
  max-height: calc(100dvh - 6rem);
  background-color: inherit;
}

.tiptap-fullscreen :deep(.editor-toolbar) {
  background-color: inherit !important;
}

// 全局聚焦效果
.ProseMirror-focused {
  outline: none;

  p,
  li,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    &:focus-visible {
      background-color: rgba(90, 214, 150, 5%);
      border-radius: 4px;
      outline: none;
    }
  }
}

// 行聚焦效果
.ProseMirror {
  p,
  li,
  blockquote,
  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    // 确保这些元素也继承父元素的背景色
    background-color: inherit !important;

    &:focus-within:not(:has(.ProseMirror-selectednode)) {
      position: relative;
      border-radius: 4px;
      transition: background-color 0.2s ease;

      &::after {
        content: '';
        position: absolute;
        left: -5px;
        top: 0;
        height: 100%;
        width: 3px;
        background-color: rgba(90, 214, 150, 50%);
        border-radius: 2px;
        opacity: 0;
        transition: opacity 0.2s ease;
      }

      &:hover::after {
        opacity: 1;
      }
    }
  }
}

// 浏览器兼容性支持
@supports not (selector(:has(*))) {
  .ProseMirror {
    p,
    li,
    blockquote,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
      &:focus-within {
        background-color: rgba(90, 214, 150, 5%);
        border-radius: 4px;
      }
    }
  }
}

// 只读模式样式
.editor-readonly {
  .ProseMirrorInput,
  .ProseMirror {
    // 禁用所有节点选择功能
    user-select: text;

    // 禁用节点选择的视觉反馈
    .ProseMirror-selectednode {
      outline: none;
      border: none;
      box-shadow: none;
      animation: none;
      transition: none;
    }

    p,
    li,
    div,
    h1,
    h2,
    h3,
    h4,
    h5,
    h6,
    blockquote {
      // 禁用段落级别的节点选择
      &.ProseMirror-selectednode {
        outline: none;
        border: none;
        box-shadow: none;
        animation: none;
        transition: none;
      }

      .image-wrapper,
      img {
        &.ProseMirror-selectednode,
        &:hover,
        &:focus,
        &:active {
          outline: none;
          border: none;
          box-shadow: none;
        }
      }
    }
  }
}
