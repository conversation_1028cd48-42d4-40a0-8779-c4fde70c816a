package com.shenmo.wen.common.objectstorage.response;

import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.io.InputStream;

/**
 * minio获取对象响应
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class MinioGetObjectResponse extends GetObjectResponse<io.minio.GetObjectResponse> {

    /**
     * 构造方法
     *
     * @param origin 源对象
     * <AUTHOR>
     */
    public MinioGetObjectResponse(@NonNull io.minio.GetObjectResponse origin) {
        super(origin);
    }

    @Nullable
    @Override
    public InputStream getInputStream() {

        return origin;
    }

    @Override
    public String getName() {
        return origin.object();
    }
}
