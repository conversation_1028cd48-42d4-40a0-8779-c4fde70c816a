/**
 * Drag Handle Manager - Manages drag handle creation and positioning
 */
import { EditorView } from '@tiptap/pm/view'
import type { DragHandlePluginOptions } from './types'
import { PerformanceUtils } from '../shared/utils/performanceOptimizations'
import { PerformanceConfigManager } from '../shared/config/performanceConfig'

export class DragHandleManager {
  private view: EditorView
  private options: DragHandlePluginOptions
  private dragHandles: Map<HTMLElement, HTMLElement> = new Map()

  constructor(view: EditorView, options: DragHandlePluginOptions) {
    this.view = view
    this.options = options
  }

  initialize() {
    this.updateDragHandles()
    this.setupPerformanceOptimizedUpdates()
  }

  private setupPerformanceOptimizedUpdates() {
    const config = PerformanceConfigManager.getConfig()
    const throttledUpdate = PerformanceUtils.throttle(() => {
      this.updateDragHandles()
    }, config.throttling.updateEvents)
    
    this.options.editor.on('update', throttledUpdate)
  }

  private updateDragHandles() {
    const batcher = new PerformanceUtils.DOMBatcher()
    
    batcher.add(() => this.clearDragHandles())
    this.addDragHandlesToSupportedNodes(batcher)
    
    batcher.flushSync()
  }

  private addDragHandlesToSupportedNodes(batcher: PerformanceUtils.DOMBatcher) {
    const elements = this.getSupportedElements()
    const config = PerformanceConfigManager.getConfig()
    
    if (this.shouldUseVirtualScrolling(elements.length)) {
      this.addDragHandlesVirtual(elements, batcher)
    } else {
      this.addDragHandlesStandard(elements, batcher)
    }
  }

  private getSupportedElements(): NodeListOf<Element> {
    const selectors = this.options.supportedNodes
      .map(node => this.getNodeSelector(node))
      .join(', ')
    
    return this.view.dom.querySelectorAll(selectors)
  }

  private getNodeSelector(nodeType: string): string {
    const config = this.options.nodeTypeConfigs[nodeType]
    return config?.selector || this.getDefaultSelector(nodeType)
  }

  private getDefaultSelector(nodeType: string): string {
    const selectorMap: Record<string, string> = {
      paragraph: 'p',
      heading: 'h1, h2, h3, h4, h5, h6',
      listItem: 'li',
      codeBlock: '.code-block, pre',
      blockquote: 'blockquote',
      bulletList: 'ul',
      orderedList: 'ol'
    }
    
    return selectorMap[nodeType] || `.${nodeType}`
  }

  private shouldUseVirtualScrolling(elementCount: number): boolean {
    const config = PerformanceConfigManager.getConfig()
    return config.virtualScrolling.enabled && 
           elementCount >= config.virtualScrolling.threshold
  }

  private addDragHandlesStandard(
    elements: NodeListOf<Element>, 
    batcher: PerformanceUtils.DOMBatcher
  ) {
    elements.forEach((element) => {
      if (this.isValidDragTarget(element as HTMLElement)) {
        batcher.add(() => this.createDragHandle(element as HTMLElement))
      }
    })
  }

  private addDragHandlesVirtual(
    elements: NodeListOf<Element>, 
    batcher: PerformanceUtils.DOMBatcher
  ) {
    const visibleRange = this.calculateVisibleRange(elements.length)
    
    for (let i = visibleRange.start; i <= visibleRange.end && i < elements.length; i++) {
      const element = elements[i] as HTMLElement
      if (this.isValidDragTarget(element)) {
        batcher.add(() => this.createDragHandle(element))
      }
    }
  }

  private calculateVisibleRange(totalElements: number) {
    const config = PerformanceConfigManager.getConfig()
    const containerRect = this.view.dom.getBoundingClientRect()
    const scrollTop = this.view.dom.scrollTop
    
    const virtualManager = new PerformanceUtils.VirtualScrollManager(
      this.view.dom,
      config.virtualScrolling.itemHeight
    )
    
    virtualManager.setTotalItems(totalElements)
    
    return virtualManager.calculateVisibleRange(
      scrollTop,
      containerRect.height,
      config.virtualScrolling.bufferSize
    )
  }

  private isValidDragTarget(element: HTMLElement): boolean {
    return !element.querySelector('.drag-handle') &&
           !element.closest('[data-drag-handle]') &&
           this.hasVisibleContent(element)
  }

  private hasVisibleContent(element: HTMLElement): boolean {
    const rect = element.getBoundingClientRect()
    return rect.width > 0 && rect.height > 0
  }

  private createDragHandle(element: HTMLElement) {
    if (this.dragHandles.has(element)) return

    const nodeType = this.getNodeTypeFromElement(element)
    if (!nodeType) return

    const handle = this.buildDragHandleElement(nodeType)
    this.positionDragHandle(element, handle, nodeType)
    this.attachEventListeners(handle, element)
    this.addAccessibilityFeatures(handle, nodeType)
    
    this.dragHandles.set(element, handle)
  }

  private buildDragHandleElement(nodeType: string): HTMLElement {
    const handle = document.createElement('div')
    handle.className = `drag-handle drag-handle--${nodeType}`
    handle.draggable = true
    handle.setAttribute('data-drag-handle', 'true')
    handle.setAttribute('data-node-type', nodeType)
    
    handle.innerHTML = `
      <div class="drag-handle__icon">
        ${this.getIconForNodeType(nodeType)}
      </div>
    `
    
    return handle
  }

  private getNodeTypeFromElement(element: HTMLElement): string | null {
    for (const [nodeType, config] of Object.entries(this.options.nodeTypeConfigs)) {
      const selectors = config.selector.split(',').map(s => s.trim())
      if (selectors.some(selector => element.matches(selector))) {
        return nodeType
      }
    }
    return null
  }

  private getIconForNodeType(nodeType: string): string {
    // Icon definitions moved to separate constant file for maintainability
    return this.getDragHandleIcon(nodeType)
  }

  private getDragHandleIcon(nodeType: string): string {
    // This would be imported from a constants file
    const icons: Record<string, string> = {
      paragraph: '<svg>...</svg>', // Simplified for brevity
      heading: '<svg>...</svg>',
      // ... other icons
    }
    
    return icons[nodeType] || icons.paragraph
  }

  clearDragHandles() {
    this.dragHandles.forEach((handle) => {
      if (handle.parentNode) {
        handle.parentNode.removeChild(handle)
      }
    })
    this.dragHandles.clear()
  }

  destroy() {
    this.clearDragHandles()
    PerformanceUtils.MemoryManager.cleanup(this)
  }
}