package com.shenmo.wen.app.core.article.controller;

import com.shenmo.wen.app.core.article.pojo.domain.WenHotTag;
import com.shenmo.wen.app.core.article.pojo.param.WenArticleSaveParam;
import com.shenmo.wen.app.core.article.pojo.param.WenArticleSearchParam;
import com.shenmo.wen.app.core.article.pojo.param.WenArticleUpdateParam;
import com.shenmo.wen.app.core.article.pojo.vo.WenArticleVo;
import com.shenmo.wen.app.core.article.service.WenArticleService;
import com.shenmo.wen.common.pojo.response.ResponseData;

import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0.0
 */
@RestController
@RequestMapping("/articles")
@RequiredArgsConstructor
public class WenArticleController {

    private final WenArticleService service;

    @PostMapping
    public ResponseData<Long> save(@Validated @RequestBody WenArticleSaveParam param) {
        return ResponseData.success(service.save(param));
    }

    @PutMapping
    public ResponseData<Void> edit(@Validated @RequestBody WenArticleUpdateParam param) {
        service.edit(param);
        return ResponseData.success();
    }

    @GetMapping
    public ResponseData<List<WenArticleVo>> search(WenArticleSearchParam param) {
        return ResponseData.success(service.search(param));
    }

    @GetMapping("/title/{id}")
    public ResponseData<String> title(@PathVariable("id") Long id) {
        return ResponseData.success(service.title(id));
    }

    @GetMapping("/detail/{id}")
    public ResponseData<WenArticleVo> detail(@PathVariable("id") Long id) {
        return ResponseData.success(service.detail(id));
    }

    @GetMapping("/md/{id}")
    public void md(@PathVariable("id") Long id) throws IOException {
        service.download(id);
    }

    /**
     * 切换文章发布范围
     * 
     * @param id 文章ID
     * @return 操作结果
     */
    @PutMapping("/toggle-scope/{id}")
    public ResponseData<Void> togglePublishedScope(@PathVariable("id") Long id) {
        service.togglePublishedScope(id);
        return ResponseData.success();
    }

    /**
     * 获取热门标签
     * 
     * @param limit 返回的标签数量，默认为10
     * @return 热门标签及其使用次数
     */
    @GetMapping("/hot-tags")
    public ResponseData<List<WenHotTag>> getHotTags(@RequestParam(value = "limit", defaultValue = "10") int limit) {
        return ResponseData.success(service.getHotTags(limit));
    }

    /**
     * 删除文章
     * 
     * @param id 文章ID
     * @return 操作结果
     */
    @DeleteMapping("/{id}")
    public ResponseData<Void> delete(@PathVariable("id") Long id) {
        service.delete(id);
        return ResponseData.success();
    }
}
