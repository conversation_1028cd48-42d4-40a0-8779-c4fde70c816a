{"version": "2.0.0", "tasks": [{"label": "启动-wen-core", "type": "shell", "command": ".\\start-wen-core.bat", "group": {"kind": "build", "isDefault": true}, "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "启动-wen-authentication", "type": "shell", "command": ".\\start-wen-authentication.bat", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}, {"label": "启动-wen-gateway", "type": "shell", "command": ".\\start-wen-gateway.bat", "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}}]}