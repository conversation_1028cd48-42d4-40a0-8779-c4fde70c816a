import { ref, nextTick, type Ref } from 'vue'

import commentApi from '@/api/comment'
import favoriteApi from '@/api/favorite'
import interactionApi from '@/api/interaction'
import { COMMENT_QUICK_REPLY, COMMENT_SEND } from '@/constants/frequency_key.constants'
import type { Comment } from '@/types/comment.types'
import type { ResponseData } from '@/types/response_data.types'
import {
  validateEditorContent,
  checkLoadingState,
  setLoadingState,
} from '@/utils/editor-validation'
import frequencyLimit from '@/utils/frequency-limit'
import message from '@/utils/message'
import tiptap from '@/utils/tiptap'

import type { Editor } from '@tiptap/vue-3'
import type { JSONContent } from '@tiptap/vue-3'

// 定义获取文章ID的函数类型
type GetArticleIdFunction = () => string

// 定义评论输入引用类型
interface CommentMainInputRef {
  sendTiptapEditorRef?: Editor & {
    clearContent: () => void
  }
}

// 定义评论交互状态类型
interface CommentInteractionState {
  quickReplyLoading: Ref<Map<string, boolean>>
  sendCommentLoading: Ref<boolean>
  commentInputVisible: Ref<string>
  commentReply: Ref<JSONContent | undefined>
  quickReplyTiptapEditorMap: Ref<Map<string, any>>
  updateEditor: (commentId: string, editor: any) => void
  interactionBtn: (comment: Comment, actionType: number) => void
  favoriteBtn: (comment: Comment) => void
  clearAllQuickReplyContent: () => (params: { commentList: Comment[] }) => void
  handleCommentReplyClick: (comment: Comment, options: { isLastBreadcrumb: boolean }) => void
  debouncedQuickReplyComment: (
    comment: Comment,
    options: { isLastBreadcrumb: boolean; onSuccess?: (commentId: string) => void },
  ) => void
  debouncedSendComment: (
    commentMainInputRef: CommentMainInputRef,
    options: { lastBreadcrumbComment: Comment; onSuccess?: (commentId: string) => void },
  ) => void
}

export function useCommentInteraction(getArticleId: GetArticleIdFunction): CommentInteractionState {
  // 评论交互状态
  const quickReplyLoading = ref<Map<string, boolean>>(new Map())
  const sendCommentLoading = ref(false)
  const commentInputVisible = ref('-1')
  const commentReply = ref<JSONContent | undefined>(undefined)
  const quickReplyTiptapEditorMap = ref<Map<string, any>>(new Map())

  // 更新编辑器引用
  const updateEditor = (commentId: string, editor: any) => {
    quickReplyTiptapEditorMap.value.set(commentId, editor)
  }

  // 互动（点赞/踩）
  const interactionBtn = (comment: Comment, actionType: number) => {
    const reqParam = {
      targetType: 0,
      targetId: comment!.id,
      actionType: actionType,
    }
    interactionApi.save(reqParam).then((res: ResponseData) => {
      const data = res?.data
      if (data) {
        comment!.likeCount = data.likeCount
        comment!.dislikeCount = data.dislikeCount
        const like = actionType === 1
        // 取消互动
        if (data.cancel) {
          if (like) {
            message.info('赞取消')
            comment!.isLike = false
          } else {
            message.info('踩取消')
            comment!.isDislike = false
          }
        } else {
          if (like) {
            message.success('赞 :)')
            comment!.isLike = true
          } else {
            message.warning('踩 :(')
            comment!.isDislike = true
          }
        }
      }
    })
  }

  // 收藏
  const favoriteBtn = (comment: Comment) => {
    const reqParam = {
      targetType: 0,
      targetId: comment!.id,
    }
    favoriteApi.save(reqParam).then((res: ResponseData) => {
      const data = res?.data
      if (data) {
        comment!.favoriteCount = data.count
        if (data.cancel) {
          message.info('取消收藏')
          comment!.isFavorite = false
        } else {
          message.success('已收藏')
          comment!.isFavorite = true
        }
      }
    })
  }

  // 清空所有快捷回复框内容
  const clearAllQuickReplyContent = () => {
    // 关闭所有回复框
    commentInputVisible.value = '-1'

    return ({ commentList }: { commentList: Comment[] }) => {
      // 清空所有评论的快捷回复内容
      commentList.forEach((comment) => {
        if (comment.quickCommentReply) {
          comment.quickCommentReply = undefined
        }
        const editor = quickReplyTiptapEditorMap.value.get(comment.id)
        if (editor) {
          editor.commands.clearContent()
        }
      })
    }
  }

  // 处理点击回复按钮
  const handleCommentReplyClick = (
    comment: Comment,
    { isLastBreadcrumb }: { isLastBreadcrumb: boolean },
  ) => {
    // 如果当前回复框已经打开，则关闭
    if (commentInputVisible.value === comment.id) {
      commentInputVisible.value = '-1'
      return
    }

    // 打开回复框
    commentInputVisible.value = comment.id

    // 如果是第三层评论，并且编辑器是空的，则添加@mention
    nextTick(() => {
      if (isLastBreadcrumb && !comment.fixed) {
        const editor = quickReplyTiptapEditorMap.value.get(comment.id)
        if (
          editor &&
          (!comment.quickCommentReply || !comment.quickCommentReply.content?.[0]?.content)
        ) {
          // 创建一个包含@用户名的初始内容
          const mentionContent = {
            type: 'doc',
            content: [
              {
                type: 'paragraph',
                content: [
                  {
                    type: 'mention',
                    attrs: {
                      id: comment.publisher,
                      label: comment.publisher,
                      avatar: comment.publisherAvatar || '',
                    },
                  },
                  {
                    type: 'text',
                    text: ' ',
                  },
                ],
              },
            ],
          }
          editor.commands.setContent(mentionContent)
        }
      }
    })
  }

  // 快捷回复评论
  const quickReplyComment = (
    comment: Comment,
    {
      isLastBreadcrumb,
      onSuccess,
    }: {
      isLastBreadcrumb: boolean
      onSuccess?: (commentId: string) => void
    },
  ) => {
    // 检查是否正在加载中
    if (!checkLoadingState(quickReplyLoading.value, comment.id)) {
      return
    }

    // 获取编辑器实例
    const editor = quickReplyTiptapEditorMap.value.get(comment.id)

    // 验证编辑器内容
    const contentRef = { value: comment.quickCommentReply }
    const validationResult = validateEditorContent(editor, contentRef, '啥也没有可不能发送哦~')

    if (!validationResult.isValid) {
      return
    }

    // 更新内容引用
    comment.quickCommentReply = validationResult.content || contentRef.value

    // 设置loading状态
    setLoadingState(quickReplyLoading.value, true, comment.id)

    const content = tiptap.toJsonString(comment.quickCommentReply!)
    commentApi
      .save({
        content: content,
        articleId: getArticleId(),
        parentCommentId: isLastBreadcrumb && !comment.fixed ? comment.parentCommentId : comment.id,
      })
      .then((res: ResponseData) => {
        message.success('发送成功')

        // 获取新评论的ID并调用onSuccess回调
        const commentId = res?.data
        if (commentId && onSuccess) {
          onSuccess(commentId)
        }

        // 清空编辑器并重置
        editor.commands.clearContent()
        comment.quickCommentReply = undefined
      })
      .finally(() => {
        // 清除loading状态
        quickReplyLoading.value.set(comment.id, false)
      })
  }

  // 使用项目自带的防抖方法为快捷回复添加防抖
  const debouncedQuickReplyComment = (
    comment: Comment,
    options: {
      isLastBreadcrumb: boolean
      onSuccess?: (commentId: string) => void
    },
  ) => {
    frequencyLimit.debounce(
      `${COMMENT_QUICK_REPLY}-${comment.id}`,
      () => {
        quickReplyComment(comment, options)
      },
      300,
    )
  }

  // 发送主评论
  const sendComment = (
    commentMainInputRef: CommentMainInputRef,
    {
      lastBreadcrumbComment,
      onSuccess,
    }: {
      lastBreadcrumbComment: Comment
      onSuccess?: (commentId: string) => void
    },
  ) => {
    // 检查是否正在加载中
    if (!checkLoadingState(sendCommentLoading)) {
      return
    }

    // 获取编辑器实例
    const sendTiptapEditorRef = commentMainInputRef?.sendTiptapEditorRef

    // 验证编辑器内容
    const validationResult = validateEditorContent(
      sendTiptapEditorRef,
      commentReply,
      '啥也没有可不能发送哦~',
    )

    if (!validationResult.isValid) {
      return
    }

    // 更新内容引用
    commentReply.value = validationResult.content || commentReply.value

    // 设置loading状态
    setLoadingState(sendCommentLoading, true)

    const content = tiptap.toJsonString(commentReply.value!)
    const comment = lastBreadcrumbComment
    const articleId = getArticleId()
    commentApi
      .save({
        content: content,
        articleId: articleId,
        parentCommentId: comment.id,
      })
      .then((res: ResponseData) => {
        message.success('发送成功')

        // 获取新评论的ID并调用onSuccess回调
        const commentId = res?.data
        if (commentId && onSuccess) {
          onSuccess(commentId)
        }

        // 清空编辑器内容
        sendTiptapEditorRef?.clearContent()
        commentReply.value = undefined
      })
      .finally(() => {
        // 清除loading状态
        sendCommentLoading.value = false
      })
  }

  // 使用项目自带的防抖方法为发送评论添加防抖
  const debouncedSendComment = (
    commentMainInputRef: CommentMainInputRef,
    options: {
      lastBreadcrumbComment: Comment
      onSuccess?: (commentId: string) => void
    },
  ) => {
    frequencyLimit.debounce(
      COMMENT_SEND,
      () => {
        sendComment(commentMainInputRef, options)
      },
      300,
    )
  }

  return {
    quickReplyLoading,
    sendCommentLoading,
    commentInputVisible,
    commentReply,
    quickReplyTiptapEditorMap,
    updateEditor,
    interactionBtn,
    favoriteBtn,
    clearAllQuickReplyContent,
    handleCommentReplyClick,
    debouncedQuickReplyComment,
    debouncedSendComment,
  }
}
