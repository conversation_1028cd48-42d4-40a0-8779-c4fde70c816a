import logger from '@/utils/log'

import type { EditorWithStorage } from '../useImageUpload'

/**
 * 图片上传成功后处理组合式函数
 * 处理图片上传成功后的选中、滚动和视觉反馈
 */
export function useImageUploadSuccess() {
  /**
   * 处理图片上传成功后的逻辑
   * @param editor 编辑器实例
   * @param imageUrl 上传成功的图片相对路径
   */
  const handleImageUploadSuccess = (editor: EditorWithStorage, imageUrl: string) => {
    // 添加延迟，以便图片加载完成后再选中它
    setTimeout(() => {
      try {
        // 清除所有已选中的图片
        clearSelectedImages()

        // 验证编辑器状态
        if (!validateEditor(editor)) {
          return
        }

        // 查找目标图片
        const targetWrapper = findTargetImageWrapper(editor, imageUrl)

        if (targetWrapper) {
          // 选中并滚动到目标图片
          selectAndScrollToImage(targetWrapper)
          // 添加上传成功的视觉反馈
          addUploadSuccessAnimation(targetWrapper)
        } else {
          // 如果没找到匹配的图片，选中最后一个图片
          selectLastImage(editor)
        }
      } catch (error) {
        logger.error('Error selecting image after upload:', error)
      }
    }, 300)
  }

  /**
   * 清除所有已选中的图片
   */
  const clearSelectedImages = () => {
    const allImages = document.querySelectorAll('.image-wrapper.ProseMirror-selectednode')
    allImages.forEach((img) => {
      img.classList.remove('ProseMirror-selectednode')
    })
  }

  /**
   * 验证编辑器状态
   * @param editor 编辑器实例
   * @returns 是否有效
   */
  const validateEditor = (editor: EditorWithStorage): boolean => {
    if (!editor || !editor.view || !editor.view.dom) {
      logger.error('Editor view dom not found')
      return false
    }
    return true
  }

  /**
   * 查找目标图片包装器
   * @param editor 编辑器实例
   * @param imageUrl 图片URL
   * @returns 目标包装器元素或null
   */
  const findTargetImageWrapper = (
    editor: EditorWithStorage,
    imageUrl: string,
  ): HTMLElement | null => {
    const editorElement = editor.view!.dom
    const uploadedImages = editorElement.querySelectorAll('.image-wrapper img')
    logger.debug('Found image elements:', uploadedImages.length)

    let targetWrapper: HTMLElement | null = null

    // 获取图片扩展的路径转换方法
    const transformSrc = editor.storage.image?.transformSrc

    // 使用Array.from转换NodeList，然后过滤和映射为正确的类型
    Array.from(uploadedImages)
      .filter((el): el is HTMLImageElement => el instanceof HTMLImageElement)
      .forEach((imgElement) => {
        // 检查图片的data-relative-src属性或src属性
        const imgRelativeSrc = imgElement.dataset.relativeSrc
        const imgSrc = imgElement.getAttribute('src')

        logger.debug('Checking img:', {
          dataRelativeSrc: imgRelativeSrc,
          src: imgSrc,
          lookingFor: imageUrl,
        })

        // 使用相对路径进行匹配，避免域名影响比较
        let relativeSrc = imgSrc || ''

        // 使用图片扩展的方法转换路径
        if (transformSrc && imgSrc) {
          relativeSrc = transformSrc(imgSrc)
        } else if (imgSrc && imgSrc.startsWith('http')) {
          // 退化处理 - 仅在扩展方法不可用时使用
          try {
            const url = new URL(imgSrc)
            relativeSrc = url.pathname
          } catch {
            logger.warn('Failed to parse URL:', imgSrc)
          }
        }

        const isMatch =
          imgRelativeSrc === imageUrl || relativeSrc === imageUrl || imageUrl.endsWith(relativeSrc)

        if (isMatch) {
          logger.debug('Found matching image element')
          targetWrapper = imgElement.closest('.image-wrapper') as HTMLElement
        }
      })

    return targetWrapper
  }

  /**
   * 选中并滚动到图片
   * @param targetWrapper 目标包装器元素
   */
  const selectAndScrollToImage = (targetWrapper: HTMLElement) => {
    logger.debug('Found target wrapper, adding visual feedback')
    targetWrapper.classList?.add('ProseMirror-selectednode')
    targetWrapper.scrollIntoView?.({
      behavior: 'smooth',
      block: 'center',
    })
  }

  /**
   * 添加上传成功的动画效果
   * @param targetWrapper 目标包装器元素
   */
  const addUploadSuccessAnimation = (targetWrapper: HTMLElement) => {
    try {
      const uploadSuccessEffect = document.createElement('div')
      uploadSuccessEffect.className = 'upload-success-effect'
      uploadSuccessEffect.style.cssText = `
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        border-radius: 0.25rem;
        background-color: rgba(90, 214, 150, 0.2);
        z-index: 5;
        pointer-events: none;
        opacity: 0;
      `
      targetWrapper.appendChild(uploadSuccessEffect)

      // 动画效果
      uploadSuccessEffect.animate([{ opacity: 0.7 }, { opacity: 0 }], {
        duration: 800,
        easing: 'ease-out',
      }).onfinish = () => {
        uploadSuccessEffect.remove()
      }

      // 图片边框动画效果
      targetWrapper.animate(
        [
          { outline: '2px solid #5ad696', outlineOffset: '3px' },
          { outline: '2px solid #2d8cf0', outlineOffset: '2px' },
        ],
        {
          duration: 800,
          easing: 'ease-out',
        },
      )
    } catch (error) {
      logger.error('Error adding upload success animation:', error)
    }
  }

  /**
   * 选中最后一个图片
   * @param editor 编辑器实例
   */
  const selectLastImage = (editor: EditorWithStorage) => {
    logger.debug('No matching image found, selecting last wrapper')
    const editorElement = editor.view!.dom
    const allWrappers = editorElement.querySelectorAll('.image-wrapper')
    if (allWrappers.length > 0) {
      const lastWrapper = allWrappers[allWrappers.length - 1] as HTMLElement
      lastWrapper.classList?.add('ProseMirror-selectednode')
      lastWrapper.scrollIntoView?.({ behavior: 'smooth', block: 'center' })
    }
  }

  return {
    handleImageUploadSuccess,
    clearSelectedImages,
    validateEditor,
    findTargetImageWrapper,
    selectAndScrollToImage,
    addUploadSuccessAnimation,
    selectLastImage,
  }
}
