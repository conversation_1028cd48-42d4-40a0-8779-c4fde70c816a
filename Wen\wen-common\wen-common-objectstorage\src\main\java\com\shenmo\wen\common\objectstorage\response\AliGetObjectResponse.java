package com.shenmo.wen.common.objectstorage.response;

import com.aliyun.oss.model.OSSObject;
import org.springframework.lang.NonNull;
import org.springframework.lang.Nullable;

import java.io.InputStream;

/**
 * ali获取对象响应
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public class AliGetObjectResponse extends GetObjectResponse<OSSObject> {

    /**
     * 构造方法
     *
     * @param origin 源对象
     * <AUTHOR>
     */
    public AliGetObjectResponse(@NonNull OSSObject origin) {
        super(origin);
    }

    @Nullable
    @Override
    public InputStream getInputStream() {

        return origin.getObjectContent();
    }

    @Override
    public String getName() {

        return origin.getKey();
    }
}
