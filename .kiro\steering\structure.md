# Project Structure

## Root Level Organization
```
├── web/                    # Vue 3 frontend application
├── Wen/                    # Java Spring Boot backend services
├── interactive-feedback-mcp/  # MCP server for interactive feedback
├── .kiro/                  # Kiro AI assistant configuration
└── *.bat                   # Windows service startup scripts
```

## Frontend Structure (web/)
```
web/
├── src/
│   ├── api/               # API service layer (REST endpoints)
│   ├── components/        # Vue components organized by feature
│   │   ├── tiptap/       # Rich text editor components
│   │   ├── article/      # Article-related components
│   │   ├── comment/      # Comment system components
│   │   └── danmaku/      # Bullet comment components
│   ├── composables/      # Vue 3 composition functions
│   ├── stores/           # Pinia state management
│   ├── types/            # TypeScript type definitions
│   ├── utils/            # Utility functions and helpers
│   ├── views/            # Page-level Vue components
│   ├── router/           # Vue Router configuration
│   ├── styles/           # Global SCSS styles and themes
│   └── constants/        # Application constants
├── public/               # Static assets
└── dist/                 # Build output
```

## Backend Structure (Wen/)
```
Wen/
├── wen-app/              # Main application module
├── wen-common/           # Shared utilities and common code
├── wen-dependency/       # Dependency management
├── wen-modules/          # Feature-specific modules
├── docs/                 # Documentation
└── sm_wen.sql           # Database schema
```

## Key Architectural Patterns

### Frontend Conventions
- **Component Organization**: Feature-based grouping with shared components at root
- **Composables**: Business logic extracted into reusable composition functions
- **API Layer**: Centralized API calls in `/api` directory with typed responses
- **Type Safety**: Comprehensive TypeScript types in `/types` directory
- **State Management**: Pinia stores for global state, local state in components
- **Styling**: SCSS with CSS modules, theme system for dark/light modes

### File Naming Conventions
- **Components**: PascalCase (e.g., `ArticleModal.vue`)
- **Composables**: camelCase with `use` prefix (e.g., `useArticleList.ts`)
- **Types**: camelCase with `.types.ts` suffix
- **Constants**: SCREAMING_SNAKE_CASE with `.constants.ts` suffix
- **Utils**: camelCase with descriptive names

### Import Organization
ESLint enforces strict import ordering:
1. Node.js built-in modules
2. External dependencies
3. Internal modules (`@/` alias for `src/`)
4. Relative imports (parent, sibling, index)
5. CSS/SCSS imports

### TipTap Editor Architecture
- **Extensions**: Custom TipTap extensions in `components/tiptap/extensions/`
- **Click Menu**: Drag-and-drop paragraph menu system
- **Composables**: Editor logic separated into composables for reusability
- **Menu Registry**: Centralized menu item management