package com.shenmo.wen.app.core.comment.service;

import java.util.List;

import com.shenmo.wen.app.core.comment.pojo.domain.WenCommonLocation;
import com.shenmo.wen.app.core.comment.pojo.param.WenCommentLoadParam;
import com.shenmo.wen.app.core.comment.pojo.param.WenCommentSaveParam;
import com.shenmo.wen.app.core.comment.pojo.vo.WenCommentVo;
import com.shenmo.wen.app.core.pojo.param.WenSearchParam;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface WenCommentService {
    Long save(WenCommentSaveParam param);

    List<WenCommentVo> load(WenCommentLoadParam param);

    WenCommentVo loadById(Long id);

    List<WenCommentVo> parents(Long id);

    List<WenCommentVo> search(WenSearchParam param);

    WenCommonLocation location(Long id);
}
