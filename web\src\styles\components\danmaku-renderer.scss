/* 
 * danmaku-renderer.scss - 弹幕渲染器样式
 * 包含主容器、富文本元素和图片预览模态框样式
 */

/* 主容器样式 */
.danmaku-renderer {
  font-size: inherit;
  line-height: inherit;
  color: var(--black);
  word-break: break-word;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 100%;
  display: inline-block;
  vertical-align: bottom;
}

/* @提及样式 */
.mention {
  color: var(--purple);
  font-weight: bold;
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  font-size: 0.85em;
  vertical-align: bottom;
  padding: 0.1rem 0.2rem;
  gap: 0.2rem;
}

/* @提及头像样式 */
.mention-avatar {
  display: inline-block;
  vertical-align: bottom;
  margin-right: 2px;
  border-radius: 50%;
  object-fit: cover;
  height: 1.25rem;
  width: 1.25rem;
  overflow: hidden;
  aspect-ratio: 1 / 1;
}

/* 图片占位符样式 */
.image-placeholder {
  color: var(--gray);
  background-color: rgba(0, 0, 0, 5%);
  padding: 0 4px;
  border-radius: 3px;
}

/* 代码样式 */
.danmaku-renderer code {
  background-color: #f6f2ff;
  border-radius: 0.4rem;
  color: #181818;
  font-size: 0.85em;
  padding: 0.25em 0.3em;
}

/* 链接样式 */
.danmaku-renderer a {
  color: #56a9ff;
  text-decoration: none;
}

.danmaku-renderer a:hover {
  text-decoration: underline;
}

/* 弹幕图片样式 */
.danmaku-image {
  display: inline-block;
  vertical-align: bottom;
  margin: 0 3px;
  border-radius: 3px;
  object-fit: contain;
  line-height: 1;
  max-height: 3rem;
  cursor: pointer;
}

/* 添加富文本基础样式 */
.danmaku-renderer strong {
  font-weight: bold;
}

.danmaku-renderer em {
  font-style: italic;
}

.danmaku-renderer u {
  text-decoration: underline;
}

.danmaku-renderer s {
  text-decoration: line-through;
}

/* 图片预览模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 85%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
  opacity: 0;
  visibility: hidden;
  cursor: zoom-out;
  transition:
    opacity 0.3s ease,
    visibility 0.3s ease;
  padding: 2rem;
  box-sizing: border-box;
  backdrop-filter: blur(3px);
}

.modal-overlay-active {
  opacity: 1;
  visibility: visible;
}

.modal-overlay img {
  max-width: 95%;
  max-height: 90vh;
  object-fit: contain;
  border-radius: 0.25rem;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 25%);
  transform: scale(0.95);
  opacity: 0;
  transition:
    opacity 0.5s ease,
    transform 0.3s ease;
}

.modal-overlay-active img {
  opacity: 1;
  transform: scale(1);
}

.loading-spinner {
  border: 0.25rem solid rgba(255, 255, 255, 20%);
  border-top: 0.25rem solid #f0f0f0;
  border-radius: 50%;
  width: 3rem;
  height: 3rem;
  animation: spin 1s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

@keyframes spin {
  0% {
    transform: translate(-50%, -50%) rotate(0deg);
  }

  100% {
    transform: translate(-50%, -50%) rotate(360deg);
  }
}

/* 移动设备上的响应式样式 */
@media (width <= 768px) {
  .modal-overlay {
    padding: 1rem;
  }

  .modal-overlay img {
    max-width: 100%;
  }

  .loading-spinner {
    width: 2.5rem;
    height: 2.5rem;
  }
}
