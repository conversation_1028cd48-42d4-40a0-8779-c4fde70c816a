package com.shenmo.wen.common.enumeration;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通知类型枚举
 * 
 * <AUTHOR>
 * @version 1.0.0
 */
@Getter
@AllArgsConstructor
public enum NotificationTypeEnum {

    SYSTEM(0, "系统"),
    PUBLISH_ARTICLE(1, "发布文章"),
    MODIFY_ARTICLE(2, "修改文章"),
    PUBLISH_COMMENT(3, "发布评论"),
    ;

    private final int code;
    private final String text;

    public static NotificationTypeEnum of(int code) {
        final NotificationTypeEnum[] values = values();
        for (NotificationTypeEnum e : values) {
            if (e.code == code) {
                return e;
            }
        }
        throw new EnumConstantNotPresentException(NotificationTypeEnum.class, String.valueOf(code));
    }
}