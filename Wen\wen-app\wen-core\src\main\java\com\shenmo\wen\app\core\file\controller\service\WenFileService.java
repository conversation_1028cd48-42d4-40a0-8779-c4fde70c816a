package com.shenmo.wen.app.core.file.controller.service;

import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.function.Consumer;

/**
 *
 * <AUTHOR>
 * @version 1.0.0
 */
public interface WenFileService {
    String save(String bucket, MultipartFile file) throws IOException;

    String save(String bucket, MultipartFile file, Consumer<String> fileUriConsumer) throws IOException;
}
