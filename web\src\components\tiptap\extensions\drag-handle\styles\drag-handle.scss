// Simplified Drag Handle Styles
.drag-handle {
  position: absolute;
  width: 20px;
  height: 20px;
  cursor: grab;
  opacity: 0.6;
  transition: opacity 0.2s ease;
  z-index: 10;
  border-radius: 4px;
  background: #f0f0f0;
  border: 1px solid #ddd;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666;

  &:hover {
    opacity: 1;
    background: #e0e0e0;
  }

  &:active {
    cursor: grabbing;
    background: #d0d0d0;
  }

  svg {
    width: 12px;
    height: 12px;
  }

  &__icon {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    background: var(--color-border);
    color: var(--color-text-secondary);
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    &:hover {
      background: var(--color-border-hover);
      color: var(--color-text);
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
    }

    // Icon styling
    svg {
      width: 12px;
      height: 12px;
      transition: transform 0.2s ease;
    }
  }

  // Active drag state
  &--dragging {
    opacity: 0.8;
    transform: translateY(-50%) scale(1.1);
    
    .drag-handle__icon {
      background: var(--color-primary);
      color: white;
      box-shadow: 0 6px 12px rgba(0, 0, 0, 0.2);
    }
  }
}

// Show drag handle on paragraph hover
.ProseMirror {
  p:hover .drag-handle,
  h1:hover .drag-handle,
  h2:hover .drag-handle,
  h3:hover .drag-handle,
  h4:hover .drag-handle,
  h5:hover .drag-handle,
  h6:hover .drag-handle,
  li:hover .drag-handle,
  .code-block:hover .drag-handle {
    opacity: 1;
  }
}

// Drop zone indicators with enhanced visual feedback
.drop-zone {
  position: relative;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, var(--color-primary), var(--color-primary-light));
    border-radius: 2px;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 0 8px rgba(var(--color-primary-rgb), 0.3);
  }

  &--active {
    &::before {
      opacity: 1;
      animation: drop-zone-pulse 1.5s infinite;
    }
  }

  &--invalid {
    &::before {
      background: linear-gradient(90deg, var(--color-error), var(--color-error-light));
      box-shadow: 0 0 8px rgba(var(--color-error-rgb), 0.3);
      animation: shake 0.5s ease-in-out;
    }
  }

  &--highlight {
    background: rgba(var(--color-primary-rgb), 0.05);
    border-radius: 4px;
    
    &::after {
      content: '';
      position: absolute;
      top: -1px;
      left: -1px;
      right: -1px;
      bottom: -1px;
      border: 2px dashed var(--color-primary);
      border-radius: 6px;
      opacity: 0.6;
      animation: dash-border 2s linear infinite;
    }
  }
}

@keyframes drop-zone-pulse {
  0%, 100% { 
    opacity: 1;
    transform: scaleX(1);
  }
  50% { 
    opacity: 0.7;
    transform: scaleX(1.02);
  }
}

@keyframes dash-border {
  0% { stroke-dashoffset: 0; }
  100% { stroke-dashoffset: 20; }
}

// Enhanced drag preview styles with ghost image effect
.drag-preview {
  position: fixed;
  pointer-events: none;
  z-index: 1000;
  opacity: 0.7;
  transform: rotate(3deg) scale(0.95);
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.15),
    0 4px 16px rgba(0, 0, 0, 0.1),
    0 0 0 1px rgba(var(--color-primary-rgb), 0.2);
  border-radius: 8px;
  background: var(--color-background);
  border: 2px solid var(--color-primary);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(2px);
  
  // Animated border effect
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, var(--color-primary), transparent, var(--color-primary));
    border-radius: 10px;
    z-index: -1;
    animation: border-glow 2s linear infinite;
  }

  // Content styling within preview
  .ProseMirror {
    padding: 12px 16px;
    font-size: 14px;
    line-height: 1.5;
    color: var(--color-text);
    
    // Limit preview content height
    max-height: 100px;
    overflow: hidden;
    
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      height: 20px;
      background: linear-gradient(transparent, var(--color-background));
    }
  }
}

@keyframes border-glow {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

// Animation keyframes
@keyframes pulse {
  0%, 100% {
    transform: translate(-50%, -50%) scale(0);
    opacity: 1;
  }
  50% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.7;
  }
}

@keyframes pulse-border {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.7;
    transform: scale(1.02);
  }
}

@keyframes shake {
  0%, 100% { transform: translateX(0); }
  25% { transform: translateX(-5px); }
  75% { transform: translateX(5px); }
}

// Success animation styles
.drop-success {
  animation: drop-success 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes drop-success {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

.drop-target-success {
  animation: target-success 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes target-success {
  0% { background-color: transparent; }
  50% { background-color: rgba(34, 197, 94, 0.1); }
  100% { background-color: transparent; }
}

// Cancel animation styles
.drag-cancelled {
  animation: drag-cancel 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes drag-cancel {
  0% { transform: translateX(0) rotate(0deg); opacity: 0.5; }
  25% { transform: translateX(-10px) rotate(-2deg); opacity: 0.7; }
  50% { transform: translateX(10px) rotate(2deg); opacity: 0.7; }
  75% { transform: translateX(-5px) rotate(-1deg); opacity: 0.8; }
  100% { transform: translateX(0) rotate(0deg); opacity: 1; }
}

// Drop target highlight
.drop-target-highlight {
  position: relative;
  
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    border: 2px solid var(--color-primary);
    border-radius: 6px;
    pointer-events: none;
    z-index: 99;
    animation: pulse-border 1s infinite;
  }
}

// Global drag state
.drag-active {
  .ProseMirror {
    cursor: grabbing;
  }
  
  // Prevent text selection during drag
  user-select: none;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
}

// Enhanced responsive design
@media (max-width: 768px) {
  .drag-handle {
    left: -1.5rem;
    width: 1.2rem;
    height: 1.2rem;
    
    // Touch-friendly sizing
    &:hover {
      transform: translateY(-50%) scale(1.1);
    }
    
    &__icon {
      svg {
        width: 10px;
        height: 10px;
      }
    }
  }
  
  .drop-zone {
    &::before {
      height: 6px;
      border-radius: 3px;
    }
    
    &--highlight::after {
      border-width: 3px;
    }
  }
  
  .drag-preview {
    transform: rotate(2deg) scale(0.9);
    max-width: calc(100vw - 32px);
    
    .ProseMirror {
      padding: 8px 12px;
      font-size: 13px;
      max-height: 80px;
    }
  }
}

// Tablet adjustments
@media (min-width: 769px) and (max-width: 1024px) {
  .drag-handle {
    left: -1.8rem;
    width: 1.4rem;
    height: 1.4rem;
  }
  
  .drag-preview {
    max-width: calc(100vw - 64px);
  }
}

// Large screen optimizations
@media (min-width: 1200px) {
  .drag-handle {
    left: -2.2rem;
    width: 1.6rem;
    height: 1.6rem;
    
    &__icon svg {
      width: 14px;
      height: 14px;
    }
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .drag-handle__icon {
    border: 2px solid currentColor;
  }
  
  .drop-zone-indicator {
    border: 2px solid var(--color-primary);
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .drag-handle,
  .drop-zone,
  .drag-preview,
  .drop-success,
  .drag-cancelled,
  .drop-target-success {
    animation: none !important;
    transition: opacity 0.2s ease !important;
  }
  
  .drag-handle {
    &:hover {
      transform: translateY(-50%);
    }
    
    &:active {
      transform: translateY(-50%);
    }
  }
  
  @keyframes pulse,
  @keyframes pulse-border,
  @keyframes shake,
  @keyframes drop-success,
  @keyframes target-success,
  @keyframes drag-cancel,
  @keyframes drop-zone-pulse,
  @keyframes dash-border,
  @keyframes border-glow {
    0%, 100% { 
      transform: none;
      opacity: 1;
    }
  }
}

// Dark mode enhancements
@media (prefers-color-scheme: dark) {
  .drag-handle__icon {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    
    &:hover {
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4);
    }
  }
  
  .drag-preview {
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.4),
      0 4px 16px rgba(0, 0, 0, 0.3),
      0 0 0 1px rgba(var(--color-primary-rgb), 0.3);
    backdrop-filter: blur(4px);
  }
}

// Print styles
@media print {
  .drag-handle,
  .drop-zone,
  .drag-preview {
    display: none !important;
  }
}