import type { RequestParams } from '@/types/request.types'
import { type ResponseData } from '@/types/response_data.types'
import api from '@/utils/api'

import type { AxiosError } from 'axios'

const favoriteApi = {
  URL: '/core/favorite',
  // 保存
  save: async <T>(params?: RequestParams): Promise<ResponseData<T>> => {
    const res = await api.post<T>(favoriteApi.URL, params).catch((err) => {
      return api.handleError(err)
    })
    return res.data as ResponseData<T>
  },
  toggle: async <T>(params: RequestParams): Promise<ResponseData<T>> => {
    const res = await api.post<T>(favoriteApi.URL + '/toggle', params).catch((err: AxiosError) => {
      return api.handleError(err)
    })
    return res.data as ResponseData<T>
  },
}

export default favoriteApi
