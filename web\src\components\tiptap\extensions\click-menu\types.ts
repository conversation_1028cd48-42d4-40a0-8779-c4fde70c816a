import type { Editor } from '@tiptap/core'
import type { Node as ProseMirrorNode } from '@tiptap/pm/model'

export interface ClickMenuOptions {
  enabled: boolean
  menuItems: ClickMenuItem[]
  positioning: 'auto' | 'left' | 'right'
  showDelay: number
  hideDelay: number
  keyboardNavigation: boolean
}

export interface ClickMenuItem {
  id: string
  label: string
  icon: string
  action: (editor: Editor, node: ProseMirrorNode, pos: number) => void
  isVisible: (node: ProseMirrorNode) => boolean
  isEnabled: (node: ProseMirrorNode) => boolean
  shortcut?: string
  group?: string
}

export interface MenuState {
  visible: boolean
  targetNode: ProseMirrorNode | null
  targetPos: number | null
  position: { x: number; y: number }
  selectedIndex: number
}

export interface ClickMenuState {
  activeMenu: MenuState | null
  menuHistory: MenuState[]
  keyboardFocus: {
    enabled: boolean
    selectedIndex: number
    navigationMode: 'mouse' | 'keyboard'
  }
}

export interface MenuItemGroup {
  id: string
  label: string
  items: ClickMenuItem[]
  priority: number
}

export interface MenuPosition {
  x: number
  y: number
  placement: 'top' | 'bottom' | 'left' | 'right'
  offset: { x: number; y: number }
}

export interface ClickMenuCommands {
  showClickMenu: (pos: number) => boolean
  hideClickMenu: () => boolean
  executeMenuItem: (itemId: string) => boolean
}

declare module '@tiptap/core' {
  interface Commands<ReturnType = any> {
    clickMenu: ClickMenuCommands
  }
}