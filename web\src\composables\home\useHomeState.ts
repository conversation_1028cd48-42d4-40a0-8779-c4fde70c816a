import { ref } from 'vue'

import { HOME_CARD } from '@/constants/storage.constants'
import type { CommentDanmakuRef } from '@/types/component-refs.types'
import localStorage from '@/utils/local-storage'
import webSocket from '@/utils/web-socket'

export function useHomeState() {
  // 显示状态控制
  const isCardVisible = ref(true)

  // 在线人数
  const onlineCount = ref<number>(0)

  // 组件引用
  const articleModalRef = ref()
  const articleListRef = ref()
  const commentDanmakuRef = ref()

  // 初始化状态
  const initializeState = () => {
    // 连接WebSocket
    webSocket.connect()

    // 从localStorage读取展示类型
    const savedCardVisibility = localStorage.get(HOME_CARD)
    if (savedCardVisibility !== null && savedCardVisibility !== undefined) {
      isCardVisible.value = Boolean(savedCardVisibility)
    }
  }

  // 切换卡片显示状态
  const toggleCardVisibility = (
    resetArticleList: () => void,
    search: () => void,
    handleDanmakuSubscription: (
      commentDanmakuRef: { value: CommentDanmakuRef },
      subscribe: boolean,
    ) => void,
  ) => {
    // 保存状态到localStorage
    localStorage.set(HOME_CARD, Boolean(isCardVisible.value))

    if (isCardVisible.value) {
      // 从评论视图切换到文章视图
      handleDanmakuSubscription(commentDanmakuRef, false)

      // 重置文章列表并重新加载数据
      resetArticleList()
      setTimeout(() => {
        search()
      }, 100)
    } else {
      // 从文章视图切换到评论视图
      handleDanmakuSubscription(commentDanmakuRef, true)
      setTimeout(() => {
        search()
      }, 100)
    }
  }

  // 重置文章列表
  const resetArticleList = () => {
    if (articleListRef.value) {
      articleListRef.value.resetList()
    }
  }

  // 打开创建文章弹框
  const openCreateArticleDialog = () => {
    articleModalRef.value.openCreateArticleDialog()
  }

  // 窗口大小调整回调
  const createResizeCallback = (
    handleDanmakuResize: (
      commentDanmakuRef: { value: CommentDanmakuRef },
      isCardVisible: boolean,
    ) => void,
  ) => {
    return () => {
      handleDanmakuResize(commentDanmakuRef, isCardVisible.value)
    }
  }

  // 清理函数
  const cleanup = (resizeCallback: () => void) => {
    window.removeEventListener('resize', resizeCallback)
  }

  return {
    // 状态
    isCardVisible,
    onlineCount,

    // 组件引用
    articleModalRef,
    articleListRef,
    commentDanmakuRef,

    // 方法
    initializeState,
    toggleCardVisibility,
    resetArticleList,
    openCreateArticleDialog,
    createResizeCallback,
    cleanup,
  }
}
