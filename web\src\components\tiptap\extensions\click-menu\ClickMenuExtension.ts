import { Extension } from '@tiptap/core'
import { Plugin, Plugin<PERSON>ey } from '@tiptap/pm/state'
import { ClickMenuView } from './ClickMenuView'

export interface ClickMenuOptions {
  enabled?: boolean
}

export const ClickMenuExtension = Extension.create<ClickMenuOptions>({
  name: 'clickMenu',

  addOptions() {
    return {
      enabled: true,
    }
  },

  addProseMirrorPlugins() {
    if (!this.options.enabled) return []

    const view = new ClickMenuView({
      editor: this.editor,
    })

    return [
      new Plugin({
        key: new PluginKey(`${this.name}-click-menu`),
        view: () => ({
          destroy: () => view.destroy()
        }),
        props: {
          handleDOMEvents: view.events()
        },
      }),
    ]
  },
})